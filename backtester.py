import pandas as pd
import numpy as np
import os
from datetime import datetime
import traceback
from hyperliquid_api import fetch_historical_data
from strategies import STRATEGY_PARAMS
from typing import Dict, Any

# --- Helper Functions ---

def calculate_sma(prices: pd.Series, window: int) -> pd.Series:
    """Calculates Simple Moving Average using pandas."""
    if len(prices) < window:
        # Return a series of NaNs with the same index if not enough data
        return pd.Series([float('nan')] * len(prices), index=prices.index)
    return prices.rolling(window=window).mean()

def _simulate_pine_script_strategy(data: pd.DataFrame, strategy_id: str, pine_script_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enhanced implementation to simulate a Pine Script strategy.
    This implementation parses the Pine Script file for key indicators and parameters,
    and applies a strategy based on the detected pattern.

    In a real implementation, you would need a proper Pine Script parser/executor.
    """
    print(f"Simulating Pine Script strategy: {strategy_id} from {pine_script_path}")

    try:
        # Read the Pine Script file
        with open(pine_script_path, 'r') as f:
            pine_script = f.read()

        # Extract strategy name from Pine Script
        import re
        strategy_name = strategy_id.replace('_', ' ').title()
        name_match = re.search(r'strategy\s*\(\s*"([^"]+)"', pine_script)
        if name_match:
            strategy_name = name_match.group(1)

        # Create a copy of the data to work with
        results_df = data.copy()

        # Initialize signal column
        results_df['signal'] = 0

        # Determine strategy type and extract parameters
        strategy_type = "unknown"
        strategy_params = {}

        # Check for RSI strategy
        if 'rsi' in pine_script.lower():
            strategy_type = "rsi"
            print(f"Detected RSI-based strategy: {strategy_name}")

            # Extract parameters with more robust regex patterns
            rsi_period = 14  # Default
            oversold = 30    # Default
            overbought = 70  # Default

            # Look for RSI period in different formats
            rsi_period_patterns = [
                r'rsi\s*\(\s*close\s*,\s*(\d+)\s*\)',  # rsi(close, 14)
                r'rsi_period\s*=\s*(\d+)',              # rsi_period = 14
                r'rsi\s*\(\s*(\d+)\s*\)'                # rsi(14)
            ]

            for pattern in rsi_period_patterns:
                match = re.search(pattern, pine_script)
                if match:
                    rsi_period = int(match.group(1))
                    break

            # Look for oversold level
            oversold_patterns = [
                r'oversold\s*=\s*(\d+)',                # oversold = 30
                r'oversold_level\s*=\s*(\d+)',         # oversold_level = 30
                r'oversold\s*:\s*(\d+)'                # oversold: 30
            ]

            for pattern in oversold_patterns:
                match = re.search(pattern, pine_script)
                if match:
                    oversold = int(match.group(1))
                    break

            # Look for overbought level
            overbought_patterns = [
                r'overbought\s*=\s*(\d+)',              # overbought = 70
                r'overbought_level\s*=\s*(\d+)',       # overbought_level = 70
                r'overbought\s*:\s*(\d+)'              # overbought: 70
            ]

            for pattern in overbought_patterns:
                match = re.search(pattern, pine_script)
                if match:
                    overbought = int(match.group(1))
                    break

            strategy_params = {
                'rsi_period': rsi_period,
                'oversold': oversold,
                'overbought': overbought
            }

            print(f"Using RSI parameters: period={rsi_period}, oversold={oversold}, overbought={overbought}")

            # Check if we already have RSI calculated in the data
            if 'rsi_14' in results_df.columns and rsi_period == 14:
                print("Using pre-calculated RSI-14 from data")
                results_df['rsi'] = results_df['rsi_14']
            else:
                # Calculate RSI
                print("Calculating RSI with period", rsi_period)
                delta = results_df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
                rs = gain / loss
                results_df['rsi'] = 100 - (100 / (1 + rs))

            # Generate signals
            results_df.loc[results_df['rsi'] < oversold, 'signal'] = 1  # Buy signal
            results_df.loc[results_df['rsi'] > overbought, 'signal'] = -1  # Sell signal

        # Check for Moving Average Crossover strategy
        elif any(term in pine_script.lower() for term in ['sma', 'ema', 'ma', 'moving average', 'crossover', 'cross']):
            strategy_type = "ma_cross"
            print(f"Detected Moving Average Crossover strategy: {strategy_name}")

            # Determine if using SMA or EMA
            using_ema = 'ema' in pine_script.lower()
            ma_type = "EMA" if using_ema else "SMA"

            # Extract parameters with more robust regex patterns
            short_window = 10  # Default
            long_window = 20   # Default

            # Look for short period in different formats
            short_period_patterns = [
                r'short\w*\s*=\s*(\d+)',                # shortPeriod = 10
                r'fast\w*\s*=\s*(\d+)',                 # fastPeriod = 10
                r'short\w*\s*:\s*(\d+)'                 # shortPeriod: 10
            ]

            for pattern in short_period_patterns:
                match = re.search(pattern, pine_script)
                if match:
                    short_window = int(match.group(1))
                    break

            # Look for long period
            long_period_patterns = [
                r'long\w*\s*=\s*(\d+)',                 # longPeriod = 20
                r'slow\w*\s*=\s*(\d+)',                 # slowPeriod = 20
                r'long\w*\s*:\s*(\d+)'                  # longPeriod: 20
            ]

            for pattern in long_period_patterns:
                match = re.search(pattern, pine_script)
                if match:
                    long_window = int(match.group(1))
                    break

            strategy_params = {
                'ma_type': ma_type,
                'short_window': short_window,
                'long_window': long_window
            }

            print(f"Using {ma_type} parameters: short={short_window}, long={long_window}")

            # Check if we already have the MAs calculated in the data
            short_col = f"{'ema' if using_ema else 'sma'}_{short_window}"
            long_col = f"{'ema' if using_ema else 'sma'}_{long_window}"

            if short_col in results_df.columns and long_col in results_df.columns:
                print(f"Using pre-calculated {short_col} and {long_col} from data")
                results_df['short_ma'] = results_df[short_col]
                results_df['long_ma'] = results_df[long_col]
            else:
                # Calculate Moving Averages
                print(f"Calculating {ma_type} with periods {short_window} and {long_window}")
                if using_ema:
                    results_df['short_ma'] = results_df['close'].ewm(span=short_window, adjust=False).mean()
                    results_df['long_ma'] = results_df['close'].ewm(span=long_window, adjust=False).mean()
                else:
                    results_df['short_ma'] = results_df['close'].rolling(window=short_window).mean()
                    results_df['long_ma'] = results_df['close'].rolling(window=long_window).mean()

            # Generate signals
            results_df.loc[results_df['short_ma'] > results_df['long_ma'], 'signal'] = 1  # Buy signal
            results_df.loc[results_df['short_ma'] < results_df['long_ma'], 'signal'] = -1  # Sell signal

        # Check for MACD strategy
        elif 'macd' in pine_script.lower():
            strategy_type = "macd"
            print(f"Detected MACD strategy: {strategy_name}")

            # Default MACD parameters
            fast_period = 12
            slow_period = 26
            signal_period = 9

            # Extract parameters if available
            fast_pattern = re.search(r'fast\w*\s*=\s*(\d+)', pine_script)
            if fast_pattern:
                fast_period = int(fast_pattern.group(1))

            slow_pattern = re.search(r'slow\w*\s*=\s*(\d+)', pine_script)
            if slow_pattern:
                slow_period = int(slow_pattern.group(1))

            signal_pattern = re.search(r'signal\w*\s*=\s*(\d+)', pine_script)
            if signal_pattern:
                signal_period = int(signal_pattern.group(1))

            strategy_params = {
                'fast_period': fast_period,
                'slow_period': slow_period,
                'signal_period': signal_period
            }

            print(f"Using MACD parameters: fast={fast_period}, slow={slow_period}, signal={signal_period}")

            # Check if we already have MACD calculated
            if 'macd_line' in results_df.columns and 'macd_signal' in results_df.columns:
                print("Using pre-calculated MACD from data")
            else:
                # Calculate MACD
                print("Calculating MACD")
                results_df['macd_fast'] = results_df['close'].ewm(span=fast_period, adjust=False).mean()
                results_df['macd_slow'] = results_df['close'].ewm(span=slow_period, adjust=False).mean()
                results_df['macd_line'] = results_df['macd_fast'] - results_df['macd_slow']
                results_df['macd_signal'] = results_df['macd_line'].ewm(span=signal_period, adjust=False).mean()
                results_df['macd_histogram'] = results_df['macd_line'] - results_df['macd_signal']

            # Generate signals
            results_df.loc[results_df['macd_line'] > results_df['macd_signal'], 'signal'] = 1  # Buy signal
            results_df.loc[results_df['macd_line'] < results_df['macd_signal'], 'signal'] = -1  # Sell signal

        # Default to a simple moving average strategy if no specific pattern is detected
        else:
            strategy_type = "default_sma"
            print(f"No specific strategy pattern detected in Pine Script, using default SMA crossover for {strategy_name}")

            # Default parameters
            short_window = 10
            long_window = 20

            strategy_params = {
                'ma_type': 'SMA',
                'short_window': short_window,
                'long_window': long_window
            }

            # Check if we already have SMAs calculated
            if f'sma_{short_window}' in results_df.columns and f'sma_{long_window}' in results_df.columns:
                print(f"Using pre-calculated SMA-{short_window} and SMA-{long_window} from data")
                results_df['short_ma'] = results_df[f'sma_{short_window}']
                results_df['long_ma'] = results_df[f'sma_{long_window}']
            else:
                # Calculate SMAs
                print(f"Calculating default SMAs with periods {short_window} and {long_window}")
                results_df['short_ma'] = results_df['close'].rolling(window=short_window).mean()
                results_df['long_ma'] = results_df['close'].rolling(window=long_window).mean()

            # Generate signals
            results_df.loc[results_df['short_ma'] > results_df['long_ma'], 'signal'] = 1  # Buy signal
            results_df.loc[results_df['short_ma'] < results_df['long_ma'], 'signal'] = -1  # Sell signal

        # Remove NaN values
        results_df = results_df.dropna(subset=['signal'])

        # Calculate positions and PnL
        results_df['position'] = results_df['signal'].shift(1).fillna(0)
        results_df['returns'] = results_df['close'].pct_change().fillna(0)
        results_df['strategy_returns'] = results_df['position'] * results_df['returns']
        results_df['cumulative_returns'] = (1 + results_df['strategy_returns']).cumprod()

        # Calculate performance statistics
        if 'cumulative_returns' not in results_df or results_df['cumulative_returns'].isna().all():
            total_pnl_pct = 0.0
        else:
            total_pnl_pct = (results_df['cumulative_returns'].fillna(1).iloc[-1] - 1) * 100

        # Identify trades
        results_df['prev_position'] = results_df['position'].shift(1).fillna(0)
        trade_signals = results_df[results_df['position'] != results_df['prev_position']].copy()

        # Extract trade information
        trades = []
        current_position = 0
        entry_price = 0
        entry_time = None

        for idx, row in trade_signals.iterrows():
            if row['position'] != 0 and current_position == 0:  # Entry
                current_position = row['position']
                entry_price = row['close']
                entry_time = idx
            elif (row['position'] == 0 or row['position'] != current_position) and current_position != 0:  # Exit
                exit_price = row['close']
                pnl = (exit_price - entry_price) / entry_price * 100 * current_position
                trades.append({
                    'entry_timestamp': entry_time.timestamp() * 1000,  # Convert to ms for JS
                    'exit_timestamp': idx.timestamp() * 1000,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'position': 'Long' if current_position > 0 else 'Short',
                    'pnl': round(pnl, 2),
                    'pnl_percentage': round(pnl, 2)
                })
                current_position = row['position']  # Update for potential new position
                if current_position != 0:  # If this is also an entry
                    entry_price = row['close']
                    entry_time = idx

        # Calculate trade statistics
        trade_returns = [trade['pnl'] for trade in trades]
        num_trades = len(trade_returns)
        winning_trades = sum(1 for pnl in trade_returns if pnl > 0)
        losing_trades = sum(1 for pnl in trade_returns if pnl < 0)

        total_win_pnl = sum(pnl for pnl in trade_returns if pnl > 0)
        total_loss_pnl = sum(pnl for pnl in trade_returns if pnl < 0)

        win_ratio_pct = (winning_trades / num_trades * 100) if num_trades > 0 else 0.0
        average_win_pnl = (total_win_pnl / winning_trades) if winning_trades > 0 else 0.0
        average_loss_pnl = (total_loss_pnl / losing_trades) if losing_trades > 0 else 0.0

        # Calculate Profit Factor
        if total_loss_pnl != 0:
            profit_factor = abs(total_win_pnl / total_loss_pnl)
        elif total_win_pnl > 0:
            profit_factor = float('inf')
        else:
            profit_factor = None

        # Extract signals for chart display
        signals = []
        for idx, row in results_df.iterrows():
            if row['signal'] != 0:
                signals.append({
                    'timestamp': idx.timestamp() * 1000,  # Convert to ms for JS
                    'price': row['close'],
                    'type': 'buy' if row['signal'] > 0 else 'sell'
                })

        # Prepare results
        stats = {
            'strategy_name': strategy_name,
            'strategy_type': strategy_type,
            'strategy_params': strategy_params,
            'total_pnl': round(total_pnl_pct, 2),
            'number_of_trades': num_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_ratio': round(win_ratio_pct, 2),
            'average_win_pnl': round(average_win_pnl, 4),
            'average_loss_pnl': round(average_loss_pnl, 4),
            'profit_factor': round(profit_factor, 2) if profit_factor is not None and profit_factor != float('inf') else profit_factor,
            'trades': trades,
            'signals': signals
        }

        # Convert DataFrame to dict for JSON serialization if needed
        # stats['data'] = results_df.reset_index().to_dict('records')

        return stats

    except Exception as e:
        error_msg = f"Error simulating Pine Script strategy: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return {'error': error_msg, 'traceback': traceback.format_exc()}

# --- Core Backtesting Functions ---

def simulate_strategy(pair: str, strategy_id: str, start_date_str: str, end_date_str: str, strategy_params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Wrapper function to handle the parameters from app.py and call the actual simulation function.
    This function fetches historical data and then calls _simulate_strategy with the data.
    """
    try:
        # --- Convert date strings to millisecond timestamps ---
        try:
            # Assume YYYY-MM-DD format, set time to start/end of day UTC
            start_dt = datetime.strptime(start_date_str, '%Y-%m-%d')
            # For end_date, include the whole day by setting time to 23:59:59.999
            end_dt = datetime.strptime(end_date_str, '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=999999)

            # Convert to UTC timestamp in milliseconds
            start_time_ms = int(start_dt.timestamp() * 1000)
            end_time_ms = int(end_dt.timestamp() * 1000)

            if start_time_ms >= end_time_ms:
                return {'error': 'Start date must be before end date.'}

        except ValueError:
            return {'error': "Invalid date format. Please use 'YYYY-MM-DD'."}

        # --- Fetch historical data ---
        print(f"Fetching data for {pair} from {start_date_str} ({start_time_ms}) to {end_date_str} ({end_time_ms})")
        data = fetch_historical_data(pair, start_time_ms, end_time_ms)
        if data is None:  # Check for None explicitly (error during fetch)
            # Error message should be printed by fetch_historical_data
            return {'error': f'Failed to fetch historical data for {pair}. Check API logs.'}
        if data.empty:  # Check for empty DataFrame (no data found for range/interval)
            return {'error': f'No historical data found for {pair} in the specified date range.'}

        # Use default parameters if none provided
        if strategy_params is None:
            strategy_params = STRATEGY_PARAMS.get(strategy_id, {})

        # Run simulation with the actual implementation
        result = _simulate_strategy(data, strategy_id, strategy_params)

        # Add historical data to the result for charting
        if 'error' not in result:
            # Convert DataFrame to records for JSON serialization
            result['historical_data'] = data.reset_index().to_dict('records')

        return result
    except Exception as e:
        return {
            'error': f"{type(e).__name__}: {str(e)}",
            'traceback': traceback.format_exc()
        }

def run_backtest(pair: str, strategy_id: str, start_date: str, end_date: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Run a backtest for a given strategy and parameters.
    start_date and end_date should be strings in 'YYYY-MM-DD' format.
    """
    try:
        # --- Convert date strings to millisecond timestamps ---
        try:
            # Assume YYYY-MM-DD format, set time to start/end of day UTC
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            # For end_date, include the whole day by setting time to 23:59:59.999
            end_dt = datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=999999)

            # Convert to UTC timestamp in milliseconds
            start_time_ms = int(start_dt.timestamp() * 1000)
            end_time_ms = int(end_dt.timestamp() * 1000)

            if start_time_ms >= end_time_ms:
                 return {'success': False, 'error': 'Start date must be before end date.'}

        except ValueError:
            return {'success': False, 'error': "Invalid date format. Please use 'YYYY-MM-DD'."}

        # --- Fetch historical data ---
        print(f"Fetching data for {pair} from {start_date} ({start_time_ms}) to {end_date} ({end_time_ms})")
        data = fetch_historical_data(pair, start_time_ms, end_time_ms)
        if data is None: # Check for None explicitly (error during fetch)
             # Error message should be printed by fetch_historical_data
             return {'success': False, 'error': f'Failed to fetch historical data for {pair}. Check API logs.'}
        if data.empty: # Check for empty DataFrame (no data found for range/interval)
            return {'success': False, 'error': f'No historical data found for {pair} in the specified date range.'}

        # Use default parameters if none provided
        if params is None:
            params = STRATEGY_PARAMS.get(strategy_id, {})

        # Run simulation
        result = simulate_strategy(data, strategy_id, params)

        return {
            'success': True,
            'result': result
        }
    except Exception as e:
        return {
            'success': False,
            'error': f"{type(e).__name__}: {str(e)}",
            'traceback': traceback.format_exc()
        }

def _simulate_strategy(data: pd.DataFrame, strategy_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Simulate a trading strategy on historical data.
    Supports both built-in Python strategies and dynamically loaded strategies.
    Internal implementation - use simulate_strategy() instead for the public API.
    """
    print(f"Attempting to simulate strategy with ID: {strategy_id}") # Debug log
    try:
        # --- Select Python Strategy Function ---
        # Map known Python strategy IDs to their functions
        known_python_strategies = {}

        # First check if it's a built-in strategy
        if strategy_id in known_python_strategies:
            strategy_func = known_python_strategies[strategy_id]
            print(f"Selected built-in Python strategy function: {strategy_func.__name__}")
        # If not a built-in strategy, try to dynamically import it from strategies.py
        elif strategy_id in STRATEGY_PARAMS:
            # Dynamically import the strategy function
            print(f"Attempting to dynamically import strategy: {strategy_id}")
            try:
                # Import the strategy module
                import strategies
                # Get the strategy function dynamically
                if hasattr(strategies, strategy_id):
                    strategy_func = getattr(strategies, strategy_id)
                    print(f"Successfully imported strategy function: {strategy_id}")
                else:
                    return {'error': f"Strategy function '{strategy_id}' not found in strategies module."}
            except Exception as e:
                print(f"Error importing strategy: {e}")
                return {'error': f"Failed to import strategy '{strategy_id}': {str(e)}"}
        else:
            # Check if it might be a Pine Script file (based on listing logic in app.py)
            # This assumes Pine strategies don't share IDs with Python ones.
            # Check if it's a pine_ prefixed strategy
            if strategy_id.startswith('pine_'):
                strategy_name = strategy_id[5:]  # Remove 'pine_' prefix
                pine_strategy_path = os.path.join(os.path.dirname(__file__), 'strategies', 'pine_examples', f"{strategy_name}.pine")
                if os.path.exists(pine_strategy_path):
                    print(f"Found Pine Script strategy: {strategy_id} at {pine_strategy_path}")
                    # Use a simple implementation for Pine Script strategies
                    return _simulate_pine_script_strategy(data, strategy_id, pine_strategy_path, params)

            # Check for legacy path (without pine_examples subdirectory)
            pine_strategy_path = os.path.join(os.path.dirname(__file__), 'strategies', f"{strategy_id}.pine")
            if os.path.exists(pine_strategy_path):
                print(f"Found Pine Script strategy: {strategy_id} at {pine_strategy_path}")
                # Use a simple implementation for Pine Script strategies
                return _simulate_pine_script_strategy(data, strategy_id, pine_strategy_path, params)
            else:
                 # It's neither a known Python strategy nor an existing .pine file
                 error_msg = f"Unknown or unsupported strategy ID: '{strategy_id}'. Strategy not found in built-in strategies, STRATEGY_PARAMS, or Pine Script files."
                 print(error_msg)
                 return {'error': error_msg, 'status_code': 404} # 404 Not Found

        # --- Run Selected Python Strategy ---
        print(f"Running strategy '{strategy_id}' with params: {params}")
        # Run strategy function - it might return a modified DataFrame (e.g., after dropna)
        # Check if the strategy function accepts params as a dictionary or as individual arguments
        import inspect
        sig = inspect.signature(strategy_func)

        if len(sig.parameters) == 1:  # Only accepts data
            strategy_data = strategy_func(data.copy())
        elif 'params' in sig.parameters:  # Accepts data and params as a dictionary
            strategy_data = strategy_func(data.copy(), params)
        else:  # Accepts data and individual parameters
            # Pass parameters individually based on their names in the function signature
            # Filter params to only include those that are in the function signature
            filtered_params = {k: v for k, v in params.items() if k in sig.parameters}
            strategy_data = strategy_func(data.copy(), **filtered_params)

        # Check if the strategy function returned a tuple of buy_signals, sell_signals
        # or a DataFrame with a 'signal' column
        if isinstance(strategy_data, tuple) and len(strategy_data) == 2:
            # Unpack the tuple
            buy_signals, sell_signals = strategy_data

            # Create a copy of the original data
            results_df = data.copy()

            # Convert buy and sell signals to position signals (1 for buy, -1 for sell, 0 for hold)
            results_df['signal'] = 0
            results_df.loc[buy_signals, 'signal'] = 1
            results_df.loc[sell_signals, 'signal'] = -1
        elif hasattr(strategy_data, 'copy'):
            # Explicitly copy the result from the strategy function before adding new columns
            # This avoids SettingWithCopyWarning
            results_df = strategy_data.copy()
        else:
            # If strategy_data is not a tuple or DataFrame, return an error
            error_msg = f"Strategy function returned an invalid type: {type(strategy_data)}. Expected a tuple of (buy_signals, sell_signals) or a DataFrame with a 'signal' column."
            print(error_msg)
            return {'error': error_msg, 'status_code': 500}

        # Calculate positions and PnL on the copied DataFrame
        results_df['position'] = results_df['signal'].shift(1)
        results_df['returns'] = results_df['close'].pct_change()
        results_df['strategy_returns'] = results_df['position'] * results_df['returns']
        results_df['cumulative_returns'] = (1 + results_df['strategy_returns']).cumprod()

        # --- Calculate Performance Statistics ---

        # Ensure 'cumulative_returns' exists and is not all NaN before accessing iloc[-1]
        if 'cumulative_returns' not in results_df or results_df['cumulative_returns'].isna().all():
             total_pnl_pct = 0.0
        else:
             # Use .fillna(1) in case the first value is NaN after cumprod
             total_pnl_pct = (results_df['cumulative_returns'].fillna(1).iloc[-1] - 1) * 100

        # Identify actual trades (where position changes, excluding initial state)
        # A trade occurs when the position flips (e.g., 0 to 1, 1 to -1, -1 to 0, etc.)
        # We need the return *during* the holding period of the position that just closed or changed.
        results_df['prev_position'] = results_df['position'].shift(1)
        trade_signals = results_df[results_df['position'] != results_df['prev_position']].copy() # Rows where position changes

        # Calculate PnL for each period the position was held
        # This requires iterating or using more complex pandas logic.
        # Simpler approach: Analyze returns on rows where a trade signal occurred.
        # Note: This simplified approach might not perfectly capture PnL for multi-period holds.
        # A more accurate method involves tracking entry/exit prices.
        # Let's stick to the current simple return calculation for now, but calculate more stats from it.

        trade_returns = trade_signals['strategy_returns'].dropna() # Returns on the day the signal changes position

        num_trades = len(trade_returns) # Number of periods with a position change and valid return
        winning_trades = int((trade_returns > 0).sum()) # Using int() for JSON compatibility
        losing_trades = int((trade_returns < 0).sum()) # Using int()

        total_win_pnl = trade_returns[trade_returns > 0].sum()
        total_loss_pnl = trade_returns[trade_returns < 0].sum() # This will be negative

        win_ratio_pct = (winning_trades / num_trades * 100) if num_trades > 0 else 0.0
        average_win_pnl = (total_win_pnl / winning_trades) if winning_trades > 0 else 0.0
        average_loss_pnl = (total_loss_pnl / losing_trades) if losing_trades > 0 else 0.0 # Will be negative

        # Calculate Profit Factor
        if total_loss_pnl != 0:
            profit_factor = abs(total_win_pnl / total_loss_pnl) # abs because total_loss_pnl is negative
        elif total_win_pnl > 0:
            profit_factor = float('inf') # Only wins
        else:
            profit_factor = None # No wins or losses

        # Round for cleaner output
        stats = {
            'total_pnl_pct': round(total_pnl_pct, 2),
            'number_of_trades': num_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_ratio_pct': round(win_ratio_pct, 2),
            'average_win_pnl': round(average_win_pnl, 4),
            'average_loss_pnl': round(average_loss_pnl, 4),
            'profit_factor': round(profit_factor, 2) if profit_factor is not None and profit_factor != float('inf') else profit_factor,
            # Return the DataFrame with results and calculations
            'data_df': results_df # Returning DataFrame might be more useful than dict
            # 'data': results_df.to_dict('records') # Or keep returning dict if needed by frontend
        }
        # Convert final DataFrame to dict right before returning if necessary
        stats['data'] = results_df.reset_index().to_dict('records') # Reset index to include timestamp
        del stats['data_df'] # Remove the DataFrame object before returning JSON-like dict

        # Generate signals for the frontend
        signals = []

        # Find buy signals (where signal changes from 0 or -1 to 1)
        buy_signals = results_df[(results_df['signal'] == 1) &
                               ((results_df['signal'].shift(1) == 0) |
                                (results_df['signal'].shift(1) == -1))]

        # Find sell signals (where signal changes from 1 to -1 or 0)
        sell_signals = results_df[(results_df['signal'] == -1) &
                                (results_df['signal'].shift(1) == 1)]

        # Add buy signals
        for idx, row in buy_signals.iterrows():
            signals.append({
                'timestamp': int(idx.timestamp() * 1000),  # Convert to milliseconds
                'price': row['close'],
                'type': 'buy'
            })

        # Add sell signals
        for idx, row in sell_signals.iterrows():
            signals.append({
                'timestamp': int(idx.timestamp() * 1000),  # Convert to milliseconds
                'price': row['close'],
                'type': 'sell'
            })

        # Add signals to the stats
        stats['signals'] = signals

        return stats

    except KeyError as ke:
         # Catch specific errors like missing 'signal' column from strategy
         error_msg = f"Missing expected column: {str(ke)}. Strategy '{strategy_id}' might not be producing the 'signal' column."
         return {'error': f"KeyError: {error_msg}", 'traceback': traceback.format_exc()}
    except Exception as e:
        # General exception handling
        return {'error': f"{type(e).__name__}: {str(e)}", 'traceback': traceback.format_exc()}

# Example usage (if run directly)
if __name__ == '__main__':
    print("--- Running Backtester Module Directly (Example Usage) ---")
    # This requires hyperliquid_api.py to be in the python path
    # This example uses mock data for demonstration.
    # Replace with actual data fetching for real backtests.

    # Mock data for demonstration (needs enough points for indicators):
    timestamps = pd.date_range(start='2023-01-01', periods=50, freq='H')
    mock_data_dict = {
        'timestamp': timestamps.astype(np.int64) // 10**6, # Milliseconds
        'open': np.random.uniform(20000, 21000, 50),
        'high': np.random.uniform(20500, 21500, 50),
        'low': np.random.uniform(19500, 20500, 50),
        'close': np.random.uniform(20000, 21000, 50),
        'volume': np.random.uniform(100, 500, 50)
    }
    mock_df = pd.DataFrame(mock_data_dict)
    # Ensure 'high' is >= 'open'/'close' and 'low' <= 'open'/'close'
    mock_df['high'] = mock_df[['high', 'open', 'close']].max(axis=1)
    mock_df['low'] = mock_df[['low', 'open', 'close']].min(axis=1)
    mock_df['timestamp'] = pd.to_datetime(mock_df['timestamp'], unit='ms')
    mock_df = mock_df.set_index('timestamp') # Strategies likely expect a DatetimeIndex

    print(f"\nUsing mock data with {len(mock_df)} rows.")
    print("Mock Data Head:\n", mock_df.head())

    # Example call using mock data:
    # Note: Strategy functions (simple_sma_cross, rsi_strategy) must exist
    # and be correctly imported for this to work.
    # We'll use default parameters defined in STRATEGY_PARAMS.
    strategy_to_test = 'simple_sma_cross'
    # strategy_to_test = 'rsi_strategy' # Uncomment to test RSI

    # Get default params for the chosen strategy
    default_params = STRATEGY_PARAMS.get(strategy_to_test, {})
    print(f"\nRunning mock simulation for strategy: '{strategy_to_test}' with params: {default_params}")

    try:
        # Assuming strategy functions are available in the global scope
        # or correctly imported via tradingbot.strategies
        results_mock = _simulate_strategy(mock_df.copy(), strategy_to_test, default_params) # Pass DataFrame, strategy_id, params

        print("\n--- Mock Simulation Results ---")
        import json
        # Avoid printing the full data dict, just show stats
        if 'result' in results_mock and 'data' in results_mock['result']:
             del results_mock['result']['data'] # Remove large data part for cleaner output
        elif 'data' in results_mock:
             del results_mock['data'] # Remove if directly in results_mock (e.g., on error)

        print(json.dumps(results_mock, indent=4))

    except NameError as ne:
         print(f"\nError: Strategy function for '{strategy_to_test}' not found or not imported correctly.")
         print(f"Details: {ne}")
         print("Please ensure the strategy function is defined and imported in backtester.py or tradingbot/strategies/__init__.py")
    except Exception as e:
        print(f"\nAn unexpected error occurred during mock simulation:")
        print(f"{type(e).__name__}: {e}")
        traceback.print_exc()

    print("\n--- Backtester Module Direct Execution Finished ---")
