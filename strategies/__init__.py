"""
Trading strategies module.
"""
import pandas as pd
import numpy as np

# Dictionary to store strategy parameters
STRATEGY_PARAMS = {}

# Dictionary to store strategy descriptions
STRATEGY_DESCRIPTIONS = {}

STRATEGY_PARAMS['pino'] = {
    'use_sma': True,
    'use_rsi': True,
    'use_macd': False,
    'use_bollinger': False,
    'use_stochastic': False,
    'use_volume': False,
    'weight_sma_buy': 1.0,
    'weight_sma_sell': 1.0,
    'weight_rsi_buy': 1.0,
    'weight_rsi_sell': 1.0,
    'weight_macd_buy': 1.0,
    'weight_macd_sell': 1.0,
    'weight_bollinger_buy': 1.0,
    'weight_bollinger_sell': 1.0,
    'sma_depends_on_volume': 0.0,
    'rsi_depends_on_sma': 0.0,
    'macd_depends_on_rsi': 0.0,
    'bollinger_depends_on_volume': 0.0,
    'threshold_buy': 0.3,
    'threshold_sell': 0.3,
    'take_profit': 5.0,
    'stop_loss': 2.0,
}
STRATEGY_DESCRIPTIONS['pino'] = 'Pinowtete'
