import pandas as pd
import numpy as np
from typing import Dict, Any, Tu<PERSON>, List

# Strategy parameters
STRATEGY_PARAMS = {}

# Strategy descriptions
STRATEGY_DESCRIPTIONS = {}

def basic_strategy(data, param1=10, param2=20):
    """Basic Strategy Template - Buy when price increases by param1/1000, sell when it decreases by param2/1000"""
    import pandas as pd
    import numpy as np

    # Initialize signals
    buy_signals = pd.Series(False, index=data.index)
    sell_signals = pd.Series(False, index=data.index)

    # Generate signals
    for i in range(1, len(data)):
        # Example buy condition
        if data['close'].iloc[i] > data['close'].iloc[i-1] * (1 + param1/1000):
            buy_signals.iloc[i] = True

        # Example sell condition
        elif data['close'].iloc[i] < data['close'].iloc[i-1] * (1 - param2/1000):
            sell_signals.iloc[i] = True

    return buy_signals, sell_signals

def master_strategy(data,
                  # Strategy selection
                  use_sma=True, use_rsi=True, use_macd=True, use_bollinger=True,
                  use_stochastic=False, use_volume=False, use_pattern_recognition=False, use_support_resistance=False,

                  # Strategy weights
                  weight_sma_buy=1.0, weight_sma_sell=1.0,
                  weight_rsi_buy=1.0, weight_rsi_sell=1.0,
                  weight_macd_buy=1.0, weight_macd_sell=1.0,
                  weight_bollinger_buy=1.0, weight_bollinger_sell=1.0,
                  weight_stochastic_buy=1.0, weight_stochastic_sell=1.0,
                  weight_volume_buy=1.0, weight_volume_sell=1.0,
                  weight_pattern_buy=1.0, weight_pattern_sell=1.0,
                  weight_support_resistance_buy=1.0, weight_support_resistance_sell=1.0,

                  # Signal thresholds
                  threshold_buy=0.3, threshold_sell=0.3,

                  # Strategy dependencies (0-1 values, how much one strategy depends on another)
                  sma_depends_on_volume=0.0,  # If volume is high, SMA signals are more reliable
                  rsi_depends_on_sma=0.0,     # RSI signals are stronger when confirmed by SMA
                  macd_depends_on_rsi=0.0,    # MACD signals are stronger when confirmed by RSI
                  bollinger_depends_on_volume=0.0,  # Bollinger signals are stronger with high volume

                  # SMA parameters
                  sma_short_window=10, sma_long_window=20,

                  # RSI parameters
                  rsi_period=14, rsi_oversold=30, rsi_overbought=70,

                  # MACD parameters
                  macd_fast_period=12, macd_slow_period=26, macd_signal_period=9,

                  # Bollinger Bands parameters
                  bollinger_window=20, bollinger_num_std=2,

                  # Stochastic parameters
                  stochastic_k_period=14, stochastic_d_period=3, stochastic_slowing=3,
                  stochastic_oversold=20, stochastic_overbought=80,

                  # Volume parameters
                  volume_window=20, volume_threshold=1.5,

                  # Pattern recognition parameters
                  pattern_window=5,

                  # Support/Resistance parameters
                  support_resistance_window=50, support_resistance_threshold=0.02,

                  # Risk management
                  take_profit=5.0, stop_loss=2.0,

                  # Timeframe adjustment (for 5-minute chart optimization)
                  timeframe_multiplier=1.0):
    """Advanced Master Strategy that combines multiple technical indicators with configurable weights and dependencies.

    This strategy combines all available trading strategies with customizable weights
    for both buy and sell signals. You can enable/disable each strategy, adjust its importance,
    and set dependencies between strategies for more sophisticated signal generation.

    Parameters:
    # Strategy selection
    - use_sma: Whether to use Simple Moving Average strategy
    - use_rsi: Whether to use RSI strategy
    - use_macd: Whether to use MACD strategy
    - use_bollinger: Whether to use Bollinger Bands strategy
    - use_stochastic: Whether to use Stochastic Oscillator
    - use_volume: Whether to use Volume analysis
    - use_pattern_recognition: Whether to use Pattern Recognition
    - use_support_resistance: Whether to use Support/Resistance levels

    # Strategy weights
    - weight_sma_buy: Weight for SMA buy signals
    - weight_sma_sell: Weight for SMA sell signals
    - weight_rsi_buy: Weight for RSI buy signals
    - weight_rsi_sell: Weight for RSI sell signals
    - weight_macd_buy: Weight for MACD buy signals
    - weight_macd_sell: Weight for MACD sell signals
    - weight_bollinger_buy: Weight for Bollinger buy signals
    - weight_bollinger_sell: Weight for Bollinger sell signals
    - weight_stochastic_buy: Weight for Stochastic buy signals
    - weight_stochastic_sell: Weight for Stochastic sell signals
    - weight_volume_buy: Weight for Volume buy signals
    - weight_volume_sell: Weight for Volume sell signals
    - weight_pattern_buy: Weight for Pattern Recognition buy signals
    - weight_pattern_sell: Weight for Pattern Recognition sell signals
    - weight_support_resistance_buy: Weight for Support/Resistance buy signals
    - weight_support_resistance_sell: Weight for Support/Resistance sell signals

    # Signal thresholds
    - threshold_buy: Threshold for combined buy signals
    - threshold_sell: Threshold for combined sell signals

    # Strategy dependencies
    - sma_depends_on_volume: How much SMA signals depend on volume confirmation
    - rsi_depends_on_sma: How much RSI signals depend on SMA confirmation
    - macd_depends_on_rsi: How much MACD signals depend on RSI confirmation
    - bollinger_depends_on_volume: How much Bollinger signals depend on volume confirmation

    # Individual strategy parameters
    - Various parameters for each strategy component

    # Risk management
    - take_profit: Take profit percentage
    - stop_loss: Stop loss percentage

    # Timeframe adjustment
    - timeframe_multiplier: Multiplier for timeframe-dependent parameters (for 5-minute chart optimization)
    """
    import pandas as pd
    import numpy as np

    # Initialize weighted signals
    weighted_buy_signals = pd.Series(0.0, index=data.index)
    weighted_sell_signals = pd.Series(0.0, index=data.index)

    # Total weights for normalization
    total_buy_weight = 0.0
    total_sell_weight = 0.0

    # Dictionary to store individual strategy signals for dependency calculations
    strategy_buy_signals = {}
    strategy_sell_signals = {}

    # Add SMA strategy signals if enabled
    if use_sma:
        # Calculate SMAs
        data["short_sma"] = data["close"].rolling(window=sma_short_window).mean()
        data["long_sma"] = data["close"].rolling(window=sma_long_window).mean()

        # Initialize signals
        sma_buy = pd.Series(False, index=data.index)
        sma_sell = pd.Series(False, index=data.index)

        # Generate signals
        for i in range(1, len(data)):
            # Buy when short MA crosses above long MA
            if data["short_sma"].iloc[i-1] < data["long_sma"].iloc[i-1] and data["short_sma"].iloc[i] > data["long_sma"].iloc[i]:
                sma_buy.iloc[i] = True

            # Sell when short MA crosses below long MA
            elif data["short_sma"].iloc[i-1] > data["long_sma"].iloc[i-1] and data["short_sma"].iloc[i] < data["long_sma"].iloc[i]:
                sma_sell.iloc[i] = True

        # Store signals for dependency calculations
        strategy_buy_signals['sma'] = sma_buy
        strategy_sell_signals['sma'] = sma_sell

        # Add weighted signals
        weighted_buy_signals += sma_buy.astype(float) * weight_sma_buy
        weighted_sell_signals += sma_sell.astype(float) * weight_sma_sell

        # Update total weights
        total_buy_weight += weight_sma_buy
        total_sell_weight += weight_sma_sell

    # Add RSI strategy signals if enabled
    if use_rsi:
        # Calculate RSI
        delta = data["close"].diff()
        gain = delta.where(delta > 0, 0).rolling(window=rsi_period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=rsi_period).mean()
        rs = gain / loss
        data["rsi"] = 100 - (100 / (1 + rs))

        # Initialize signals
        rsi_buy = pd.Series(False, index=data.index)
        rsi_sell = pd.Series(False, index=data.index)

        # Generate signals
        for i in range(1, len(data)):
            # Buy when RSI crosses above oversold level
            if data["rsi"].iloc[i-1] < rsi_oversold and data["rsi"].iloc[i] > rsi_oversold:
                rsi_buy.iloc[i] = True

            # Sell when RSI crosses above overbought level
            elif data["rsi"].iloc[i-1] < rsi_overbought and data["rsi"].iloc[i] > rsi_overbought:
                rsi_sell.iloc[i] = True

        # Store signals for dependency calculations
        strategy_buy_signals['rsi'] = rsi_buy
        strategy_sell_signals['rsi'] = rsi_sell

        # Add weighted signals
        weighted_buy_signals += rsi_buy.astype(float) * weight_rsi_buy
        weighted_sell_signals += rsi_sell.astype(float) * weight_rsi_sell

        # Update total weights
        total_buy_weight += weight_rsi_buy
        total_sell_weight += weight_rsi_sell

    # Add MACD strategy signals if enabled
    if use_macd:
        # Calculate MACD
        data["ema_fast"] = data["close"].ewm(span=macd_fast_period, adjust=False).mean()
        data["ema_slow"] = data["close"].ewm(span=macd_slow_period, adjust=False).mean()
        data["macd"] = data["ema_fast"] - data["ema_slow"]
        data["signal"] = data["macd"].ewm(span=macd_signal_period, adjust=False).mean()

        # Initialize signals
        macd_buy = pd.Series(False, index=data.index)
        macd_sell = pd.Series(False, index=data.index)

        # Generate signals
        for i in range(1, len(data)):
            # Buy when MACD crosses above signal line
            if data["macd"].iloc[i-1] < data["signal"].iloc[i-1] and data["macd"].iloc[i] > data["signal"].iloc[i]:
                macd_buy.iloc[i] = True

            # Sell when MACD crosses below signal line
            elif data["macd"].iloc[i-1] > data["signal"].iloc[i-1] and data["macd"].iloc[i] < data["signal"].iloc[i]:
                macd_sell.iloc[i] = True

        # Store signals for dependency calculations
        strategy_buy_signals['macd'] = macd_buy
        strategy_sell_signals['macd'] = macd_sell

        # Add weighted signals
        weighted_buy_signals += macd_buy.astype(float) * weight_macd_buy
        weighted_sell_signals += macd_sell.astype(float) * weight_macd_sell

        # Update total weights
        total_buy_weight += weight_macd_buy
        total_sell_weight += weight_macd_sell

    # Add Bollinger Bands strategy signals if enabled
    if use_bollinger:
        # Calculate Bollinger Bands
        data["sma"] = data["close"].rolling(window=bollinger_window).mean()
        data["std"] = data["close"].rolling(window=bollinger_window).std()
        data["upper_band"] = data["sma"] + (data["std"] * bollinger_num_std)
        data["lower_band"] = data["sma"] - (data["std"] * bollinger_num_std)

        # Initialize signals
        bollinger_buy = pd.Series(False, index=data.index)
        bollinger_sell = pd.Series(False, index=data.index)

        # Generate signals
        for i in range(1, len(data)):
            # Buy when price touches the lower band (within 0.1%)
            lower_band_touch = abs(data["close"].iloc[i] - data["lower_band"].iloc[i]) / data["lower_band"].iloc[i] < 0.001
            if lower_band_touch and data["close"].iloc[i] > data["close"].iloc[i-1]:
                bollinger_buy.iloc[i] = True

            # Sell when price touches the upper band (within 0.1%)
            upper_band_touch = abs(data["close"].iloc[i] - data["upper_band"].iloc[i]) / data["upper_band"].iloc[i] < 0.001
            if upper_band_touch and data["close"].iloc[i] < data["close"].iloc[i-1]:
                bollinger_sell.iloc[i] = True

        # Store signals for dependency calculations
        strategy_buy_signals['bollinger'] = bollinger_buy
        strategy_sell_signals['bollinger'] = bollinger_sell

        # Add weighted signals
        weighted_buy_signals += bollinger_buy.astype(float) * weight_bollinger_buy
        weighted_sell_signals += bollinger_sell.astype(float) * weight_bollinger_sell

        # Update total weights
        total_buy_weight += weight_bollinger_buy
        total_sell_weight += weight_bollinger_sell

    # Add Stochastic Oscillator signals if enabled
    if use_stochastic:
        # Calculate Stochastic Oscillator
        data['lowest_low'] = data['low'].rolling(window=stochastic_k_period).min()
        data['highest_high'] = data['high'].rolling(window=stochastic_k_period).max()
        data['%K'] = 100 * ((data['close'] - data['lowest_low']) /
                           (data['highest_high'] - data['lowest_low']))
        data['%D'] = data['%K'].rolling(window=stochastic_d_period).mean()

        # Initialize signals
        stochastic_buy = pd.Series(False, index=data.index)
        stochastic_sell = pd.Series(False, index=data.index)

        # Generate signals
        for i in range(1, len(data)):
            # Buy when %K crosses above %D in oversold region
            if (data['%K'].iloc[i-1] < data['%D'].iloc[i-1] and
                data['%K'].iloc[i] > data['%D'].iloc[i] and
                data['%K'].iloc[i-1] < stochastic_oversold):
                stochastic_buy.iloc[i] = True

            # Sell when %K crosses below %D in overbought region
            elif (data['%K'].iloc[i-1] > data['%D'].iloc[i-1] and
                  data['%K'].iloc[i] < data['%D'].iloc[i] and
                  data['%K'].iloc[i-1] > stochastic_overbought):
                stochastic_sell.iloc[i] = True

        # Store signals for dependency calculations
        strategy_buy_signals['stochastic'] = stochastic_buy
        strategy_sell_signals['stochastic'] = stochastic_sell

        # Add weighted signals
        weighted_buy_signals += stochastic_buy.astype(float) * weight_stochastic_buy
        weighted_sell_signals += stochastic_sell.astype(float) * weight_stochastic_sell

        # Update total weights
        total_buy_weight += weight_stochastic_buy
        total_sell_weight += weight_stochastic_sell

    # Add Volume analysis signals if enabled
    if use_volume:
        # Calculate volume indicators
        data['volume_sma'] = data['volume'].rolling(window=volume_window).mean()
        data['volume_ratio'] = data['volume'] / data['volume_sma']

        # Initialize signals
        volume_buy = pd.Series(False, index=data.index)
        volume_sell = pd.Series(False, index=data.index)

        # Generate signals
        for i in range(1, len(data)):
            # Buy on high volume with price increase
            if (data['volume_ratio'].iloc[i] > volume_threshold and
                data['close'].iloc[i] > data['close'].iloc[i-1]):
                volume_buy.iloc[i] = True

            # Sell on high volume with price decrease
            elif (data['volume_ratio'].iloc[i] > volume_threshold and
                  data['close'].iloc[i] < data['close'].iloc[i-1]):
                volume_sell.iloc[i] = True

        # Store signals for dependency calculations
        strategy_buy_signals['volume'] = volume_buy
        strategy_sell_signals['volume'] = volume_sell

        # Add weighted signals
        weighted_buy_signals += volume_buy.astype(float) * weight_volume_buy
        weighted_sell_signals += volume_sell.astype(float) * weight_volume_sell

        # Update total weights
        total_buy_weight += weight_volume_buy
        total_sell_weight += weight_volume_sell

    # Apply strategy dependencies if enabled

    # SMA depends on volume
    if use_sma and use_volume and sma_depends_on_volume > 0:
        if 'sma' in strategy_buy_signals and 'volume' in strategy_buy_signals:
            # Strengthen SMA signals when volume confirms
            volume_confirmed_buy = strategy_buy_signals['sma'] & strategy_buy_signals['volume']
            # Add extra weight for confirmed signals
            weighted_buy_signals += volume_confirmed_buy.astype(float) * weight_sma_buy * sma_depends_on_volume

        if 'sma' in strategy_sell_signals and 'volume' in strategy_sell_signals:
            # Strengthen SMA signals when volume confirms
            volume_confirmed_sell = strategy_sell_signals['sma'] & strategy_sell_signals['volume']
            # Add extra weight for confirmed signals
            weighted_sell_signals += volume_confirmed_sell.astype(float) * weight_sma_sell * sma_depends_on_volume

    # RSI depends on SMA
    if use_rsi and use_sma and rsi_depends_on_sma > 0:
        if 'rsi' in strategy_buy_signals and 'sma' in strategy_buy_signals:
            # Strengthen RSI signals when SMA confirms
            sma_confirmed_buy = strategy_buy_signals['rsi'] & strategy_buy_signals['sma']
            # Add extra weight for confirmed signals
            weighted_buy_signals += sma_confirmed_buy.astype(float) * weight_rsi_buy * rsi_depends_on_sma

        if 'rsi' in strategy_sell_signals and 'sma' in strategy_sell_signals:
            # Strengthen RSI signals when SMA confirms
            sma_confirmed_sell = strategy_sell_signals['rsi'] & strategy_sell_signals['sma']
            # Add extra weight for confirmed signals
            weighted_sell_signals += sma_confirmed_sell.astype(float) * weight_rsi_sell * rsi_depends_on_sma

    # MACD depends on RSI
    if use_macd and use_rsi and macd_depends_on_rsi > 0:
        if 'macd' in strategy_buy_signals and 'rsi' in strategy_buy_signals:
            # Strengthen MACD signals when RSI confirms
            rsi_confirmed_buy = strategy_buy_signals['macd'] & strategy_buy_signals['rsi']
            # Add extra weight for confirmed signals
            weighted_buy_signals += rsi_confirmed_buy.astype(float) * weight_macd_buy * macd_depends_on_rsi

        if 'macd' in strategy_sell_signals and 'rsi' in strategy_sell_signals:
            # Strengthen MACD signals when RSI confirms
            rsi_confirmed_sell = strategy_sell_signals['macd'] & strategy_sell_signals['rsi']
            # Add extra weight for confirmed signals
            weighted_sell_signals += rsi_confirmed_sell.astype(float) * weight_macd_sell * macd_depends_on_rsi

    # Bollinger depends on volume
    if use_bollinger and use_volume and bollinger_depends_on_volume > 0:
        if 'bollinger' in strategy_buy_signals and 'volume' in strategy_buy_signals:
            # Strengthen Bollinger signals when volume confirms
            volume_confirmed_buy = strategy_buy_signals['bollinger'] & strategy_buy_signals['volume']
            # Add extra weight for confirmed signals
            weighted_buy_signals += volume_confirmed_buy.astype(float) * weight_bollinger_buy * bollinger_depends_on_volume

        if 'bollinger' in strategy_sell_signals and 'volume' in strategy_sell_signals:
            # Strengthen Bollinger signals when volume confirms
            volume_confirmed_sell = strategy_sell_signals['bollinger'] & strategy_sell_signals['volume']
            # Add extra weight for confirmed signals
            weighted_sell_signals += volume_confirmed_sell.astype(float) * weight_bollinger_sell * bollinger_depends_on_volume

    # Normalize weights (avoid division by zero)
    if total_buy_weight > 0:
        weighted_buy_signals = weighted_buy_signals / total_buy_weight

    if total_sell_weight > 0:
        weighted_sell_signals = weighted_sell_signals / total_sell_weight

    # Generate final signals based on thresholds
    final_buy_signals = weighted_buy_signals > threshold_buy
    final_sell_signals = weighted_sell_signals > threshold_sell

    return final_buy_signals, final_sell_signals

# Strategy parameters and descriptions
STRATEGY_PARAMS['basic_strategy'] = {'param1': 10, 'param2': 20}
STRATEGY_DESCRIPTIONS['basic_strategy'] = 'Basic Strategy Template - Buy when price increases by param1/1000, sell when it decreases by param2/1000'

STRATEGY_PARAMS['master_strategy'] = {
    # Strategy selection
    'use_sma': True,
    'use_rsi': True,
    'use_macd': True,
    'use_bollinger': True,
    'use_stochastic': False,
    'use_volume': False,
    'use_pattern_recognition': False,
    'use_support_resistance': False,

    # Strategy weights
    'weight_sma_buy': 1.0,
    'weight_sma_sell': 1.0,
    'weight_rsi_buy': 1.0,
    'weight_rsi_sell': 1.0,
    'weight_macd_buy': 1.0,
    'weight_macd_sell': 1.0,
    'weight_bollinger_buy': 1.0,
    'weight_bollinger_sell': 1.0,
    'weight_stochastic_buy': 1.0,
    'weight_stochastic_sell': 1.0,
    'weight_volume_buy': 1.0,
    'weight_volume_sell': 1.0,
    'weight_pattern_buy': 1.0,
    'weight_pattern_sell': 1.0,
    'weight_support_resistance_buy': 1.0,
    'weight_support_resistance_sell': 1.0,

    # Signal thresholds
    'threshold_buy': 0.3,
    'threshold_sell': 0.3,

    # Strategy dependencies
    'sma_depends_on_volume': 0.0,
    'rsi_depends_on_sma': 0.0,
    'macd_depends_on_rsi': 0.0,
    'bollinger_depends_on_volume': 0.0,

    # SMA parameters
    'sma_short_window': 10,
    'sma_long_window': 20,

    # RSI parameters
    'rsi_period': 14,
    'rsi_oversold': 30,
    'rsi_overbought': 70,

    # MACD parameters
    'macd_fast_period': 12,
    'macd_slow_period': 26,
    'macd_signal_period': 9,

    # Bollinger Bands parameters
    'bollinger_window': 20,
    'bollinger_num_std': 2,

    # Stochastic parameters
    'stochastic_k_period': 14,
    'stochastic_d_period': 3,
    'stochastic_slowing': 3,
    'stochastic_oversold': 20,
    'stochastic_overbought': 80,

    # Volume parameters
    'volume_window': 20,
    'volume_threshold': 1.5,

    # Pattern recognition parameters
    'pattern_window': 5,

    # Support/Resistance parameters
    'support_resistance_window': 50,
    'support_resistance_threshold': 0.02,

    # Risk management
    'take_profit': 5.0,
    'stop_loss': 2.0,

    # Timeframe adjustment
    'timeframe_multiplier': 1.0
}
STRATEGY_DESCRIPTIONS['master_strategy'] = 'Master combined strategy with all available strategies and configurable weights'
