"""
Improved Strategy-bot for optimizing trading strategies.
This module provides a more robust implementation of the Strategy-bot
with better parameter optimization and error handling.
"""

import random
import time
import json
import os
import importlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
import pandas as pd
import numpy as np
import traceback
import threading
import queue
from hyperliquid_api import fetch_historical_data
from new_minimal_backtester import run_backtest
from strategies import STRATEGY_PARAMS, STRATEGY_DESCRIPTIONS

# Global variables for tracking the Strategy-bot status
strategy_bot_thread = None
strategy_bot_queue = queue.Queue()
strategy_bot_running = False
strategy_bot_progress = 0
strategy_bot_status = "Niet actief"
strategy_bot_results = []
strategy_bot_current_performance = 0.0
strategy_bot_current_win_ratio = 0.0
strategy_bot_current_trades = 0
strategy_bot_iteration = 0
strategy_bot_total_iterations = 0
strategy_bot_start_time = None
strategy_bot_parameter_history = []

# Path to store Strategy-bot results
RESULTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategy_bot_results')
os.makedirs(RESULTS_DIR, exist_ok=True)

class ImprovedStrategyBotOptimizer:
    """
    Improved class for optimizing trading strategies.
    """
    def __init__(self,
                 pair: str,
                 start_date: str,
                 end_date: str,
                 iterations: int = 20,
                 strategy: str = 'master_strategy'):
        self.pair = pair
        self.start_date = start_date
        self.end_date = end_date
        self.iterations = iterations
        self.strategy = strategy

        # Define parameter ranges for optimization
        self.param_ranges = {
            # Strategy selection
            'use_sma': [True, False],
            'use_rsi': [True, False],
            'use_macd': [True, False],
            'use_bollinger': [True, False],
            'use_stochastic': [True, False],
            'use_volume': [True, False],

            # Strategy weights
            'weight_sma_buy': [0.5, 1.0, 1.5, 2.0],
            'weight_sma_sell': [0.5, 1.0, 1.5, 2.0],
            'weight_rsi_buy': [0.5, 1.0, 1.5, 2.0],
            'weight_rsi_sell': [0.5, 1.0, 1.5, 2.0],
            'weight_macd_buy': [0.5, 1.0, 1.5, 2.0],
            'weight_macd_sell': [0.5, 1.0, 1.5, 2.0],
            'weight_bollinger_buy': [0.5, 1.0, 1.5, 2.0],
            'weight_bollinger_sell': [0.5, 1.0, 1.5, 2.0],
            'weight_stochastic_buy': [0.5, 1.0, 1.5, 2.0],
            'weight_stochastic_sell': [0.5, 1.0, 1.5, 2.0],
            'weight_volume_buy': [0.5, 1.0, 1.5, 2.0],
            'weight_volume_sell': [0.5, 1.0, 1.5, 2.0],

            # Strategy dependencies
            'sma_depends_on_volume': [0.0, 0.3, 0.6, 1.0],
            'rsi_depends_on_sma': [0.0, 0.3, 0.6, 1.0],
            'macd_depends_on_rsi': [0.0, 0.3, 0.6, 1.0],
            'bollinger_depends_on_volume': [0.0, 0.3, 0.6, 1.0],

            # Signal thresholds
            'threshold_buy': [0.1, 0.2, 0.3, 0.4, 0.5],
            'threshold_sell': [0.1, 0.2, 0.3, 0.4, 0.5],

            # Indicator parameters
            'sma_short_window': [5, 10, 15, 20],
            'sma_long_window': [20, 30, 40, 50],
            'rsi_period': [7, 14, 21],
            'rsi_oversold': [20, 30, 40],
            'rsi_overbought': [60, 70, 80],
            'macd_fast_period': [8, 12, 16],
            'macd_slow_period': [21, 26, 30],
            'macd_signal_period': [7, 9, 11],
            'bollinger_window': [15, 20, 25],
            'bollinger_num_std': [1.5, 2.0, 2.5],
            'stochastic_k_period': [9, 14, 21],
            'stochastic_d_period': [3, 5, 7],
            'stochastic_slowing': [3, 5, 7],
            'stochastic_oversold': [10, 20, 30],
            'stochastic_overbought': [70, 80, 90],

            # Risk management
            'take_profit': [3.0, 5.0, 7.0, 10.0],
            'stop_loss': [1.0, 2.0, 3.0, 5.0]
        }

        # Initialize best parameters and performance
        self.best_params = STRATEGY_PARAMS.get(strategy, {}).copy()
        self.best_performance = -float('inf')
        self.best_win_ratio = 0.0
        self.best_trades = 0

        # Initialize results list
        self.results = []

    def _evaluate_strategy(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run backtest for a strategy with specific parameters.

        Args:
            params: Dictionary of strategy parameters

        Returns:
            Dictionary with backtest results
        """
        try:
            # Run backtest with parameters
            result = run_backtest(self.pair, self.strategy, self.start_date, self.end_date, params)

            if 'error' in result:
                print(f"Error in backtest: {result['error']}")
                return {
                    'performance': 0.0,
                    'win_ratio': 0.0,
                    'number_of_trades': 0,
                    'error': result['error']
                }

            # Extract relevant metrics
            performance = result.get('total_pnl_pct', 0.0)
            win_ratio = result.get('win_ratio_pct', 0.0)
            num_trades = result.get('number_of_trades', 0)

            print(f"Performance: {performance:.2f}%, Win Ratio: {win_ratio:.2f}%, Trades: {num_trades}")

            return {
                'performance': performance,
                'win_ratio': win_ratio,
                'number_of_trades': num_trades,
                'trades': result.get('trades', []),
                'signals': result.get('signals', [])
            }
        except Exception as e:
            print(f"Error evaluating strategy: {e}")
            traceback.print_exc()
            return {
                'performance': 0.0,
                'win_ratio': 0.0,
                'number_of_trades': 0,
                'error': str(e)
            }

    def _save_result(self, strategy_id: str, parameters: Dict[str, Any],
                    performance: float, win_ratio: float, number_of_trades: int) -> None:
        """
        Save a Strategy-bot result.

        Args:
            strategy_id: ID of the strategy
            parameters: Dictionary of strategy parameters
            performance: Performance percentage
            win_ratio: Win ratio percentage
            number_of_trades: Number of trades
        """
        global strategy_bot_results

        try:
            # Create a unique version number
            version = len(strategy_bot_results) + 1

            # Create result object
            result = {
                'version': version,
                'timestamp': datetime.now().isoformat(),
                'strategy_id': strategy_id,
                'parameters': parameters,
                'performance': float(performance),
                'pair': self.pair,
                'start_date': self.start_date,
                'end_date': self.end_date,
                'win_ratio': float(win_ratio),
                'number_of_trades': int(number_of_trades),
                'num_trades': int(number_of_trades)  # For compatibility
            }

            # Add to global results list
            strategy_bot_results.append(result)

            # Save to file
            result_file = os.path.join(RESULTS_DIR, f"result_{version}.json")
            with open(result_file, 'w') as f:
                json.dump(result, f, indent=4)

            print(f"Saved result to {result_file}")

        except Exception as e:
            print(f"Error saving result: {e}")
            traceback.print_exc()

    def _save_permanent_strategy(self, strategy_result: Dict[str, Any]) -> None:
        """
        Save the best strategy as a permanent strategy in the system.

        Args:
            strategy_result: Dictionary with strategy result
        """
        global strategy_bot_results

        try:
            # Generate a unique name for the strategy
            timestamp = int(time.time())
            strategy_name = f"Geoptimaliseerde Strategie {timestamp}"
            strategy_id = f"optimized_strategy_{timestamp}"

            # Get the parameters from the result
            params = strategy_result['parameters']

            # Add to STRATEGY_PARAMS and STRATEGY_DESCRIPTIONS
            STRATEGY_PARAMS[strategy_id] = params.copy()
            STRATEGY_DESCRIPTIONS[strategy_id] = f"Automatisch geoptimaliseerde strategie met {strategy_result['performance']:.2f}% rendement"

            # Create a permanent result entry
            permanent_result = {
                'version': len(strategy_bot_results) + 1,
                'timestamp': datetime.now().isoformat(),
                'strategy_id': strategy_id,
                'parameters': params,
                'performance': strategy_result.get('performance', 0.0),
                'pair': self.pair,
                'start_date': self.start_date,
                'end_date': self.end_date,
                'win_ratio': strategy_result.get('win_ratio', 0.0),
                'number_of_trades': strategy_result.get('number_of_trades', 0),
                'num_trades': strategy_result.get('number_of_trades', 0),
                'is_permanent': True,
                'name': strategy_name
            }

            # Add to global results list
            strategy_bot_results.append(permanent_result)

            # Save to file
            result_file = os.path.join(RESULTS_DIR, f"result_permanent_{timestamp}.json")
            with open(result_file, 'w') as f:
                json.dump(permanent_result, f, indent=4)

            # Update strategies.py file
            self._update_strategies_file(strategy_id, params, strategy_name)

            print(f"Successfully saved permanent strategy: {strategy_id}")

        except Exception as e:
            print(f"Error saving permanent strategy: {e}")
            traceback.print_exc()

    def _update_strategies_file(self, strategy_id: str, params: Dict[str, Any], strategy_name: str) -> None:
        """
        Update the strategies.py file with the new strategy.

        Args:
            strategy_id: ID of the strategy
            params: Dictionary of strategy parameters
            strategy_name: Name of the strategy
        """
        try:
            import inspect
            import strategies

            # Get the path to the strategies.py file
            strategies_file = inspect.getfile(strategies)

            # Read the current content of the file
            with open(strategies_file, 'r') as f:
                content = f.read()

            # Add the new strategy parameters
            new_content = content + f"\n\n# {strategy_name}\n"
            new_content += f"STRATEGY_PARAMS['{strategy_id}'] = {{\n"

            for key, value in params.items():
                if isinstance(value, bool):
                    new_content += f"    '{key}': {str(value).lower()},\n"
                elif isinstance(value, (int, float)):
                    new_content += f"    '{key}': {value},\n"
                else:
                    new_content += f"    '{key}': '{value}',\n"

            new_content += "}\n"
            new_content += f"STRATEGY_DESCRIPTIONS['{strategy_id}'] = '{strategy_name}'\n"

            # Write the updated content back to the file
            with open(strategies_file, 'w') as f:
                f.write(new_content)

            # Reload the strategies module
            importlib.reload(strategies)

        except Exception as e:
            print(f"Error updating strategies file: {e}")
            traceback.print_exc()

    def _generate_new_parameters(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate new parameters by randomly modifying some parameters.

        Args:
            base_params: Base parameters to modify

        Returns:
            Dictionary with new parameters
        """
        new_params = base_params.copy()

        # Randomly modify some parameters
        for param, values in self.param_ranges.items():
            if param in new_params and random.random() < 0.3:  # 30% chance to change each parameter
                new_params[param] = random.choice(values)

        return new_params

    def run(self) -> List[Dict[str, Any]]:
        """
        Run the Strategy-bot to optimize the parameters of the strategy.

        Returns:
            List of results
        """
        global strategy_bot_progress, strategy_bot_status
        global strategy_bot_current_performance, strategy_bot_current_win_ratio, strategy_bot_current_trades
        global strategy_bot_iteration, strategy_bot_total_iterations, strategy_bot_start_time, strategy_bot_parameter_history

        try:
            # Initialize status and progress
            strategy_bot_status = "Bezig met initialisatie"
            strategy_bot_progress = 5
            print(f"Initial progress: {strategy_bot_progress}%")

            # Set total iterations and reset current iteration
            strategy_bot_total_iterations = self.iterations
            strategy_bot_iteration = 0

            # Record start time
            strategy_bot_start_time = time.time()

            # Clear parameter history
            strategy_bot_parameter_history = []

            # Get the strategy parameters
            base_params = STRATEGY_PARAMS.get(self.strategy, {}).copy()
            print(f"Starting with base parameters: {base_params}")

            # Initialize best parameters and performance
            self.best_params = base_params.copy()
            self.best_performance = -float('inf')
            self.best_win_ratio = 0.0
            self.best_trades = 0

            # Optimization loop
            for iteration in range(self.iterations):
                if not strategy_bot_running:
                    break

                # Update iteration counter
                strategy_bot_iteration = iteration + 1

                strategy_bot_status = f"Iteratie {strategy_bot_iteration}/{self.iterations}: Optimaliseren van parameters"
                print(f"Iteration {strategy_bot_iteration}/{self.iterations}")

                # Update progress
                strategy_bot_progress = 5 + int(strategy_bot_iteration * 90 / self.iterations)
                print(f"Progress updated to: {strategy_bot_progress}%")

                # Generate new parameters
                if iteration == 0:
                    # First iteration: use base parameters
                    current_params = base_params.copy()
                else:
                    # Subsequent iterations: modify best parameters
                    current_params = self._generate_new_parameters(self.best_params)

                print(f"Testing parameters: {current_params}")

                # Evaluate strategy with current parameters
                result = self._evaluate_strategy(current_params)

                # Extract metrics
                performance = result.get('performance', 0.0)
                win_ratio = result.get('win_ratio', 0.0)
                num_trades = result.get('number_of_trades', 0)

                # Update current performance metrics for real-time display
                strategy_bot_current_performance = performance
                strategy_bot_current_win_ratio = win_ratio
                strategy_bot_current_trades = num_trades

                # Add to parameter history
                strategy_bot_parameter_history.append({
                    'iteration': strategy_bot_iteration,
                    'parameters': current_params.copy(),
                    'performance': performance,
                    'win_ratio': win_ratio,
                    'trades': num_trades,
                    'timestamp': time.time()
                })

                # Update best if improved
                if performance > self.best_performance:
                    self.best_performance = performance
                    self.best_params = current_params.copy()
                    self.best_win_ratio = win_ratio
                    self.best_trades = num_trades

                    print(f"New best performance: {self.best_performance:.2f}%")

                    # Create a unique ID for this version
                    timestamp = int(time.time())
                    version_id = f"{self.strategy}_v{iteration+1}_{timestamp}"

                    # Save the result
                    self._save_result(
                        version_id,
                        self.best_params,
                        self.best_performance,
                        self.best_win_ratio,
                        self.best_trades
                    )

                    # Add to results
                    self.results.append({
                        'strategy_id': version_id,
                        'parameters': self.best_params.copy(),
                        'performance': self.best_performance,
                        'win_ratio': self.best_win_ratio,
                        'number_of_trades': self.best_trades,
                        'timestamp': datetime.now().isoformat()
                    })

            # Sort results by performance
            self.results.sort(key=lambda x: x.get('performance', 0.0), reverse=True)

            # Final optimization phase
            if self.results and strategy_bot_running:
                strategy_bot_status = "Fase 2: Finetuning van de beste parameters"
                strategy_bot_progress = 95
                print("Phase 2: Fine-tuning the best parameters")

                # Get the best result
                best_result = self.results[0]
                print(f"Best result: {best_result['strategy_id']} with performance: {best_result['performance']:.2f}%")

                # Create a final optimized strategy
                timestamp = int(time.time())
                final_strategy_id = f"{self.strategy}_optimized_{timestamp}"

                # Create the final strategy result
                final_strategy_result = {
                    'strategy_id': final_strategy_id,
                    'parameters': best_result['parameters'].copy(),
                    'performance': best_result['performance'],
                    'win_ratio': best_result['win_ratio'],
                    'number_of_trades': best_result['number_of_trades'],
                    'timestamp': datetime.now().isoformat()
                }

                # Update current performance metrics for real-time display
                strategy_bot_current_performance = best_result['performance']
                strategy_bot_current_win_ratio = best_result['win_ratio']
                strategy_bot_current_trades = best_result['number_of_trades']

                # Save the result
                self._save_result(
                    final_strategy_result['strategy_id'],
                    final_strategy_result['parameters'],
                    final_strategy_result['performance'],
                    final_strategy_result['win_ratio'],
                    final_strategy_result['number_of_trades']
                )

                # Add to results
                self.results.append(final_strategy_result)

                print(f"Final optimized strategy: {final_strategy_id} performance: {final_strategy_result['performance']:.2f}%")

                # Save the best strategy as a permanent strategy in the system
                self._save_permanent_strategy(final_strategy_result)

                # Update the strategy parameters with the best parameters
                STRATEGY_PARAMS[self.strategy] = best_result['parameters'].copy()
                print(f"Updated {self.strategy} parameters: {STRATEGY_PARAMS[self.strategy]}")

            strategy_bot_status = "Voltooid"
            strategy_bot_progress = 100

            return self.results

        except Exception as e:
            print(f"Error running Strategy-bot: {e}")
            traceback.print_exc()
            strategy_bot_status = "Fout"
            return self.results


# Functions to control the Strategy-bot
def start_strategy_bot(pair: str, start_date: str, end_date: str, iterations: int = 20, strategy: str = 'master_strategy') -> Dict[str, Any]:
    """
    Start the Strategy-bot.

    Args:
        pair: Trading pair symbol (e.g., 'BTC-USD')
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        iterations: Number of iterations to run
        strategy: ID of the strategy to optimize

    Returns:
        Dictionary with status information
    """
    global strategy_bot_thread, strategy_bot_running, strategy_bot_progress, strategy_bot_status, strategy_bot_results

    # Check if already running
    if strategy_bot_running:
        return {
            'status': 'error',
            'message': 'Strategy-bot is already running'
        }

    try:
        # Reset status
        strategy_bot_running = True
        strategy_bot_progress = 0
        strategy_bot_status = "Bezig met starten"

        # Create optimizer
        optimizer = ImprovedStrategyBotOptimizer(pair, start_date, end_date, iterations, strategy)

        # Define thread function
        def run_optimizer():
            global strategy_bot_running, strategy_bot_status, strategy_bot_progress
            try:
                # Run optimizer
                results = optimizer.run()

                # Update status
                strategy_bot_status = "Voltooid"
                strategy_bot_progress = 100
                strategy_bot_running = False

                # Save status to file
                save_status_to_file()

                print(f"Strategy-bot completed with {len(results)} results")

            except Exception as e:
                print(f"Error in Strategy-bot thread: {e}")
                traceback.print_exc()
                strategy_bot_status = "Fout"
                strategy_bot_running = False

                # Save status to file
                save_status_to_file()

        # Start thread
        strategy_bot_thread = threading.Thread(target=run_optimizer)
        strategy_bot_thread.daemon = True
        strategy_bot_thread.start()

        return {
            'status': 'success',
            'message': 'Strategy-bot started successfully'
        }

    except Exception as e:
        print(f"Error starting Strategy-bot: {e}")
        traceback.print_exc()
        strategy_bot_running = False
        strategy_bot_status = "Fout bij starten"

        return {
            'status': 'error',
            'message': f'Error starting Strategy-bot: {str(e)}'
        }


def stop_strategy_bot() -> Dict[str, Any]:
    """
    Stop the Strategy-bot.

    Returns:
        Dictionary with status information
    """
    global strategy_bot_running, strategy_bot_progress, strategy_bot_status
    global strategy_bot_current_performance, strategy_bot_current_win_ratio, strategy_bot_current_trades
    global strategy_bot_iteration, strategy_bot_total_iterations, strategy_bot_start_time, strategy_bot_parameter_history

    # Set running flag to False
    strategy_bot_running = False
    strategy_bot_status = "Gestopt"

    # Calculate elapsed time
    elapsed_time = 0
    if strategy_bot_start_time:
        elapsed_time = int(time.time() - strategy_bot_start_time)

    # Save the current status to file
    save_status_to_file()

    return {
        'status': 'success',
        'message': 'Strategy-bot stopped successfully',
        'progress': strategy_bot_progress,
        'bot_status': strategy_bot_status,
        'current_performance': strategy_bot_current_performance,
        'current_win_ratio': strategy_bot_current_win_ratio,
        'current_trades': strategy_bot_current_trades,
        'iteration': strategy_bot_iteration,
        'total_iterations': strategy_bot_total_iterations,
        'elapsed_time': elapsed_time,
        'results': strategy_bot_results[-10:] if strategy_bot_results else []
    }


def get_strategy_bot_status() -> Dict[str, Any]:
    """
    Get the current status of the Strategy-bot.

    Returns:
        Dictionary with status information
    """
    global strategy_bot_running, strategy_bot_progress, strategy_bot_status, strategy_bot_results
    global strategy_bot_current_performance, strategy_bot_current_win_ratio, strategy_bot_current_trades
    global strategy_bot_iteration, strategy_bot_total_iterations, strategy_bot_start_time, strategy_bot_parameter_history

    # Print debug information
    print(f"Strategy-bot status: {strategy_bot_status}, progress: {strategy_bot_progress}, running: {strategy_bot_running}")

    # Ensure progress is an integer
    try:
        progress = int(strategy_bot_progress)
    except (ValueError, TypeError):
        progress = 0

    # Try to load status from file if not running
    if not strategy_bot_running and progress == 0 and strategy_bot_status == "Niet actief":
        try:
            if os.path.exists('strategy_bot_status.json'):
                with open('strategy_bot_status.json', 'r') as f:
                    saved_status = json.load(f)
                    # Only use saved status if it shows the bot was running or completed
                    if saved_status.get('status') not in ["Niet actief"]:
                        print(f"Loading saved status from file: {saved_status}")

                        # Update global variables from saved status
                        strategy_bot_status = saved_status.get('status', strategy_bot_status)
                        strategy_bot_progress = saved_status.get('progress', strategy_bot_progress)
                        strategy_bot_running = saved_status.get('running', strategy_bot_running)

                        # Update performance metrics if available
                        strategy_bot_current_performance = saved_status.get('current_performance', 0.0)
                        strategy_bot_current_win_ratio = saved_status.get('current_win_ratio', 0.0)
                        strategy_bot_current_trades = saved_status.get('current_trades', 0)

                        # Update iteration information if available
                        strategy_bot_iteration = saved_status.get('iteration', 0)
                        strategy_bot_total_iterations = saved_status.get('total_iterations', 0)

                        # Update parameter history if available
                        if 'parameter_history' in saved_status:
                            strategy_bot_parameter_history = saved_status.get('parameter_history', [])

                        # Update results if available
                        if 'results' in saved_status and saved_status['results']:
                            # Replace the entire results list with the saved results
                            strategy_bot_results = saved_status['results']

                        return saved_status
        except Exception as e:
            print(f"Error loading saved status: {e}")

    # Make sure we return the best results
    if strategy_bot_results:
        # Sort results by performance (highest first)
        strategy_bot_results.sort(key=lambda x: x.get('performance', 0), reverse=True)

    # Print debug information about the current performance metrics
    print(f"Returning status with performance: {strategy_bot_current_performance}, win ratio: {strategy_bot_current_win_ratio}, trades: {strategy_bot_current_trades}")

    # Determine performance values to return
    # Return None if running but progress is very low (likely hasn't calculated first value)
    perf_val = strategy_bot_current_performance if not (strategy_bot_running and progress < 20) else None
    win_ratio_val = strategy_bot_current_win_ratio if not (strategy_bot_running and progress < 20) else None
    trades_val = strategy_bot_current_trades if not (strategy_bot_running and progress < 20) else None

    # Calculate elapsed time and estimated time remaining
    elapsed_time = 0
    estimated_time_remaining = 0

    if strategy_bot_running and strategy_bot_start_time:
        elapsed_time = int(time.time() - strategy_bot_start_time)

        if progress > 0:
            time_per_percent = elapsed_time / progress
            estimated_time_remaining = int(time_per_percent * (100 - progress))

    return {
        'status': strategy_bot_status,
        'progress': progress,
        'running': strategy_bot_running,
        'current_performance': perf_val,
        'current_win_ratio': win_ratio_val,
        'current_trades': trades_val,
        'iteration': strategy_bot_iteration,
        'total_iterations': strategy_bot_total_iterations,
        'elapsed_time': elapsed_time,
        'estimated_time_remaining': estimated_time_remaining,
        'parameter_history': strategy_bot_parameter_history[-5:] if strategy_bot_parameter_history else [],
        'results': strategy_bot_results[-10:] if strategy_bot_results else []
    }


def get_strategy_bot_results() -> Dict[str, Any]:
    """
    Get the results of the Strategy-bot.

    Returns:
        Dictionary with results
    """
    global strategy_bot_results

    # Sort results by performance
    if strategy_bot_results:
        strategy_bot_results.sort(key=lambda x: x.get('performance', 0), reverse=True)

    return {
        'results': strategy_bot_results
    }


def save_status_to_file() -> None:
    """
    Save the current status of the Strategy-bot to a file.
    """
    global strategy_bot_running, strategy_bot_progress, strategy_bot_status, strategy_bot_results
    global strategy_bot_current_performance, strategy_bot_current_win_ratio, strategy_bot_current_trades
    global strategy_bot_iteration, strategy_bot_total_iterations, strategy_bot_start_time, strategy_bot_parameter_history

    try:
        # Create status object
        status = {
            'status': strategy_bot_status,
            'progress': strategy_bot_progress,
            'running': strategy_bot_running,
            'current_performance': strategy_bot_current_performance,
            'current_win_ratio': strategy_bot_current_win_ratio,
            'current_trades': strategy_bot_current_trades,
            'iteration': strategy_bot_iteration,
            'total_iterations': strategy_bot_total_iterations,
            'parameter_history': strategy_bot_parameter_history[-5:] if strategy_bot_parameter_history else [],
            'results': strategy_bot_results[-10:] if strategy_bot_results else []
        }

        # Save to file
        with open('strategy_bot_status.json', 'w') as f:
            json.dump(status, f, indent=4)

        print(f"Saved status to file")

    except Exception as e:
        print(f"Error saving status to file: {e}")
        traceback.print_exc()


def reset_strategy_bot_results() -> Dict[str, Any]:
    """
    Reset all Strategy-bot results.

    Returns:
        Dictionary with status information
    """
    global strategy_bot_results, strategy_bot_parameter_history

    try:
        # Reset results
        strategy_bot_results = []
        strategy_bot_parameter_history = []

        # Delete all result files
        for file in os.listdir(RESULTS_DIR):
            if file.startswith('result_'):
                try:
                    os.remove(os.path.join(RESULTS_DIR, file))
                except Exception as e:
                    print(f"Error deleting result file {file}: {e}")

        # Save status to file
        save_status_to_file()

        return {
            'status': 'success',
            'message': 'Strategy-bot results reset successfully'
        }

    except Exception as e:
        print(f"Error resetting Strategy-bot results: {e}")
        traceback.print_exc()

        return {
            'status': 'error',
            'message': f'Error resetting Strategy-bot results: {str(e)}'
        }