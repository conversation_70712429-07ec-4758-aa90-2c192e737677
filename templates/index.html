<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingBot</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Include Chart.js directly -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
    <script src="{{ url_for('static', filename='js/chart_signals.js') }}"></script>
    <script src="{{ url_for('static', filename='js/createTradeTable.js') }}"></script>
    <script src="{{ url_for('static', filename='js/simple_trade_table.js') }}"></script>
    <script src="{{ url_for('static', filename='js/debug_signals.js') }}"></script>
    <script src="{{ url_for('static', filename='js/simple_signals.js') }}"></script>
    <script src="{{ url_for('static', filename='js/signals_chart.js') }}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #f0f0f0;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .strategy-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .strategy-params {
            margin-top: 10px;
        }
        .param-input {
            margin: 5px 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        /* Basic table styling */
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 0.9em;
        }
        .results-table th, .results-table td {
            border: 1px solid #444;
            padding: 8px;
            text-align: left;
        }
        .results-table th {
            background-color: #333;
            font-weight: bold;
        }
        .results-table tbody tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        .results-table tfoot td {
            border-top: 2px solid #555;
        }
    </style>
</head>
<body>
    <!-- Navigation Menu -->
    <div class="navigation-menu" style="background-color: #2a2a2a; padding: 10px; margin-bottom: 20px;">
        <div style="max-width: 1200px; margin: 0 auto; display: flex; gap: 20px;">
            <a href="/" style="color: #f0f0f0; text-decoration: none;">Home</a>
            <a href="/?tab=Configuration" style="color: {% if active_tab == 'Configuration' %}#4CAF50; font-weight: bold;{% else %}#f0f0f0;{% endif %} text-decoration: none;">Configuration</a>
            <a href="/strategies" style="color: #f0f0f0; text-decoration: none;">Strategy Management</a>
            <a href="/?tab=Backtesting" style="color: {% if active_tab == 'Backtesting' %}#4CAF50; font-weight: bold;{% else %}#f0f0f0;{% endif %} text-decoration: none;">Backtesting</a>
            <a href="/?tab=StrategyBot" style="color: {% if active_tab == 'StrategyBot' %}#4CAF50; font-weight: bold;{% else %}#f0f0f0;{% endif %} text-decoration: none;">Strategy-bot</a>
            <a href="/?tab=LiveTrading" style="color: {% if active_tab == 'LiveTrading' %}#4CAF50; font-weight: bold;{% else %}#f0f0f0;{% endif %} text-decoration: none;">Live Trading</a>
        </div>
    </div>

    <div class="container">
        <button id="theme-toggle" style="position: absolute; top: 10px; right: 20px;">Toggle Theme</button>
        <h1>TradingBot</h1>
        <div id="status-message" class="status-message">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>



    <!-- Configuration Tab -->
    <div id="Configuration" class="tab-content" style="display: {% if active_tab == 'Configuration' %}block{% else %}none{% endif %};">
        <h2>Credentials Configuration (Hyperliquid)</h2>
        <div style="color: red; border: 1px solid red; padding: 10px; margin-bottom: 15px;">
            <strong>Security Warning:</strong> Entering your private key here is highly insecure and only intended for local testing as part of this assignment. Never expose your private key in a real application's frontend or store it insecurely. Use environment variables or secure secret management tools in production.
        </div>
        <form id="config-form">
            <label for="wallet_address">Wallet Address:</label>
            <input type="text" id="wallet_address" name="wallet_address" value="{{ config.wallet_address }}" required pattern="^0x[a-fA-F0-9]{40}$" title="Enter a valid Ethereum address (0x...)">
            <br>
            <label for="private_key">Private Key:</label>
            <input type="password" id="private_key" name="private_key" value="{{ config.private_key }}" required pattern="^0x[a-fA-F0-9]{64}$" title="Enter a valid private key (0x...)">
            <br>
            <button type="submit">Save Keys</button>
            <button type="button" id="test-api-button">Test Keys</button>
        </form>
        <div id="config-status"></div>
    </div>

    <!-- Strategy Management Tab removed - now using /strategies page -->


    <!-- Backtesting Tab -->
    <div id="Backtesting" class="tab-content" style="display: {% if active_tab == 'Backtesting' %}block{% else %}none{% endif %};">
        <h2>Backtesting</h2>

        <div class="backtesting-container">
            <!-- Left panel for controls -->
            <div class="backtesting-controls">
                <div class="control-section">
                    <h3>1. Data Selection</h3>
                    <div class="form-group compact">
                        <label for="backtest-pair">Trading Pair:</label>
                        <select id="backtest-pair" class="compact-input">
                            <option value="BTC-USD">BTC-USD</option>
                            <option value="ETH-USD">ETH-USD</option>
                            <option value="SOL-USD">SOL-USD</option>
                            <option value="AVAX-USD">AVAX-USD</option>
                            <option value="LINK-USD">LINK-USD</option>
                        </select>
                    </div>
                    <div class="form-row">
                        <div class="form-group compact">
                            <label for="start-date">Start:</label>
                            <input type="date" id="start-date" class="compact-input" required>
                        </div>
                        <div class="form-group compact">
                            <label for="end-date">End:</label>
                            <input type="date" id="end-date" class="compact-input" required>
                        </div>
                    </div>
                    <div class="button-group">
                        <button type="button" id="fetch-data-button" class="button">Download Data</button>
                    </div>

                    <div id="download-progress" style="display: none;">
                        <p>Downloading... <span id="progress-text">0%</span></p>
                        <div class="progress-bar-container">
                            <div class="progress-bar" id="progress-bar"></div>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3>2. Strategy Selection</h3>
                    <div class="form-group compact">
                        <label for="backtest-strategy">Strategy:</label>
                        <select id="backtest-strategy" class="compact-input">
                            <option value="">-- Select a strategy --</option>
                            <!-- Options populated by JS -->
                        </select>
                    </div>
                    <div class="button-group">
                        <button type="button" id="run-backtest-button" class="button">Run Backtest</button>
                    </div>
                </div>

                <div class="control-section">
                    <h3>3. Optimization</h3>
                    <div class="form-group compact">
                        <label for="optimization-iterations">Iterations:</label>
                        <input type="number" id="optimization-iterations" class="compact-input" value="10" min="1" max="100">
                    </div>
                    <div class="button-group">
                        <button type="button" id="run-optimization-button" class="button">Optimize Strategy</button>
                    </div>
                </div>



            </div>

            <!-- Right panel for chart and trade details -->
            <div class="backtesting-chart-panel">
                <div class="chart-controls" style="margin-bottom: 10px;">
                    <button id="revert-chart-button" class="button compact-button" onclick="revertChartChanges()">Revert Chart</button>
                </div>
                <div class="chart-container">
                    <canvas id="price-chart"></canvas>
                    <canvas id="volume-chart"></canvas>
                </div>



                <!-- Trade results will be displayed here -->
                <div id="trade-results-container" class="trade-results-container">
                    <!-- Trade table will be inserted here by JavaScript -->
                    <div id="trade-summary" class="trade-summary">
                        <!-- Trade summary will be inserted here by JavaScript -->
                    </div>
                </div>

                <!-- Trade results Table -->
                <div id="backtest-results" class="results-section" style="margin-top: 20px;">
                    <h3>Handelsdetails</h3>
                    <div style="overflow-x: auto;"> <!-- Make table horizontally scrollable if needed -->
                        <table id="trade-details-table" class="results-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Type</th>
                                    <th>Inkoop</th>
                                    <th>Inkoopprijs</th>
                                    <th>Verkoop</th>
                                    <th>Verkoopprijs</th>
                                    <th>Resultaat (€)</th>
                                    <th>Resultaat (%)</th>
                                </tr>
                            </thead>
                            <tbody id="trade-details-body">
                                <!-- Trade rows will be inserted here by JavaScript -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="9" style="text-align: right; font-weight: bold;">Totaal resultaat (%):</td>
                                    <td id="total-pnl-percentage" style="font-weight: bold;">-</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <div id="detailed-trade-results"></div> <!-- This might be redundant now -->
                </div>
            </div>
        </div>

        <script>
            // Theme toggle and initialization functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Load strategies for backtesting
                const backtestingTab = document.querySelector('a[href="/?tab=Backtesting"]');
                if (backtestingTab) {
                    backtestingTab.addEventListener('click', function() {
                        console.log('Backtesting tab clicked, loading strategies');
                        // Call the function to load strategies
                        fetch('/available_python_strategies')
                            .then(response => response.json())
                            .then(strategies => {
                                console.log('Loaded strategies for backtesting:', strategies);
                                const backtestStrategySelect = document.getElementById('backtest-strategy');
                                if (backtestStrategySelect) {
                                    // Clear existing options except the first one
                                    while (backtestStrategySelect.options.length > 1) {
                                        backtestStrategySelect.remove(1);
                                    }

                                    // Add new options
                                    strategies.forEach(strategy => {
                                        const option = document.createElement('option');
                                        option.value = strategy.id;
                                        option.textContent = strategy.name;
                                        backtestStrategySelect.appendChild(option);
                                    });

                                    console.log('Updated backtest strategy select with', strategies.length, 'strategies');
                                }
                            })
                            .catch(error => console.error('Error loading strategies:', error));
                    });
                }

                // Also load strategies on page load if we're on the backtesting tab
                if (window.location.href.includes('tab=Backtesting')) {
                    console.log('On backtesting tab, loading strategies');
                    fetch('/available_python_strategies')
                        .then(response => response.json())
                        .then(strategies => {
                            console.log('Loaded strategies for backtesting:', strategies);
                            const backtestStrategySelect = document.getElementById('backtest-strategy');
                            if (backtestStrategySelect) {
                                // Clear existing options except the first one
                                while (backtestStrategySelect.options.length > 1) {
                                    backtestStrategySelect.remove(1);
                                }

                                // Add new options
                                strategies.forEach(strategy => {
                                    const option = document.createElement('option');
                                    option.value = strategy.id;
                                    option.textContent = strategy.name;
                                    backtestStrategySelect.appendChild(option);
                                });

                                console.log('Updated backtest strategy select with', strategies.length, 'strategies');
                            }
                        })
                        .catch(error => console.error('Error loading strategies:', error));
                }
                // Theme toggle
                const themeToggle = document.getElementById('theme-toggle');
                if (themeToggle) {
                    // Check for saved theme preference or use preferred color scheme
                    const savedTheme = localStorage.getItem('theme') ||
                        (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

                    // Apply saved theme
                    document.documentElement.setAttribute('data-theme', savedTheme);

                    // Update toggle button text
                    themeToggle.textContent = savedTheme === 'dark' ? 'Light Mode' : 'Dark Mode';

                    // Add click event
                    themeToggle.addEventListener('click', function() {
                        const currentTheme = document.documentElement.getAttribute('data-theme');
                        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                        // Update theme
                        document.documentElement.setAttribute('data-theme', newTheme);
                        localStorage.setItem('theme', newTheme);

                        // Update button text
                        themeToggle.textContent = newTheme === 'dark' ? 'Light Mode' : 'Dark Mode';

                        // Update charts with new theme colors if they exist
                        if (window.priceChart && window.volumeChart && window.getChartColors) {
                            const colors = window.getChartColors();

                            // Update price chart colors
                            if (window.priceChart.data && window.priceChart.data.datasets) {
                                // Update price line
                                if (window.priceChart.data.datasets[0]) {
                                    window.priceChart.data.datasets[0].borderColor = colors.price.borderColor;
                                    window.priceChart.data.datasets[0].backgroundColor = colors.price.backgroundColor;
                                }

                                // Update buy signals
                                if (window.priceChart.data.datasets.length > 1) {
                                    window.priceChart.data.datasets[1].backgroundColor = colors.buy.backgroundColor;
                                    window.priceChart.data.datasets[1].borderColor = colors.buy.borderColor;
                                }

                                // Update sell signals
                                if (window.priceChart.data.datasets.length > 2) {
                                    window.priceChart.data.datasets[2].backgroundColor = colors.sell.backgroundColor;
                                    window.priceChart.data.datasets[2].borderColor = colors.sell.borderColor;
                                }
                            }

                            // Update volume chart colors
                            if (window.volumeChart.data && window.volumeChart.data.datasets && window.volumeChart.data.datasets[0]) {
                                window.volumeChart.data.datasets[0].backgroundColor = colors.price.backgroundColor;
                                window.volumeChart.data.datasets[0].borderColor = colors.price.borderColor;
                            }

                            // Update text and grid colors for both charts
                            if (window.priceChart.options && window.priceChart.options.scales) {
                                // Update x-axis
                                if (window.priceChart.options.scales.x) {
                                    if (window.priceChart.options.scales.x.ticks) {
                                        window.priceChart.options.scales.x.ticks.color = colors.text.color;
                                    }
                                    if (window.priceChart.options.scales.x.grid) {
                                        window.priceChart.options.scales.x.grid.color = colors.grid.color;
                                    }
                                }

                                // Update y-axis
                                if (window.priceChart.options.scales.y) {
                                    if (window.priceChart.options.scales.y.ticks) {
                                        window.priceChart.options.scales.y.ticks.color = colors.text.color;
                                    }
                                    if (window.priceChart.options.scales.y.grid) {
                                        window.priceChart.options.scales.y.grid.color = colors.grid.color;
                                    }
                                    if (window.priceChart.options.scales.y.title) {
                                        window.priceChart.options.scales.y.title.color = colors.text.color;
                                    }
                                }
                            }

                            // Update legend colors
                            if (window.priceChart.options && window.priceChart.options.plugins &&
                                window.priceChart.options.plugins.legend &&
                                window.priceChart.options.plugins.legend.labels) {
                                window.priceChart.options.plugins.legend.labels.color = colors.text.color;
                            }

                            // Do the same for volume chart
                            if (window.volumeChart.options && window.volumeChart.options.scales) {
                                // Update axes
                                ['x', 'y'].forEach(axis => {
                                    if (window.volumeChart.options.scales[axis]) {
                                        if (window.volumeChart.options.scales[axis].ticks) {
                                            window.volumeChart.options.scales[axis].ticks.color = colors.text.color;
                                        }
                                        if (window.volumeChart.options.scales[axis].grid) {
                                            window.volumeChart.options.scales[axis].grid.color = colors.grid.color;
                                        }
                                        if (window.volumeChart.options.scales[axis].title) {
                                            window.volumeChart.options.scales[axis].title.color = colors.text.color;
                                        }
                                    }
                                });
                            }

                            // Update legend colors for volume chart
                            if (window.volumeChart.options && window.volumeChart.options.plugins &&
                                window.volumeChart.options.plugins.legend &&
                                window.volumeChart.options.plugins.legend.labels) {
                                window.volumeChart.options.plugins.legend.labels.color = colors.text.color;
                            }

                            // Update the charts
                            window.priceChart.update();
                            window.volumeChart.update();
                        }
                    });
                }

                // Charts are initialized in charts.js
            });

            // These functions are now in charts.js

            // Define the displayStatusMessage function directly in the page
            function displayStatusMessage(message, type = 'info') {
                const statusDiv = document.getElementById('status-message');
                if (statusDiv) {
                    statusDiv.textContent = message;
                    statusDiv.className = `status-message ${type}`; // Apply 'success' or 'error' class
                    statusDiv.style.display = 'block'; // Make sure it's visible

                    // Clear message after 5 seconds
                    setTimeout(() => {
                        statusDiv.textContent = '';
                        statusDiv.className = 'status-message';
                        statusDiv.style.display = 'none';
                    }, 5000);
                } else {
                    console.error('Status message div not found');
                    alert(message); // Fallback to alert
                }
            }

            // Set default dates when the page loads
            document.addEventListener('DOMContentLoaded', function() {
                // Set default dates (today and 30 days ago)
                const today = new Date();
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(today.getDate() - 30);

                document.getElementById('end-date').value = today.toISOString().split('T')[0];
                document.getElementById('start-date').value = thirtyDaysAgo.toISOString().split('T')[0];

                // Add event listeners for buttons
                const fetchDataButton = document.getElementById('fetch-data-button');
                if (fetchDataButton) {
                    fetchDataButton.addEventListener('click', fetchHistoricalData);
                }

                const runBacktestButton = document.getElementById('run-backtest-button');
                if (runBacktestButton) {
                    runBacktestButton.addEventListener('click', runBacktest);
                }

                const runOptimizationButton = document.getElementById('run-optimization-button');
                if (runOptimizationButton) {
                    runOptimizationButton.addEventListener('click', runOptimization);
                }
            });

            // Function to run backtest
            function runBacktest() {
                // Get parameters from form
                const pair = document.getElementById('backtest-pair').value;
                const strategy = document.getElementById('backtest-strategy').value;
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;

                if (!pair || !strategy || !startDate || !endDate) {
                    displayStatusMessage('Please fill in all fields', 'error');
                    return;
                }

                displayStatusMessage(`Running ${strategy} backtest on ${pair}...`, 'info');

                // Make the request to run backtest
                fetch(`/backtest?pair=${encodeURIComponent(pair)}&strategy=${encodeURIComponent(strategy)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    const contentType = response.headers.get('content-type');
                    if (!response.ok) {
                        // Try to get error details from response if possible
                        if (contentType && contentType.includes('application/json')) {
                            return response.json().then(errorData => {
                                throw new Error(errorData.error || `HTTP error! Status: ${response.status}`);
                            });
                        } else {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                    }

                    // Parse JSON response
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        throw new Error('Received non-JSON response from server');
                    }
                })
                .then(data => {
                    const tableBody = document.getElementById('trade-details-body');
                    const totalPnlCell = document.getElementById('total-pnl-percentage');

                    // Clear previous results
                    if (tableBody) tableBody.innerHTML = ''; // Clear previous table rows
                    if (totalPnlCell) {
                         totalPnlCell.textContent = '-';
                         totalPnlCell.style.color = ''; // Reset color
                    }


                    if (data.error) {
                        displayStatusMessage(`Backtest failed: ${data.error}`, 'error');
                    } else {
                        const stats = data.result || data;
                        const trades = stats.trades || [];

                        // Summary section removed

                        // --- Populate Trade Table ---
                        if (tableBody) {
                            trades.forEach(trade => {
                                const row = tableBody.insertRow();

                                const entryTime = new Date(trade.entry_timestamp || trade.entry_time).toLocaleString();
                                const exitTime = new Date(trade.exit_timestamp || trade.exit_time).toLocaleString();
                                const positionType = (trade.position || trade.position_type || 'N/A').toUpperCase();
                                const entryPrice = parseFloat(trade.entry_price || 0);
                                const exitPrice = parseFloat(trade.exit_price || 0);
                                const priceDiff = exitPrice - entryPrice;
                                // Try multiple common property names for PnL percentage
                                const pnlPercent = parseFloat(trade.pnl_percentage || trade.pnl_pct || trade.pnl_percent || trade.pnl || 0);
                                console.log('Trade PnL data:', {
                                    pnl_percentage: trade.pnl_percentage,
                                    pnl_pct: trade.pnl_pct,
                                    pnl_percent: trade.pnl_percent,
                                    pnl: trade.pnl,
                                    calculated: pnlPercent
                                });

                                // Add row number
                                row.insertCell().textContent = tableBody.rows.length;
                                // Add position type
                                row.insertCell().textContent = positionType;

                                // Format entry date/time in the requested format
                                const entryDateTime = new Date(trade.entry_timestamp || trade.entry_time);
                                const entryDateStr = entryDateTime.toLocaleDateString('nl-NL', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric'
                                });
                                const entryTimeStr = entryDateTime.toLocaleTimeString('nl-NL', {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit'
                                });
                                const entryDateTimeStr = `${entryDateStr} ${entryTimeStr}`;

                                // Format exit date/time in the requested format
                                const exitDateTime = new Date(trade.exit_timestamp || trade.exit_time);
                                const exitDateStr = exitDateTime.toLocaleDateString('nl-NL', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric'
                                });
                                const exitTimeStr = exitDateTime.toLocaleTimeString('nl-NL', {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit'
                                });
                                const exitDateTimeStr = `${exitDateStr} ${exitTimeStr}`;

                                // Add combined entry date/time cell
                                row.insertCell().textContent = entryDateTimeStr;
                                row.insertCell().textContent = '$' + entryPrice.toFixed(4); // Increased precision

                                // Add combined exit date/time cell
                                row.insertCell().textContent = exitDateTimeStr;
                                row.insertCell().textContent = '$' + exitPrice.toFixed(4); // Increased precision

                                // Add price difference in money
                                const priceDiffCell = row.insertCell();
                                priceDiffCell.textContent = (priceDiff >= 0 ? '+' : '-') + '$' + Math.abs(priceDiff).toFixed(2);
                                priceDiffCell.style.color = priceDiff >= 0 ? 'lightgreen' : 'salmon';

                                // Add price difference in percentage
                                const pnlPercentCell = row.insertCell();
                                // Calculate percentage manually if not available in trade object
                                let calculatedPnlPercent = pnlPercent;
                                if (Math.abs(calculatedPnlPercent) < 0.01) {
                                    // If pnlPercent is close to zero, calculate it manually
                                    if (positionType === 'LONG') {
                                        calculatedPnlPercent = (exitPrice - entryPrice) / entryPrice * 100;
                                    } else { // SHORT
                                        calculatedPnlPercent = (entryPrice - exitPrice) / entryPrice * 100;
                                    }
                                }
                                pnlPercentCell.textContent = (calculatedPnlPercent >= 0 ? '+' : '-') + Math.abs(calculatedPnlPercent).toFixed(2) + '%';
                                pnlPercentCell.style.color = calculatedPnlPercent >= 0 ? 'lightgreen' : 'salmon';

                                // Color the entire row based on profit/loss
                                row.style.backgroundColor = pnlPercent >= 0 ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)';
                            });
                        }

                        // --- Update Total PnL in Footer ---
                        if (totalPnlCell) {
                            // Try multiple common property names for total PnL percentage
                            const totalPnlValue = parseFloat(stats.total_pnl_pct || stats.total_pnl || 0);
                            totalPnlCell.textContent = totalPnlValue.toFixed(2);
                            totalPnlCell.style.color = totalPnlValue >= 0 ? 'lightgreen' : 'salmon';
                        }

                        // --- Update Charts ---
                        // Pass the 'stats' object which correctly contains 'result.trades'
                        if (window.historicalData && stats) {
                            console.log(`Updating charts with ${window.historicalData.length} data points and backtest results object:`, stats);
                            updateChartsWithBacktestResults(window.historicalData, stats); // Pass the 'stats' object
                        } else {
                            console.warn('Historical data or processed backtest stats missing, cannot update chart signals.');
                            if (window.historicalData) {
                                createPriceChart(window.historicalData);
                            }
                        }

                        displayStatusMessage(`Backtest completed successfully!`, 'success');
                    }
                })
                .catch(error => {
                    // Also clear table on error
                    const tableBody = document.getElementById('trade-details-body');
                     if (tableBody) tableBody.innerHTML = '';
                     const totalPnlCell = document.getElementById('total-pnl-percentage');
                     if (totalPnlCell) {
                         totalPnlCell.textContent = '-';
                         totalPnlCell.style.color = ''; // Reset color
                     }

                    console.error('Error running backtest:', error);
                    displayStatusMessage(`Error running backtest: ${error.message}`, 'error');
                });
            }

            // Function to run optimization
            function runOptimization() {
                // Get parameters from form
                const pair = document.getElementById('backtest-pair').value;
                const strategy = document.getElementById('backtest-strategy').value;
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                const iterations = document.getElementById('optimization-iterations').value;

                if (!pair || !strategy || !startDate || !endDate || !iterations) {
                    displayStatusMessage('Please fill in all fields', 'error');
                    return;
                }

                displayStatusMessage(`Optimizing ${strategy} on ${pair} with ${iterations} iterations...`, 'info');

                // Make the request to run optimization
                fetch(`/optimize?pair=${encodeURIComponent(pair)}&strategy=${encodeURIComponent(strategy)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}&iterations=${encodeURIComponent(iterations)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    const contentType = response.headers.get('content-type');
                    if (!response.ok) {
                        // Try to get error details from response if possible
                        if (contentType && contentType.includes('application/json')) {
                            return response.json().then(errorData => {
                                throw new Error(errorData.error || `HTTP error! Status: ${response.status}`);
                            });
                        } else {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                    }

                    // Parse JSON response
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        throw new Error('Received non-JSON response from server');
                    }
                })
                .then(data => {
                    if (data.error) {
                        displayStatusMessage(`Optimization failed: ${data.error}`, 'error');
                    } else {
                        // Display success message
                        displayStatusMessage(`Optimization completed successfully!`, 'success');

                        // Display optimization results in a status message
                        let resultMessage = `Optimization Results for ${strategy}\n`;
                        resultMessage += `Iterations: ${iterations}\n\n`;
                        if (data.best_params) {
                            resultMessage += 'Best Parameters:\n';
                            for (const [key, value] of Object.entries(data.best_params)) {
                                resultMessage += `  ${key}: ${value}\n`;
                            }
                            resultMessage += `\nBest Performance (Total PnL %): ${(data.best_performance || 0).toFixed(2)}%\n`;
                        } else {
                            resultMessage += 'No best parameters found.\n';
                        }

                        // Show the optimization results in a more visible way
                        alert(resultMessage);
                        // Clear the trade table after optimization
                        const tableBody = document.getElementById('trade-details-body');
                        if (tableBody) tableBody.innerHTML = '';
                        const totalPnlCell = document.getElementById('total-pnl-percentage');
                        if (totalPnlCell) {
                             totalPnlCell.textContent = '-';
                             totalPnlCell.style.color = '';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error running optimization:', error);
                    displayStatusMessage(`Error running optimization: ${error.message}`, 'error');
                });
            }

            // Function to fetch historical data with progress bar
            function fetchHistoricalData() {
                const pair = document.getElementById('backtest-pair').value;
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;

                if (!pair || !startDate || !endDate) {
                    displayStatusMessage('Please fill in all fields', 'error');
                    return;
                }

                // Show progress bar
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');
                const downloadProgress = document.getElementById('download-progress');

                if (downloadProgress) {
                    downloadProgress.style.display = 'block';
                    progressBar.style.width = '0%';
                    progressText.textContent = '0%';
                }

                // Simulate progress with a more realistic pattern
                let progress = 0;
                const progressInterval = setInterval(() => {
                    // Start slow, then speed up, then slow down again
                    if (progress < 30) {
                        progress += 5; // Fast at start
                    } else if (progress < 60) {
                        progress += 2; // Slower in middle
                    } else if (progress < 85) {
                        progress += 1; // Very slow near end
                    } else {
                        // Stop at 85% and wait for actual completion
                        clearInterval(progressInterval);
                    }
                    progressBar.style.width = progress + '%';
                    progressText.textContent = progress + '%';
                }, 200);

                // Make the actual request
                fetch(`/fetch_historical_data?pair=${encodeURIComponent(pair)}&start_date=${encodeURIComponent(startDate)}&end_date=${encodeURIComponent(endDate)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Clear the progress interval
                    clearInterval(progressInterval);

                    // Complete the progress bar
                    progressBar.style.width = '100%';
                    progressText.textContent = '100%';

                    // Hide progress bar after a short delay
                    setTimeout(() => {
                        if(downloadProgress) downloadProgress.style.display = 'none';
                        // Reset progress bar style
                        if(progressBar) progressBar.style.backgroundColor = ''; // Reset color if it was set to red on error
                    }, 1000);

                    // Check if data has the expected structure
                    if (!data.data || !Array.isArray(data.data) || data.data.length === 0) {
                        console.error('Invalid data format received:', data);
                        displayStatusMessage('Error: Invalid data format received from server', 'error');
                        return;
                    }

                    // Store the data globally
                    window.historicalData = data.data;

                    // Create chart with the fetched data
                    try {
                        createPriceChart(window.historicalData);
                        displayStatusMessage(`Successfully downloaded ${window.historicalData.length} data points for ${pair}`, 'success');
                    } catch (chartError) {
                        console.error('Error creating chart:', chartError);
                        displayStatusMessage(`Data downloaded but error creating chart: ${chartError.message}`, 'error');
                    }
                })
                .catch(error => {
                    // Clear the progress interval
                    clearInterval(progressInterval);

                    // Show error in progress bar
                    if (progressBar) progressBar.style.width = '100%';
                    if (progressBar) progressBar.style.backgroundColor = 'red';
                    if (progressText) progressText.textContent = 'Error!';

                    // Hide progress bar after a short delay
                    setTimeout(() => {
                        if (downloadProgress) downloadProgress.style.display = 'none';
                         if(progressBar) progressBar.style.backgroundColor = ''; // Reset color
                    }, 2000);

                    console.error('Error fetching data:', error);
                    displayStatusMessage(`Error downloading data: ${error.message}. Please try again.`, 'error');
                });
            }
        </script>

        <!-- Auto-optimization section moved to a tab or modal if needed -->
        <div class="auto-optimization-section" style="display: none;">
            <h3>Automatic Optimization</h3>
            <div class="form-group">
                <label for="auto-opt-pair">Trading Pair:</label>
                <select id="auto-opt-pair" name="pair">
                    <option value="">-- Select Pair --</option>
                </select>
            </div>

            <div class="form-group">
                <label for="lookback-days">Lookback Period (days):</label>
                <input type="number" id="lookback-days" name="lookback_days" min="1" value="30">
            </div>

            <div class="button-group">
                <button type="button" id="start-auto-opt-button">Start Auto-Optimization</button>
                <button type="button" id="stop-auto-opt-button">Stop Auto-Optimization</button>
            </div>

            <div id="auto-opt-status" class="status-section">
                <h4>Status</h4>
                <p>Status: <span id="opt-status-text">Not running</span></p>
                <p>Queue Size: <span id="opt-queue-size">0</span></p>
            </div>

            <div id="auto-opt-results" class="results-section">
                <h4>Latest Results</h4>
                <div id="opt-results-list"></div>
            </div>
        </div>

        <div id="optimization-results" class="results-section" style="display: none;">
            <!-- Optimization results will be displayed here -->
        </div>
    </div>

    <!-- Strategy-bot Tab -->
    <div id="StrategyBot" class="tab-content" style="display: {% if active_tab == 'StrategyBot' %}block{% else %}none{% endif %};">
        <!-- Save Strategy Modal -->
        <div id="saveStrategyModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.7);">
            <div class="modal-content" style="background-color: #333; margin: 15% auto; padding: 20px; border: 1px solid #444; width: 50%; border-radius: 5px; color: white;">
                <span class="close" style="color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
                <h2>Strategie opslaan</h2>
                <p>Geef een naam voor de nieuwe strategie:</p>
                <input type="text" id="newStrategyName" placeholder="Nieuwe strategie naam" class="input-field" style="width: 100%; padding: 8px; margin: 10px 0; background-color: #444; color: white; border: 1px solid #555;">
                <input type="hidden" id="strategyIdToSave">
                <input type="hidden" id="strategyParamsToSave">
                <div class="modal-buttons" style="margin-top: 20px; text-align: right;">
                    <button id="saveStrategyButton" class="button">Opslaan</button>
                    <button id="cancelSaveButton" class="button">Annuleren</button>
                </div>
            </div>
        </div>
        <h2>Strategy-bot</h2>
        <p>De Strategy-bot optimaliseert een strategie door parameters aan te passen om de meest winstgevende configuratie te vinden.</p>

        <div class="strategy-bot-container" style="display: flex; gap: 20px;">
            <!-- Left panel for controls -->
            <div class="strategy-bot-controls" style="flex: 1;">
                <div class="control-section">
                    <h3>1. Configuratie</h3>
                    <div class="form-group compact">
                        <label for="strategy-bot-pair">Trading Pair:</label>
                        <select id="strategy-bot-pair" class="compact-input">
                            <option value="BTC-USD">BTC-USD</option>
                            <option value="ETH-USD">ETH-USD</option>
                            <option value="SOL-USD">SOL-USD</option>
                            <option value="AVAX-USD">AVAX-USD</option>
                            <option value="LINK-USD">LINK-USD</option>
                        </select>
                    </div>

                    <div class="form-group compact">
                        <label for="strategy-bot-strategy">Strategie:</label>
                        <select id="strategy-bot-strategy" class="compact-input">
                            <option value="">-- Selecteer een strategie --</option>
                            <!-- Options will be populated by JS -->
                        </select>
                    </div>

                    <div class="form-group compact">
                        <label for="strategy-bot-iterations">Aantal iteraties:</label>
                        <input type="number" id="strategy-bot-iterations" class="compact-input" value="20" min="1" max="100">
                    </div>
                    <div class="form-group compact">
                        <label for="strategy-bot-generations">Aantal generaties:</label>
                        <input type="number" id="strategy-bot-generations" class="compact-input" value="10" min="1" max="50">
                    </div>
                    <div class="form-group compact">
                        <label for="strategy-bot-population">Populatiegrootte:</label>
                        <input type="number" id="strategy-bot-population" class="compact-input" value="20" min="5" max="100">
                    </div>
                    <div class="button-group">
                        <button type="button" id="start-strategy-bot-button" class="button">Start Strategy-bot</button>
                        <button type="button" id="stop-strategy-bot-button" class="button">Stop Strategy-bot</button>
                        <button type="button" id="reset-strategy-bot-button" class="button">Reset Resultaten</button>
                        <button type="button" id="reset-all-versions-button" class="button">Reset Alle Versies</button>
                    </div>
                </div>

                <div class="control-section">
                    <h3>2. Status</h3>
                    <div id="strategy-bot-status" class="status-section">
                        <p>Status: <span id="strategy-bot-status-text">Niet actief</span></p>
                        <p>Voortgang: <span id="strategy-bot-progress">0%</span></p>
                        <div class="progress-bar-container" style="height: 20px; background-color: #333; border-radius: 4px; overflow: hidden; margin-top: 5px;">
                            <div class="progress-bar" id="strategy-bot-progress-bar" style="width: 0%; height: 100%; background-color: #4CAF50; transition: width 0.3s ease;"></div>
                        </div>
                        <div id="current-performance-container" style="margin-top: 15px;">
                            <h4>Huidige Optimalisatie:</h4>
                            <p>Prestatie: <span id="current-performance" style="font-weight: bold;">0.00%</span></p>
                            <p>Win Ratio: <span id="current-win-ratio" style="font-weight: bold;">0.00%</span></p>
                            <p>Aantal Trades: <span id="current-trades" style="font-weight: bold;">0</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right panel for visualization and results -->
            <div class="strategy-bot-results" style="flex: 2;">
                <!-- High-tech visualization container -->
                <div id="strategy-bot-visualization" class="strategy-bot-visualization" style="margin-bottom: 20px;">
                    <!-- Canvas will be inserted here by JavaScript -->
                </div>

                <h3>Resultaten</h3>
                <div style="overflow-x: auto;">
                    <table id="strategy-bot-results-table" class="results-table">
                        <thead>
                            <tr>
                                <th>Versie</th>
                                <th>Datum/Tijd</th>
                                <th>Strategie</th>
                                <th>Parameters</th>
                                <th>Resultaat (%)</th>
                                <th>Win Ratio (%)</th>
                                <th>Aantal Trades</th>
                                <th>Acties</th>
                            </tr>
                        </thead>
                        <tbody id="strategy-bot-results-body">
                            <!-- Results will be inserted here by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <script>

            document.addEventListener('DOMContentLoaded', function() {
                // Initialize the Strategy-bot visualization
                if (window.strategyBotVisualization && typeof window.strategyBotVisualization.init === 'function') {

                    window.strategyBotVisualization.init();
                }

                // Setup modal event listeners
                const modal = document.getElementById('saveStrategyModal');
                const closeBtn = modal.querySelector('.close');
                const saveBtn = document.getElementById('saveStrategyButton');
                const cancelBtn = document.getElementById('cancelSaveButton');

                // Close the modal when clicking the X
                closeBtn.addEventListener('click', function() {
                    modal.style.display = 'none';
                });

                // Close the modal when clicking outside of it
                window.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        modal.style.display = 'none';
                    }
                });

                // Save the strategy when clicking the save button
                saveBtn.addEventListener('click', saveStrategy);

                // Close the modal when clicking the cancel button
                cancelBtn.addEventListener('click', function() {
                    modal.style.display = 'none';
                });

                // Handle Enter key in the name input field
                document.getElementById('newStrategyName').addEventListener('keyup', function(event) {
                    if (event.key === 'Enter') {
                        saveStrategy();
                    }
                });

                // Initialize visualization
                if (window.strategyBotVisualization && typeof window.strategyBotVisualization.init === 'function') {
                    window.strategyBotVisualization.init();
                }

                // Add event listeners for Strategy-bot buttons
                const startStrategyBotButton = document.getElementById('start-strategy-bot-button');
                if (startStrategyBotButton) {
                    startStrategyBotButton.addEventListener('click', startStrategyBot);
                }

                const stopStrategyBotButton = document.getElementById('stop-strategy-bot-button');
                if (stopStrategyBotButton) {
                    stopStrategyBotButton.addEventListener('click', stopStrategyBot);
                }

                const resetStrategyBotButton = document.getElementById('reset-strategy-bot-button');
                if (resetStrategyBotButton) {
                    resetStrategyBotButton.addEventListener('click', resetStrategyBot);
                }

                const resetAllVersionsButton = document.getElementById('reset-all-versions-button');
                if (resetAllVersionsButton) {
                    resetAllVersionsButton.addEventListener('click', resetAllStrategyVersions);
                }

                // Load existing results when the tab is opened
                const strategyBotTab = document.querySelector('a[href="/?tab=StrategyBot"]');
                if (strategyBotTab) {
                    strategyBotTab.addEventListener('click', function() {
                        loadStrategyBotResults();
                        loadStrategiesForStrategyBot();
                    });
                }

                // Also load results if we're already on the Strategy-bot tab
                if (window.location.href.includes('tab=StrategyBot')) {
                    loadStrategyBotResults();
                    loadStrategiesForStrategyBot();
                }
            });

            // Function to load strategies for the Strategy-bot
            function loadStrategiesForStrategyBot() {
                fetch('/available_python_strategies')
                    .then(response => response.json())
                    .then(strategies => {
                        console.log('Loaded strategies for Strategy-bot:', strategies);
                        const strategySelect = document.getElementById('strategy-bot-strategy');

                        if (strategySelect) {
                            // Clear existing options except the first one
                            while (strategySelect.options.length > 1) {
                                strategySelect.remove(1);
                            }

                            // Add new options
                            strategies.forEach(strategy => {
                                const option = document.createElement('option');
                                option.value = strategy.id;
                                option.textContent = strategy.name;
                                strategySelect.appendChild(option);
                            });

                            console.log('Updated Strategy-bot strategy select with', strategies.length, 'strategies');
                        }
                    })
                    .catch(error => console.error('Error loading strategies for Strategy-bot:', error));
            }

            // Function to start the Strategy-bot
            function startStrategyBot() {
                const pair = document.getElementById('strategy-bot-pair').value;
                const strategy = document.getElementById('strategy-bot-strategy').value;
                const iterations = document.getElementById('strategy-bot-iterations').value;
                const generations = document.getElementById('strategy-bot-generations').value;
                const population = document.getElementById('strategy-bot-population').value;

                if (!pair || !iterations || !generations || !population) {
                    displayStatusMessage('Vul alle velden in', 'error');
                    return;
                }

                if (!strategy) {
                    displayStatusMessage('Selecteer een strategie', 'error');
                    return;
                }

                displayStatusMessage(`Strategy-bot wordt gestart voor ${pair} met ${strategy}...`, 'info');

                // Update status
                document.getElementById('strategy-bot-status-text').textContent = 'Bezig';
                document.getElementById('strategy-bot-progress').textContent = '0%';
                document.getElementById('strategy-bot-progress-bar').style.width = '0%';

                // Make the request to start the Strategy-bot
                fetch('/strategy_bot/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        pair: pair,
                        strategy: strategy,
                        iterations: parseInt(iterations),
                        generations: parseInt(generations),
                        population_size: parseInt(population)
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        displayStatusMessage(`Strategy-bot fout: ${data.error}`, 'error');
                        document.getElementById('strategy-bot-status-text').textContent = 'Fout';
                    } else {
                        displayStatusMessage(`Strategy-bot gestart: ${data.message}`, 'success');
                        // Start polling for status updates
                        pollStrategyBotStatus();
                    }
                })
                .catch(error => {
                    console.error('Error starting Strategy-bot:', error);
                    displayStatusMessage(`Fout bij starten Strategy-bot: ${error.message}`, 'error');
                    document.getElementById('strategy-bot-status-text').textContent = 'Fout';
                });
            }

            // Function to stop the Strategy-bot
            function stopStrategyBot() {
                displayStatusMessage('Strategy-bot wordt gestopt...', 'info');

                fetch('/strategy_bot/stop', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    displayStatusMessage(`Strategy-bot gestopt: ${data.message}`, 'success');
                    document.getElementById('strategy-bot-status-text').textContent = 'Gestopt';
                })
                .catch(error => {
                    console.error('Error stopping Strategy-bot:', error);
                    displayStatusMessage(`Fout bij stoppen Strategy-bot: ${error.message}`, 'error');
                });
            }

            // Function to poll for Strategy-bot status updates
            function pollStrategyBotStatus() {
                fetch('/strategy_bot/status', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Strategy-bot status update:', data);

                    // Update status display
                    document.getElementById('strategy-bot-status-text').textContent = data.status;
                    document.getElementById('strategy-bot-progress').textContent = `${data.progress}%`;
                    document.getElementById('strategy-bot-progress-bar').style.width = `${data.progress}%`;

                    // Update current performance metrics if available
                    const perfElement = document.getElementById('current-performance');
                    const winRatioElement = document.getElementById('current-win-ratio');
                    const tradesElement = document.getElementById('current-trades');
                    const calculatingText = "Calculating..."; // Text to display when value is null

                    if (data.current_performance !== undefined && data.current_performance !== null) {
                        const performanceValue = Number(data.current_performance);
                        perfElement.textContent = `${performanceValue.toFixed(2)}%`;
                        perfElement.style.color = performanceValue >= 0 ? 'lightgreen' : 'salmon';
                        console.log('Current performance updated:', typeof performanceValue, performanceValue, 'from original:', typeof data.current_performance, data.current_performance);
                    } else {
                        perfElement.textContent = calculatingText;
                        perfElement.style.color = ''; // Reset color
                        console.log('Current performance is null/undefined, showing calculating text.');
                    }

                    if (data.current_win_ratio !== undefined && data.current_win_ratio !== null) {
                        const winRatioValue = Number(data.current_win_ratio);
                        winRatioElement.textContent = `${winRatioValue.toFixed(2)}%`;
                        winRatioElement.style.color = winRatioValue >= 50 ? 'lightgreen' : 'salmon';
                        console.log('Current win ratio updated:', winRatioValue);
                    } else {
                        winRatioElement.textContent = calculatingText;
                        winRatioElement.style.color = ''; // Reset color
                        console.log('Current win ratio is null/undefined, showing calculating text.');
                    }

                    if (data.current_trades !== undefined && data.current_trades !== null) {
                        const tradesValue = Number(data.current_trades);
                        tradesElement.textContent = tradesValue;
                        console.log('Current trades updated:', tradesValue);
                    } else {
                        tradesElement.textContent = calculatingText;
                        console.log('Current trades is null/undefined, showing calculating text.');
                    }

                    // If there are new results, update the results table
                    if (data.results && data.results.length > 0) {
                        updateStrategyBotResults(data.results);
                    }

                    // Continue polling if still running
                    if (data.status === 'Bezig' || data.running === true) {
                        setTimeout(pollStrategyBotStatus, 1000); // Poll every 1 second for more responsive updates
                    } else {
                        console.log('Strategy-bot stopped polling, status:', data.status);
                    }
                })
                .catch(error => {
                    console.error('Error polling Strategy-bot status:', error);
                    // Try again after a delay
                    setTimeout(pollStrategyBotStatus, 5000);
                });
            }

            // Function to load Strategy-bot results
            function loadStrategyBotResults() {
                fetch('/strategy_bot/results', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    updateStrategyBotResults(data.results);
                })
                .catch(error => {
                    console.error('Error loading Strategy-bot results:', error);
                    displayStatusMessage(`Fout bij laden resultaten: ${error.message}`, 'error');
                });
            }

            // Function to update the Strategy-bot results table is now in strategy_bot.js

            // Function to use a Strategy-bot result is now in strategy_bot.js

            // Function to show the save strategy modal
            function showSaveStrategyModal(strategyId, parameters) {
                // Set the strategy ID and parameters in hidden fields
                document.getElementById('strategyIdToSave').value = strategyId;
                document.getElementById('strategyParamsToSave').value = JSON.stringify(parameters);

                // Show the modal
                const modal = document.getElementById('saveStrategyModal');
                modal.style.display = 'block';

                // Focus on the name input
                document.getElementById('newStrategyName').focus();
            }

            // Function to save the strategy
            function saveStrategy() {
                const strategyId = document.getElementById('strategyIdToSave').value;
                const parameters = JSON.parse(document.getElementById('strategyParamsToSave').value);
                const newName = document.getElementById('newStrategyName').value.trim();

                if (!newName) {
                    alert('Geef een naam op voor de nieuwe strategie');
                    return;
                }

                // Make the request to save the strategy
                fetch('/strategy_bot/save_strategy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        strategy_id: strategyId,
                        new_name: newName,
                        parameters: parameters
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        displayStatusMessage(`Fout bij opslaan strategie: ${data.error}`, 'error');
                    } else {
                        displayStatusMessage(`Strategie opgeslagen als ${newName}`, 'success');
                        // Close the modal
                        document.getElementById('saveStrategyModal').style.display = 'none';
                        // Clear the input field
                        document.getElementById('newStrategyName').value = '';
                    }
                })
                .catch(error => {
                    console.error('Error saving strategy:', error);
                    displayStatusMessage(`Fout bij opslaan strategie: ${error.message}`, 'error');
                });
            }

            // Function to reset all Strategy-bot results
            function resetStrategyBot() {
                if (confirm('Weet je zeker dat je alle Strategy-bot resultaten wilt verwijderen? Dit kan niet ongedaan worden gemaakt.')) {
                    displayStatusMessage('Bezig met het verwijderen van alle resultaten...', 'info');

                    fetch('/strategy_bot/reset', {
                        method: 'POST',
                        headers: {
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        displayStatusMessage(`${data.message}`, 'success');
                        document.getElementById('strategy-bot-status-text').textContent = 'Niet actief';
                        document.getElementById('strategy-bot-progress').textContent = '0%';
                        document.getElementById('strategy-bot-progress-bar').style.width = '0%';

                        // Clear the results table
                        const tableBody = document.getElementById('strategy-bot-results-body');
                        if (tableBody) {
                            tableBody.innerHTML = '';
                        }
                    })
                    .catch(error => {
                        console.error('Error resetting Strategy-bot results:', error);
                        displayStatusMessage(`Fout bij het resetten van resultaten: ${error.message}`, 'error');
                    });
                }
            }

            // Function to reset all strategy versions
            function resetAllStrategyVersions() {
                if (confirm('Weet je zeker dat je alle strategie versies wilt verwijderen? Dit verwijdert alle geoptimaliseerde strategieën en kan niet ongedaan worden gemaakt.')) {
                    displayStatusMessage('Bezig met het verwijderen van alle strategie versies...', 'info');

                    fetch('/strategy_bot/reset_all_versions', {
                        method: 'POST',
                        headers: {
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        displayStatusMessage(`${data.message}`, 'success');
                        document.getElementById('strategy-bot-status-text').textContent = 'Niet actief';
                        document.getElementById('strategy-bot-progress').textContent = '0%';
                        document.getElementById('strategy-bot-progress-bar').style.width = '0%';

                        // Clear the results table
                        const tableBody = document.getElementById('strategy-bot-results-body');
                        if (tableBody) {
                            tableBody.innerHTML = '';
                        }

                        // Reload strategies to reflect the changes
                        loadStrategiesForStrategyBot();
                    })
                    .catch(error => {
                        console.error('Error resetting strategy versions:', error);
                        displayStatusMessage(`Fout bij het verwijderen van strategie versies: ${error.message}`, 'error');
                    });
                }
            }
        </script>
    </div>

    <!-- Live Trading Tab -->
    <div id="LiveTrading" class="tab-content" style="display: {% if active_tab == 'LiveTrading' %}block{% else %}none{% endif %};">
        <h2>Live Trading</h2>
        <label for="live-pair">Select Pair:</label>
        <select id="live-pair">
            <!-- Options will be populated dynamically -->
        </select>
        <br>
        <label for="live-strategy">Select Strategy:</label>
        <select id="live-strategy">
            <option value="simple_sma_cross">Simple SMA Crossover</option>
            <option value="rsi_strategy">RSI Strategy</option>
            <option value="pine_simple_ma_cross">Pine: Simple MA Cross</option>
            <option value="pine_rsi_strategy">Pine: RSI Strategy</option>
            <option value="pine_bollinger_bands">Pine: Bollinger Bands</option>
            <option value="pine_macd_strategy">Pine: MACD Strategy</option>
        </select>
        <br>
        <label for="capital">Max Capital ($):</label>
        <input type="number" id="capital" value="1000">
        <br>
        <label for="take-profit">Take Profit (%):</label>
        <input type="number" id="take-profit" step="0.1" value="5.0">
        <br>
        <label for="stop-loss">Stop Loss (%):</label>
        <input type="number" id="stop-loss" step="0.1" value="2.0">
        <br>
        <button id="start-live-button">Start Live Trading</button>
        <button id="stop-live-button">Stop Live Trading</button>
        <div id="live-status">
            <h3>Live Status:</h3>
            <p>Not running.</p>
            <!-- Log messages or status updates here -->
        </div>
    </div>

    <!-- Chart.js library for charts and candlestick charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0/dist/chartjs-adapter-luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial@0.1.1/dist/chartjs-chart-financial.min.js"></script>

    <!-- Charts script is already included in the <head> -->

    <!-- Main application script -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/strategy_bot_visualization.js') }}"></script>
    <script src="{{ url_for('static', filename='js/strategy_bot.js') }}"></script>
    <script>
        // Strategy descriptions and parameters (assuming these are passed from Flask)
        const strategyDescriptions = {{ strategy_descriptions|tojson|safe }};
        const strategyParams = {{ STRATEGY_PARAMS|tojson|safe }};

        // Update parameters when strategy changes (assuming a select element with name="strategy" exists elsewhere)
        // Example: document.querySelector('select[name="strategy"]').addEventListener(...)
        // This part might need adjustment based on where the strategy selection happens if not in this file.
        const strategySelectElement = document.querySelector('select[name="strategy"]'); // Adjust selector if needed
        if (strategySelectElement) {
            strategySelectElement.addEventListener('change', function() {
                const strategy = this.value;
                const paramsContainer = document.getElementById('params-container'); // Adjust ID if needed
                if (paramsContainer) {
                    paramsContainer.innerHTML = ''; // Clear previous params

                    if (strategyParams[strategy]) {
                        for (const [param, value] of Object.entries(strategyParams[strategy])) {
                            const div = document.createElement('div');
                            div.className = 'param-input';
                            div.innerHTML = `
                                <label>${param}:</label>
                                <input type="number" name="params[${param}]" value="${value}" step="0.1">
                            `;
                            paramsContainer.appendChild(div);
                        }
                    }
                }
            });
            // Trigger initial load if needed
            // strategySelectElement.dispatchEvent(new Event('change'));
        }


        // Handle Config form submission
        const configForm = document.getElementById('config-form');
        if (configForm) {
            configForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                try {
                    const response = await fetch('/config', {
                        method: 'POST',
                        body: formData
                    });
                    const result = await response.json();
                    displayStatusMessage(result.message || 'Configuration saved.', result.success ? 'success' : 'error');
                } catch (error) {
                     displayStatusMessage(`Error saving config: ${error}`, 'error');
                }
            });
        }

         // Handle Test Keys button
        const testApiButton = document.getElementById('test-api-button');
        if (testApiButton) {
            testApiButton.addEventListener('click', async function() {
                displayStatusMessage('Testing API keys...', 'info');
                try {
                    const response = await fetch('/test_api_keys');
                    const result = await response.json();
                     displayStatusMessage(result.message || 'API test finished.', result.success ? 'success' : 'error');
                } catch (error) {
                    displayStatusMessage(`Error testing keys: ${error}`, 'error');
                }
            });
        }


        // Handle Backtest form submission (assuming a form with id 'backtest-form' exists)
        // This seems to be handled by the runBacktest function triggered by the button click,
        // so this separate listener might be redundant or incorrect depending on the exact HTML structure.
        // If there's no form with id 'backtest-form', this listener should be removed.
        const backtestForm = document.getElementById('backtest-form');
        if (backtestForm) {
            backtestForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                // This logic seems duplicated in runBacktest(). Consider consolidating.
                console.warn("Redundant backtest form listener triggered. Check HTML structure.");
                // runBacktest(); // Call the main function instead?
            });
        }

    </script>
    </div> <!-- Close container -->
</body>
</html>
