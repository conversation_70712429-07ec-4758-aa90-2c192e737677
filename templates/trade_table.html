<!DOCTYPE html>
<html>
<head>
    <title>Trade Results</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .trade-summary {
            margin-top: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        .trade-summary h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }

        .summary-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #444;
        }

        .summary-table td:first-child {
            font-weight: bold;
            width: 40%;
        }

        .trade-table-container {
            margin-top: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            overflow-x: auto;
        }

        .trade-table-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .trade-table {
            width: 100%;
            border-collapse: collapse;
        }

        .trade-table th {
            background-color: #333;
            color: #f0f0f0;
            font-weight: bold;
            padding: 10px;
            text-align: left;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .trade-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #444;
        }

        .profit {
            color: #4CAF50;
        }

        .loss {
            color: #f44336;
        }

        .profit-row {
            background-color: rgba(76, 175, 80, 0.1);
        }

        .loss-row {
            background-color: rgba(244, 67, 54, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="trade-summary">
            <h3>Trade Summary</h3>
            <table class="summary-table">
                <tr>
                    <td>Strategy:</td>
                    <td>{{ strategy_name }}</td>
                </tr>
                <tr>
                    <td>Total Trades:</td>
                    <td>{{ metrics.number_of_trades }}</td>
                </tr>
                <tr>
                    <td>Win Ratio:</td>
                    <td>{{ metrics.win_ratio_pct }}%</td>
                </tr>
                <tr>
                    <td>Total P/L:</td>
                    <td class="{% if metrics.total_pnl_pct >= 0 %}profit{% else %}loss{% endif %}">{{ metrics.total_pnl_pct }}%</td>
                </tr>
                <tr>
                    <td>Initial Capital:</td>
                    <td>${{ metrics.initial_capital }}</td>
                </tr>
                <tr>
                    <td>Final Capital:</td>
                    <td>${{ metrics.final_capital }}</td>
                </tr>
                <tr>
                    <td>Growth:</td>
                    <td class="{% if metrics.capital_growth_pct >= 0 %}profit{% else %}loss{% endif %}">{{ metrics.capital_growth_pct }}%</td>
                </tr>
            </table>
        </div>

        <div class="trade-table-container">
            <h3>Trade Details</h3>
            <table class="trade-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Type</th>
                        <th>Inkoopdatum</th>
                        <th>Inkooptijd</th>
                        <th>Inkoopprijs</th>
                        <th>Verkoopdatum</th>
                        <th>Verkooptijd</th>
                        <th>Verkoopprijs</th>
                        <th>Verschil (€)</th>
                        <th>Resultaat (%)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trade in trades %}
                    {% set entry_time = trade.entry_time|timestamp_to_datetime %}
                    {% set exit_time = trade.exit_time|timestamp_to_datetime %}
                    {% set price_diff = trade.exit_price - trade.entry_price %}
                    {% set price_diff_pct = (price_diff / trade.entry_price) * 100 %}
                    {% set row_class = 'profit-row' if trade.pnl_amount > 0 else 'loss-row' %}
                    {% set diff_class = 'profit' if price_diff > 0 else 'loss' %}
                    <tr class="{{ row_class }}">
                        <td>{{ loop.index }}</td>
                        <td>{{ trade.position_type|upper }}</td>
                        <td>{{ entry_time.strftime('%d-%m-%Y') }}</td>
                        <td>{{ entry_time.strftime('%H:%M') }}</td>
                        <td>${{ "%.4f"|format(trade.entry_price) }}</td>
                        <td>{{ exit_time.strftime('%d-%m-%Y') }}</td>
                        <td>{{ exit_time.strftime('%H:%M') }}</td>
                        <td>${{ "%.4f"|format(trade.exit_price) }}</td>
                        <td class="{{ diff_class }}">{{ '+' if price_diff > 0 else '' }}${{ "%.2f"|format(price_diff) }}</td>
                        <td class="{{ diff_class }}">{{ '+' if price_diff_pct > 0 else '' }}{{ "%.2f"|format(price_diff_pct) }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
