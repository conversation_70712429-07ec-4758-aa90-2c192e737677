<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strategy Management</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #f0f0f0;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .strategy-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .strategy-card {
            background-color: #2a2a2a;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .strategy-card:hover {
            transform: translateY(-5px);
        }
        .strategy-card h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        .strategy-card p {
            margin-bottom: 15px;
            color: #ffffff; /* Changed from #ccc to white for better readability */
        }
        .strategy-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        .btn-secondary {
            background-color: #2196F3;
            color: white;
        }
        .btn-danger {
            background-color: #f44336;
            color: white;
        }
        .form-container {
            background-color: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #444;
            border-radius: 4px;
            background-color: #333;
            color: #fff;
        }
        textarea.form-control {
            min-height: 100px;
        }
        .editor-container {
            display: none;
            margin-top: 30px;
        }
        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .code-editor {
            width: 100%;
            height: 400px;
            font-family: monospace;
            padding: 10px;
            background-color: #1e1e1e;
            color: #d4d4d4;
            border: 1px solid #444;
            border-radius: 4px;
        }
        .status-message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status-success {
            background-color: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        .status-error {
            background-color: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
        }
        .status-info {
            background-color: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
        }
        .hidden {
            display: none;
        }

        /* Accordion styling */
        .accordion {
            margin-bottom: 20px;
        }
        .accordion-item {
            background-color: #2a2a2a;
            border: 1px solid #444;
            margin-bottom: 5px;
        }
        .accordion-button {
            background-color: #333;
            color: #f0f0f0;
            padding: 15px;
            font-weight: bold;
            border: none;
        }
        .accordion-button:not(.collapsed) {
            background-color: #4CAF50;
            color: white;
        }
        .accordion-button:focus {
            box-shadow: none;
            border-color: #444;
        }
        .accordion-button::after {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
        }
        .accordion-body {
            background-color: #222;
            padding: 15px;
        }

        /* Form styling */
        .form-range {
            background-color: #333;
            height: 8px;
            border-radius: 4px;
        }
        .form-range::-webkit-slider-thumb {
            background-color: #4CAF50;
        }
        .form-range::-moz-range-thumb {
            background-color: #4CAF50;
        }
        .form-check-input {
            background-color: #333;
            border-color: #555;
        }
        .form-check-input:checked {
            background-color: #4CAF50;
            border-color: #4CAF50;
        }
        .form-text {
            color: #ffffff; /* Changed from #aaa to white for better readability */
            font-size: 0.8rem;
            margin-top: 5px;
        }
        .weight-value {
            display: inline-block;
            margin-left: 10px;
            font-weight: bold;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <!-- Navigation Menu -->
    <div class="navigation-menu" style="background-color: #2a2a2a; padding: 10px; margin-bottom: 20px;">
        <div style="max-width: 1200px; margin: 0 auto; display: flex; gap: 20px;">
            <a href="/" style="color: #f0f0f0; text-decoration: none;">Home</a>
            <a href="/?tab=Configuration" style="color: #f0f0f0; text-decoration: none;">Configuration</a>
            <a href="/strategies" style="color: #4CAF50; text-decoration: none; font-weight: bold;">Strategy Management</a>
            <a href="/?tab=Backtesting" style="color: #f0f0f0; text-decoration: none;">Backtesting</a>
            <a href="/?tab=StrategyBot" style="color: #f0f0f0; text-decoration: none;">Strategy-bot</a>
            <a href="/?tab=LiveTrading" style="color: #f0f0f0; text-decoration: none;">Live Trading</a>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <h1>Strategy Management</h1>
            <div>
                <button id="delete-all-strategies-btn" class="btn btn-danger">Reset All Strategies</button>
                <button id="new-strategy-btn" class="btn btn-primary">New Strategy</button>
            </div>
        </div>

        <div id="status-message" class="status-message hidden"></div>

        <!-- New Strategy Form -->
        <div id="new-strategy-form" class="form-container hidden">
            <h2>Create New Strategy</h2>
            <div class="form-group">
                <label for="strategy-id">Strategy ID:</label>
                <input type="text" id="strategy-id" class="form-control" placeholder="e.g., macd_cross">
            </div>
            <div class="form-group">
                <label for="strategy-name">Strategy Name:</label>
                <input type="text" id="strategy-name" class="form-control" placeholder="e.g., MACD Crossover">
            </div>
            <div class="form-group">
                <label for="strategy-description">Description:</label>
                <textarea id="strategy-description" class="form-control" placeholder="Describe your strategy..."></textarea>
            </div>
            <input type="hidden" id="strategy-template" value="simple">
            <!-- Template selection removed as there's only one template now -->
            <div class="strategy-actions">
                <button id="create-strategy-btn" class="btn btn-primary">Create</button>
                <button id="cancel-create-btn" class="btn btn-secondary">Cancel</button>
            </div>
        </div>

        <!-- Strategy List -->
        <div id="strategy-list" class="strategy-list">
            <!-- Strategy cards will be dynamically added here -->
        </div>

        <!-- Strategy Editor -->
        <div id="strategy-editor" class="editor-container">
            <div class="editor-header">
                <h2 id="editor-title">Edit Strategy Parameters</h2>
                <div>
                    <button id="save-strategy-btn" class="btn btn-primary">Save</button>
                    <button id="test-strategy-btn" class="btn btn-secondary">Test</button>
                    <button id="close-editor-btn" class="btn btn-secondary">Close</button>
                </div>
            </div>
            <div class="form-container">
                <!-- Strategy Name and Description -->
                <div class="form-group">
                    <label for="strategy-name-edit">Strategy Name:</label>
                    <input type="text" id="strategy-name-edit" class="form-control" placeholder="Enter strategy name">
                </div>
                <div class="form-group">
                    <label for="strategy-description-edit">Description:</label>
                    <textarea id="strategy-description-edit" class="form-control" placeholder="Describe your strategy..."></textarea>
                </div>
                <p class="strategy-description" style="display: none;">The master strategy combines multiple technical indicators with configurable weights and dependencies. You can enable/disable each strategy and adjust its importance.</p>

                <div class="accordion" id="parameterAccordion">
                    <!-- Strategy Selection -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="headingStrategySelection">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseStrategySelection" aria-expanded="true" aria-controls="collapseStrategySelection">
                                Strategy Selection
                            </button>
                        </h3>
                        <div id="collapseStrategySelection" class="accordion-collapse collapse show" aria-labelledby="headingStrategySelection">
                            <div class="accordion-body">
                                <div class="form-check">
                                    <input type="checkbox" id="use-sma" name="use_sma" class="form-check-input" checked>
                                    <label for="use-sma" class="form-check-label">Use SMA Strategy</label>
                                </div>

                                <div class="form-check">
                                    <input type="checkbox" id="use-rsi" name="use_rsi" class="form-check-input" checked>
                                    <label for="use-rsi" class="form-check-label">Use RSI Strategy</label>
                                </div>

                                <div class="form-check">
                                    <input type="checkbox" id="use-macd" name="use_macd" class="form-check-input" checked>
                                    <label for="use-macd" class="form-check-label">Use MACD Strategy</label>
                                </div>

                                <div class="form-check">
                                    <input type="checkbox" id="use-bollinger" name="use_bollinger" class="form-check-input" checked>
                                    <label for="use-bollinger" class="form-check-label">Use Bollinger Bands Strategy</label>
                                </div>

                                <div class="form-check">
                                    <input type="checkbox" id="use-stochastic" name="use_stochastic" class="form-check-input">
                                    <label for="use-stochastic" class="form-check-label">Use Stochastic Oscillator</label>
                                </div>

                                <div class="form-check">
                                    <input type="checkbox" id="use-volume" name="use_volume" class="form-check-input">
                                    <label for="use-volume" class="form-check-label">Use Volume Analysis</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Weights -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="headingWeights">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseWeights" aria-expanded="false" aria-controls="collapseWeights">
                                Strategy Weights
                            </button>
                        </h3>
                        <div id="collapseWeights" class="accordion-collapse collapse" aria-labelledby="headingWeights">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <label for="weight-sma-buy">SMA Buy Weight:</label>
                                    <input type="range" id="weight-sma-buy" name="weight_sma_buy" class="form-range" min="0" max="2" step="0.1" value="1.0">
                                    <span class="weight-value">1.0</span>
                                </div>

                                <div class="form-group">
                                    <label for="weight-sma-sell">SMA Sell Weight:</label>
                                    <input type="range" id="weight-sma-sell" name="weight_sma_sell" class="form-range" min="0" max="2" step="0.1" value="1.0">
                                    <span class="weight-value">1.0</span>
                                </div>

                                <div class="form-group">
                                    <label for="weight-rsi-buy">RSI Buy Weight:</label>
                                    <input type="range" id="weight-rsi-buy" name="weight_rsi_buy" class="form-range" min="0" max="2" step="0.1" value="1.0">
                                    <span class="weight-value">1.0</span>
                                </div>

                                <div class="form-group">
                                    <label for="weight-rsi-sell">RSI Sell Weight:</label>
                                    <input type="range" id="weight-rsi-sell" name="weight_rsi_sell" class="form-range" min="0" max="2" step="0.1" value="1.0">
                                    <span class="weight-value">1.0</span>
                                </div>

                                <div class="form-group">
                                    <label for="weight-macd-buy">MACD Buy Weight:</label>
                                    <input type="range" id="weight-macd-buy" name="weight_macd_buy" class="form-range" min="0" max="2" step="0.1" value="1.0">
                                    <span class="weight-value">1.0</span>
                                </div>

                                <div class="form-group">
                                    <label for="weight-macd-sell">MACD Sell Weight:</label>
                                    <input type="range" id="weight-macd-sell" name="weight_macd_sell" class="form-range" min="0" max="2" step="0.1" value="1.0">
                                    <span class="weight-value">1.0</span>
                                </div>

                                <div class="form-group">
                                    <label for="weight-bollinger-buy">Bollinger Buy Weight:</label>
                                    <input type="range" id="weight-bollinger-buy" name="weight_bollinger_buy" class="form-range" min="0" max="2" step="0.1" value="1.0">
                                    <span class="weight-value">1.0</span>
                                </div>

                                <div class="form-group">
                                    <label for="weight-bollinger-sell">Bollinger Sell Weight:</label>
                                    <input type="range" id="weight-bollinger-sell" name="weight_bollinger_sell" class="form-range" min="0" max="2" step="0.1" value="1.0">
                                    <span class="weight-value">1.0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Dependencies -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="headingDependencies">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDependencies" aria-expanded="false" aria-controls="collapseDependencies">
                                Strategy Dependencies
                            </button>
                        </h3>
                        <div id="collapseDependencies" class="accordion-collapse collapse" aria-labelledby="headingDependencies">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <label for="sma-depends-on-volume">SMA depends on Volume:</label>
                                    <input type="range" id="sma-depends-on-volume" name="sma_depends_on_volume" class="form-range" min="0" max="1" step="0.1" value="0">
                                    <span class="weight-value">0.0</span>
                                    <small class="form-text text-muted">How much SMA signals depend on volume confirmation</small>
                                </div>

                                <div class="form-group">
                                    <label for="rsi-depends-on-sma">RSI depends on SMA:</label>
                                    <input type="range" id="rsi-depends-on-sma" name="rsi_depends_on_sma" class="form-range" min="0" max="1" step="0.1" value="0">
                                    <span class="weight-value">0.0</span>
                                    <small class="form-text text-muted">How much RSI signals depend on SMA confirmation</small>
                                </div>

                                <div class="form-group">
                                    <label for="macd-depends-on-rsi">MACD depends on RSI:</label>
                                    <input type="range" id="macd-depends-on-rsi" name="macd_depends_on_rsi" class="form-range" min="0" max="1" step="0.1" value="0">
                                    <span class="weight-value">0.0</span>
                                    <small class="form-text text-muted">How much MACD signals depend on RSI confirmation</small>
                                </div>

                                <div class="form-group">
                                    <label for="bollinger-depends-on-volume">Bollinger depends on Volume:</label>
                                    <input type="range" id="bollinger-depends-on-volume" name="bollinger_depends_on_volume" class="form-range" min="0" max="1" step="0.1" value="0">
                                    <span class="weight-value">0.0</span>
                                    <small class="form-text text-muted">How much Bollinger signals depend on volume confirmation</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Signal Thresholds -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="headingThresholds">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThresholds" aria-expanded="false" aria-controls="collapseThresholds">
                                Signal Thresholds
                            </button>
                        </h3>
                        <div id="collapseThresholds" class="accordion-collapse collapse" aria-labelledby="headingThresholds">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <label for="threshold-buy">Buy Threshold:</label>
                                    <input type="range" id="threshold-buy" name="threshold_buy" class="form-range" min="0.1" max="0.9" step="0.1" value="0.3">
                                    <span class="weight-value">0.3</span>
                                    <small class="form-text text-muted">Threshold for combined buy signals (lower = more signals)</small>
                                </div>

                                <div class="form-group">
                                    <label for="threshold-sell">Sell Threshold:</label>
                                    <input type="range" id="threshold-sell" name="threshold_sell" class="form-range" min="0.1" max="0.9" step="0.1" value="0.3">
                                    <span class="weight-value">0.3</span>
                                    <small class="form-text text-muted">Threshold for combined sell signals (lower = more signals)</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Risk Management -->
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="headingRisk">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRisk" aria-expanded="false" aria-controls="collapseRisk">
                                Risk Management
                            </button>
                        </h3>
                        <div id="collapseRisk" class="accordion-collapse collapse" aria-labelledby="headingRisk">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <label for="take-profit">Take Profit (%):</label>
                                    <input type="number" id="take-profit" name="take_profit" class="form-control" value="5.0" min="0.5" max="20" step="0.5">
                                </div>

                                <div class="form-group">
                                    <label for="stop-loss">Stop Loss (%):</label>
                                    <input type="number" id="stop-loss" name="stop_loss" class="form-control" value="2.0" min="0.5" max="10" step="0.5">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="test-results" class="status-message hidden"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const newStrategyBtn = document.getElementById('new-strategy-btn');
            const newStrategyForm = document.getElementById('new-strategy-form');
            const createStrategyBtn = document.getElementById('create-strategy-btn');
            const cancelCreateBtn = document.getElementById('cancel-create-btn');
            const strategyList = document.getElementById('strategy-list');
            const strategyEditor = document.getElementById('strategy-editor');
            const codeEditor = document.getElementById('code-editor');
            const editorTitle = document.getElementById('editor-title');
            const saveStrategyBtn = document.getElementById('save-strategy-btn');
            const testStrategyBtn = document.getElementById('test-strategy-btn');
            const closeEditorBtn = document.getElementById('close-editor-btn');
            const statusMessage = document.getElementById('status-message');
            const testResults = document.getElementById('test-results');
            const deleteAllStrategiesBtn = document.getElementById('delete-all-strategies-btn');

            // Current strategy being edited
            let currentStrategy = null;

            // Show status message
            function showStatus(message, type = 'info') {
                statusMessage.textContent = message;
                statusMessage.className = `status-message status-${type}`;
                statusMessage.classList.remove('hidden');

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    statusMessage.classList.add('hidden');
                }, 5000);
            }

            // Load available strategies
            async function loadStrategies() {
                try {
                    const response = await fetch('/available_python_strategies');
                    if (!response.ok) {
                        throw new Error('Failed to load strategies');
                    }

                    const strategies = await response.json();
                    renderStrategyList(strategies);
                } catch (error) {
                    console.error('Error loading strategies:', error);
                    showStatus('Failed to load strategies: ' + error.message, 'error');
                }
            }

            // Render strategy list
            function renderStrategyList(strategies) {
                strategyList.innerHTML = '';

                if (strategies.length === 0) {
                    strategyList.innerHTML = '<p>No strategies available. Create a new one to get started.</p>';
                    return;
                }

                strategies.forEach(strategy => {
                    const card = document.createElement('div');
                    card.className = 'strategy-card';

                    // Create HTML for card with or without delete button based on is_core flag
                    let cardHTML = `
                        <h3>${strategy.name}</h3>
                        <p>${strategy.description}</p>
                        <div class="strategy-actions">
                            <button class="btn btn-secondary edit-btn" data-id="${strategy.id}">Edit</button>
                    `;

                    // Only add delete button for non-core strategies
                    if (!strategy.is_core) {
                        cardHTML += `<button class="btn btn-danger delete-btn" data-id="${strategy.id}">Delete</button>`;
                    }

                    cardHTML += `</div>`;
                    card.innerHTML = cardHTML;

                    strategyList.appendChild(card);

                    // Add event listeners to buttons
                    card.querySelector('.edit-btn').addEventListener('click', () => editStrategy(strategy.id));

                    // Only add delete event listener if the button exists
                    const deleteBtn = card.querySelector('.delete-btn');
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', () => deleteStrategy(strategy.id));
                    }
                });
            }

            // Edit strategy
            async function editStrategy(strategyId) {
                try {
                    const response = await fetch(`/strategies/python/${strategyId}`);
                    if (!response.ok) {
                        throw new Error('Failed to load strategy');
                    }

                    const strategy = await response.json();
                    currentStrategy = strategy;

                    // Update editor title
                    editorTitle.textContent = `Edit Strategy Parameters: ${strategy.name}`;

                    // Update strategy name and description fields
                    document.getElementById('strategy-name-edit').value = strategy.name;
                    document.getElementById('strategy-description-edit').value = strategy.description;

                    // Update form fields with strategy parameters
                    if (strategy.params) {
                        // Strategy Selection
                        document.getElementById('use-sma').checked = strategy.params.use_sma !== false;
                        document.getElementById('use-rsi').checked = strategy.params.use_rsi !== false;
                        document.getElementById('use-macd').checked = strategy.params.use_macd !== false;
                        document.getElementById('use-bollinger').checked = strategy.params.use_bollinger !== false;
                        document.getElementById('use-stochastic').checked = strategy.params.use_stochastic === true;
                        document.getElementById('use-volume').checked = strategy.params.use_volume === true;

                        // Strategy Weights
                        if (document.getElementById('weight-sma-buy')) {
                            document.getElementById('weight-sma-buy').value = strategy.params.weight_sma_buy || 1.0;
                            document.getElementById('weight-sma-buy').nextElementSibling.textContent = strategy.params.weight_sma_buy || 1.0;
                        }
                        if (document.getElementById('weight-sma-sell')) {
                            document.getElementById('weight-sma-sell').value = strategy.params.weight_sma_sell || 1.0;
                            document.getElementById('weight-sma-sell').nextElementSibling.textContent = strategy.params.weight_sma_sell || 1.0;
                        }
                        if (document.getElementById('weight-rsi-buy')) {
                            document.getElementById('weight-rsi-buy').value = strategy.params.weight_rsi_buy || 1.0;
                            document.getElementById('weight-rsi-buy').nextElementSibling.textContent = strategy.params.weight_rsi_buy || 1.0;
                        }
                        if (document.getElementById('weight-rsi-sell')) {
                            document.getElementById('weight-rsi-sell').value = strategy.params.weight_rsi_sell || 1.0;
                            document.getElementById('weight-rsi-sell').nextElementSibling.textContent = strategy.params.weight_rsi_sell || 1.0;
                        }
                        if (document.getElementById('weight-macd-buy')) {
                            document.getElementById('weight-macd-buy').value = strategy.params.weight_macd_buy || 1.0;
                            document.getElementById('weight-macd-buy').nextElementSibling.textContent = strategy.params.weight_macd_buy || 1.0;
                        }
                        if (document.getElementById('weight-macd-sell')) {
                            document.getElementById('weight-macd-sell').value = strategy.params.weight_macd_sell || 1.0;
                            document.getElementById('weight-macd-sell').nextElementSibling.textContent = strategy.params.weight_macd_sell || 1.0;
                        }
                        if (document.getElementById('weight-bollinger-buy')) {
                            document.getElementById('weight-bollinger-buy').value = strategy.params.weight_bollinger_buy || 1.0;
                            document.getElementById('weight-bollinger-buy').nextElementSibling.textContent = strategy.params.weight_bollinger_buy || 1.0;
                        }
                        if (document.getElementById('weight-bollinger-sell')) {
                            document.getElementById('weight-bollinger-sell').value = strategy.params.weight_bollinger_sell || 1.0;
                            document.getElementById('weight-bollinger-sell').nextElementSibling.textContent = strategy.params.weight_bollinger_sell || 1.0;
                        }

                        // Strategy Dependencies
                        if (document.getElementById('sma-depends-on-volume')) {
                            document.getElementById('sma-depends-on-volume').value = strategy.params.sma_depends_on_volume || 0.0;
                            document.getElementById('sma-depends-on-volume').nextElementSibling.textContent = strategy.params.sma_depends_on_volume || 0.0;
                        }
                        if (document.getElementById('rsi-depends-on-sma')) {
                            document.getElementById('rsi-depends-on-sma').value = strategy.params.rsi_depends_on_sma || 0.0;
                            document.getElementById('rsi-depends-on-sma').nextElementSibling.textContent = strategy.params.rsi_depends_on_sma || 0.0;
                        }
                        if (document.getElementById('macd-depends-on-rsi')) {
                            document.getElementById('macd-depends-on-rsi').value = strategy.params.macd_depends_on_rsi || 0.0;
                            document.getElementById('macd-depends-on-rsi').nextElementSibling.textContent = strategy.params.macd_depends_on_rsi || 0.0;
                        }
                        if (document.getElementById('bollinger-depends-on-volume')) {
                            document.getElementById('bollinger-depends-on-volume').value = strategy.params.bollinger_depends_on_volume || 0.0;
                            document.getElementById('bollinger-depends-on-volume').nextElementSibling.textContent = strategy.params.bollinger_depends_on_volume || 0.0;
                        }

                        // Signal Thresholds
                        if (document.getElementById('threshold-buy')) {
                            document.getElementById('threshold-buy').value = strategy.params.threshold_buy || 0.3;
                            document.getElementById('threshold-buy').nextElementSibling.textContent = strategy.params.threshold_buy || 0.3;
                        }
                        if (document.getElementById('threshold-sell')) {
                            document.getElementById('threshold-sell').value = strategy.params.threshold_sell || 0.3;
                            document.getElementById('threshold-sell').nextElementSibling.textContent = strategy.params.threshold_sell || 0.3;
                        }

                        // Risk Management
                        if (document.getElementById('take-profit')) {
                            document.getElementById('take-profit').value = strategy.params.take_profit || 5.0;
                        }
                        if (document.getElementById('stop-loss')) {
                            document.getElementById('stop-loss').value = strategy.params.stop_loss || 2.0;
                        }
                    }

                    // Show editor
                    strategyEditor.style.display = 'block';

                    // Scroll to editor
                    strategyEditor.scrollIntoView({ behavior: 'smooth' });

                    // Add event listeners to update displayed values for sliders
                    document.querySelectorAll('input[type="range"]').forEach(slider => {
                        slider.addEventListener('input', function() {
                            if (this.nextElementSibling && this.nextElementSibling.classList.contains('weight-value')) {
                                this.nextElementSibling.textContent = this.value;
                            }
                        });
                    });
                } catch (error) {
                    console.error('Error loading strategy:', error);
                    showStatus('Failed to load strategy: ' + error.message, 'error');
                }
            }

            // Save strategy
            async function saveStrategy() {
                if (!currentStrategy) {
                    showStatus('No strategy selected', 'error');
                    return;
                }

                try {
                    // Collect parameters from form
                    const params = {
                        // Strategy Selection
                        use_sma: document.getElementById('use-sma').checked,
                        use_rsi: document.getElementById('use-rsi').checked,
                        use_macd: document.getElementById('use-macd').checked,
                        use_bollinger: document.getElementById('use-bollinger').checked,
                        use_stochastic: document.getElementById('use-stochastic').checked,
                        use_volume: document.getElementById('use-volume').checked,

                        // Strategy Weights
                        weight_sma_buy: parseFloat(document.getElementById('weight-sma-buy').value),
                        weight_sma_sell: parseFloat(document.getElementById('weight-sma-sell').value),
                        weight_rsi_buy: parseFloat(document.getElementById('weight-rsi-buy').value),
                        weight_rsi_sell: parseFloat(document.getElementById('weight-rsi-sell').value),
                        weight_macd_buy: parseFloat(document.getElementById('weight-macd-buy').value),
                        weight_macd_sell: parseFloat(document.getElementById('weight-macd-sell').value),
                        weight_bollinger_buy: parseFloat(document.getElementById('weight-bollinger-buy').value),
                        weight_bollinger_sell: parseFloat(document.getElementById('weight-bollinger-sell').value),

                        // Strategy Dependencies
                        sma_depends_on_volume: parseFloat(document.getElementById('sma-depends-on-volume').value),
                        rsi_depends_on_sma: parseFloat(document.getElementById('rsi-depends-on-sma').value),
                        macd_depends_on_rsi: parseFloat(document.getElementById('macd-depends-on-rsi').value),
                        bollinger_depends_on_volume: parseFloat(document.getElementById('bollinger-depends-on-volume').value),

                        // Signal Thresholds
                        threshold_buy: parseFloat(document.getElementById('threshold-buy').value),
                        threshold_sell: parseFloat(document.getElementById('threshold-sell').value),

                        // Risk Management
                        take_profit: parseFloat(document.getElementById('take-profit').value),
                        stop_loss: parseFloat(document.getElementById('stop-loss').value)
                    };

                    const response = await fetch('/save_strategy_parameters', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            strategy_id: currentStrategy.id,
                            parameters: params,
                            strategy_name: document.getElementById('strategy-name-edit').value,
                            strategy_description: document.getElementById('strategy-description-edit').value,
                            create_new_version: false // Don't create a new version, just update the existing one
                        })
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || 'Failed to save strategy parameters');
                    }

                    const result = await response.json();
                    showStatus(result.message || 'Strategy parameters saved successfully', 'success');

                    // Reload strategies
                    loadStrategies();
                } catch (error) {
                    console.error('Error saving strategy parameters:', error);
                    showStatus('Failed to save strategy parameters: ' + error.message, 'error');
                }
            }

            // Test strategy - this function is now defined later in the file

            // Delete strategy
            async function deleteStrategy(strategyId) {
                if (!confirm(`Are you sure you want to delete the strategy "${strategyId}"?`)) {
                    return;
                }

                // Find the strategy card to remove it visually
                const strategyCard = findStrategyCard(strategyId);

                try {
                    console.log(`Deleting strategy: ${strategyId}`);
                    // Try both endpoints to ensure compatibility
                    let response;
                    try {
                        console.log(`Sending DELETE request to /delete_python_strategy/${strategyId}`);
                        response = await fetch(`/delete_python_strategy/${strategyId}`, {
                            method: 'DELETE'
                        });
                        console.log('Response status:', response.status);
                    } catch (e) {
                        console.error('Error with first endpoint, trying fallback:', e);
                        console.log(`Sending DELETE request to /strategies/delete/${strategyId}`);
                        response = await fetch(`/strategies/delete/${strategyId}`, {
                            method: 'DELETE'
                        });
                        console.log('Fallback response status:', response.status);
                    }

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || error.message || 'Failed to delete strategy');
                    }

                    console.log('Parsing response...');
                    const result = await response.json();
                    console.log('Response result:', result);
                    showStatus(result.message || 'Strategy deleted successfully', 'success');

                    // Remove the strategy card from the UI immediately
                    if (strategyCard) {
                        strategyCard.remove();
                    }

                    // Close editor if the deleted strategy was being edited
                    if (currentStrategy && currentStrategy.id === strategyId) {
                        strategyEditor.style.display = 'none';
                        currentStrategy = null;
                    }

                    // Check if there are no strategies left
                    if (strategyList.children.length === 0) {
                        strategyList.innerHTML = '<p>No strategies available. Create a new one to get started.</p>';
                    }
                } catch (error) {
                    console.error('Error deleting strategy:', error);
                    showStatus('Failed to delete strategy: ' + error.message, 'error');
                }
            }

            // Helper function to find a strategy card by ID
            function findStrategyCard(strategyId) {
                const cards = document.querySelectorAll('.strategy-card');
                for (const card of cards) {
                    const deleteBtn = card.querySelector('.delete-btn');
                    if (deleteBtn && deleteBtn.getAttribute('data-id') === strategyId) {
                        return card;
                    }
                }
                return null;
            }

            // Create new strategy
            async function createStrategy() {
                console.log("Create strategy function called");
                const strategyId = document.getElementById('strategy-id').value.trim();
                const strategyName = document.getElementById('strategy-name').value.trim();
                const strategyDescription = document.getElementById('strategy-description').value.trim();
                const strategyTemplate = document.getElementById('strategy-template').value;

                console.log("Strategy ID:", strategyId);
                console.log("Strategy Name:", strategyName);
                console.log("Strategy Description:", strategyDescription);
                console.log("Strategy Template:", strategyTemplate);

                if (!strategyId) {
                    showStatus('Strategy ID is required', 'error');
                    return;
                }

                if (!strategyName) {
                    showStatus('Strategy name is required', 'error');
                    return;
                }

                if (!strategyDescription) {
                    showStatus('Strategy description is required', 'error');
                    return;
                }

                try {
                    console.log("Sending request to create strategy");
                    const response = await fetch('/create_python_strategy', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            id: strategyId,
                            name: strategyName,
                            description: strategyDescription,
                            template: strategyTemplate
                        })
                    });

                    console.log("Response status:", response.status);

                    if (!response.ok) {
                        const error = await response.json();
                        console.error("Error response:", error);
                        throw new Error(error.error || 'Failed to create strategy');
                    }

                    const result = await response.json();
                    console.log("Success response:", result);
                    showStatus(result.message || 'Strategy created successfully', 'success');

                    // Reset form
                    document.getElementById('strategy-id').value = '';
                    document.getElementById('strategy-name').value = '';
                    document.getElementById('strategy-description').value = '';

                    // Hide form
                    newStrategyForm.classList.add('hidden');

                    // Reload strategies
                    loadStrategies();

                    // Edit the newly created strategy
                    setTimeout(() => {
                        editStrategy(strategyId);
                    }, 500);
                } catch (error) {
                    console.error('Error creating strategy:', error);
                    showStatus('Failed to create strategy: ' + error.message, 'error');
                }
            }

            // Delete all strategies
            async function deleteAllStrategies() {
                if (!confirm('Are you sure you want to reset ALL strategies to default? This action cannot be undone.')) {
                    return;
                }

                try {
                    const response = await fetch('/delete_all_python_strategies', {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || error.message || 'Failed to delete all strategies');
                    }

                    const result = await response.json();
                    showStatus(result.message || 'All strategies reset to default successfully', 'success');

                    // Close editor if open
                    strategyEditor.style.display = 'none';
                    currentStrategy = null;

                    // Reload strategies
                    loadStrategies();
                } catch (error) {
                    console.error('Error deleting all strategies:', error);
                    showStatus('Failed to delete all strategies: ' + error.message, 'error');
                }
            }

            // Event Listeners
            newStrategyBtn.addEventListener('click', () => {
                newStrategyForm.classList.toggle('hidden');
            });

            cancelCreateBtn.addEventListener('click', () => {
                newStrategyForm.classList.add('hidden');
            });

            // Add event listener for create button
            createStrategyBtn.addEventListener('click', createStrategy);

            saveStrategyBtn.addEventListener('click', saveStrategy);

            // Test strategy function
            async function testStrategy() {
                if (!currentStrategy) {
                    showStatus('No strategy selected', 'error');
                    return;
                }

                try {
                    showStatus('Testing strategy...', 'info');
                    testResults.innerHTML = 'Running test...';
                    testResults.classList.remove('hidden');

                    // Collect parameters from form
                    const params = {
                        // Strategy Selection
                        use_sma: document.getElementById('use-sma').checked,
                        use_rsi: document.getElementById('use-rsi').checked,
                        use_macd: document.getElementById('use-macd').checked,
                        use_bollinger: document.getElementById('use-bollinger').checked,
                        use_stochastic: document.getElementById('use-stochastic').checked,
                        use_volume: document.getElementById('use-volume').checked,

                        // Strategy Weights
                        weight_sma_buy: parseFloat(document.getElementById('weight-sma-buy').value),
                        weight_sma_sell: parseFloat(document.getElementById('weight-sma-sell').value),
                        weight_rsi_buy: parseFloat(document.getElementById('weight-rsi-buy').value),
                        weight_rsi_sell: parseFloat(document.getElementById('weight-rsi-sell').value),
                        weight_macd_buy: parseFloat(document.getElementById('weight-macd-buy').value),
                        weight_macd_sell: parseFloat(document.getElementById('weight-macd-sell').value),
                        weight_bollinger_buy: parseFloat(document.getElementById('weight-bollinger-buy').value),
                        weight_bollinger_sell: parseFloat(document.getElementById('weight-bollinger-sell').value),

                        // Strategy Dependencies
                        sma_depends_on_volume: parseFloat(document.getElementById('sma-depends-on-volume').value),
                        rsi_depends_on_sma: parseFloat(document.getElementById('rsi-depends-on-sma').value),
                        macd_depends_on_rsi: parseFloat(document.getElementById('macd-depends-on-rsi').value),
                        bollinger_depends_on_volume: parseFloat(document.getElementById('bollinger-depends-on-volume').value),

                        // Signal Thresholds
                        threshold_buy: parseFloat(document.getElementById('threshold-buy').value),
                        threshold_sell: parseFloat(document.getElementById('threshold-sell').value),

                        // Risk Management
                        take_profit: parseFloat(document.getElementById('take-profit').value),
                        stop_loss: parseFloat(document.getElementById('stop-loss').value)
                    };

                    const response = await fetch('/test_strategy', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            strategy_id: currentStrategy.id,
                            parameters: params
                        })
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.error || 'Failed to test strategy');
                    }

                    const result = await response.json();

                    // Display test results
                    testResults.innerHTML = `
                        <h3>Test Results</h3>
                        <p>Total Trades: ${result.total_trades}</p>
                        <p>Win Rate: ${result.win_rate}%</p>
                        <p>Total Profit: ${result.total_profit}%</p>
                        <p>Average Profit per Trade: ${result.avg_profit}%</p>
                        <p>Max Drawdown: ${result.max_drawdown}%</p>
                    `;

                    showStatus('Strategy test completed', 'success');
                } catch (error) {
                    console.error('Error testing strategy:', error);
                    testResults.innerHTML = `<p class="error">Test failed: ${error.message}</p>`;
                    showStatus('Failed to test strategy: ' + error.message, 'error');
                }
            }

            testStrategyBtn.addEventListener('click', testStrategy);

            closeEditorBtn.addEventListener('click', () => {
                strategyEditor.style.display = 'none';
                currentStrategy = null;
            });

            deleteAllStrategiesBtn.addEventListener('click', deleteAllStrategies);

            // Load strategies on page load
            loadStrategies();
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
