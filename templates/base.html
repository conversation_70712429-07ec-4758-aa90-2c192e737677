<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TradingBot{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Include Chart.js directly -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #f0f0f0;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background-color: #2a2a2a;
            border: 1px solid #444;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #333;
            padding: 10px 15px;
            border-bottom: 1px solid #444;
        }
        .card-body {
            padding: 15px;
        }
        .btn-primary {
            background-color: #4CAF50;
            border-color: #4CAF50;
        }
        .btn-primary:hover {
            background-color: #45a049;
            border-color: #45a049;
        }
        .btn-danger {
            background-color: #f44336;
            border-color: #f44336;
        }
        .btn-danger:hover {
            background-color: #d32f2f;
            border-color: #d32f2f;
        }
        .btn-warning {
            background-color: #ff9800;
            border-color: #ff9800;
            color: #fff;
        }
        .btn-warning:hover {
            background-color: #f57c00;
            border-color: #f57c00;
            color: #fff;
        }
        .form-control {
            background-color: #333;
            border: 1px solid #444;
            color: #f0f0f0;
        }
        .form-control:focus {
            background-color: #3a3a3a;
            border-color: #4CAF50;
            color: #f0f0f0;
            box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
        }
        .form-check-input:checked {
            background-color: #4CAF50;
            border-color: #4CAF50;
        }
        .table {
            color: #f0f0f0;
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .progress {
            background-color: #333;
        }
        .progress-bar {
            background-color: #4CAF50;
        }
        .navigation-menu {
            background-color: #2a2a2a;
            padding: 10px;
            margin-bottom: 20px;
        }
        .navigation-menu a {
            color: #f0f0f0;
            text-decoration: none;
            margin-right: 20px;
        }
        .navigation-menu a:hover {
            color: #4CAF50;
        }
        .navigation-menu a.active {
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
    <!-- Navigation Menu -->
    <div class="navigation-menu">
        <div class="container">
            <div class="d-flex gap-4">
                <a href="/" class="{% if request.path == '/' and not request.args.get('tab') %}active{% endif %}">Home</a>
                <a href="/?tab=Configuration" class="{% if request.args.get('tab') == 'Configuration' %}active{% endif %}">Configuration</a>
                <a href="/strategies" class="{% if request.path == '/strategies' %}active{% endif %}">Strategy Management</a>
                <a href="/?tab=Backtesting" class="{% if request.args.get('tab') == 'Backtesting' %}active{% endif %}">Backtesting</a>
                <a href="/strategy_bot" class="{% if request.path == '/strategy_bot' %}active{% endif %}">Strategy-bot</a>
                <a href="/?tab=LiveTrading" class="{% if request.args.get('tab') == 'LiveTrading' %}active{% endif %}">Live Trading</a>
            </div>
        </div>
    </div>

    <div class="container">
        <button id="theme-toggle" class="btn btn-sm btn-secondary position-absolute top-0 end-0 m-3">Toggle Theme</button>
        
        <div id="status-message" class="alert alert-info" style="display: none;"></div>
        
        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Theme toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                // Check for saved theme preference or use preferred color scheme
                const savedTheme = localStorage.getItem('theme') ||
                    (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

                // Apply saved theme
                document.documentElement.setAttribute('data-theme', savedTheme);

                // Update toggle button text
                themeToggle.textContent = savedTheme === 'dark' ? 'Light Mode' : 'Dark Mode';

                // Add click event
                themeToggle.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                    // Update theme
                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);

                    // Update button text
                    themeToggle.textContent = newTheme === 'dark' ? 'Light Mode' : 'Dark Mode';
                });
            }
        });

        // Function to display status messages
        function displayStatusMessage(message, type = 'info') {
            const statusDiv = document.getElementById('status-message');
            if (statusDiv) {
                statusDiv.textContent = message;
                statusDiv.className = `alert alert-${type}`;
                statusDiv.style.display = 'block';

                // Clear message after 5 seconds
                setTimeout(() => {
                    statusDiv.textContent = '';
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
