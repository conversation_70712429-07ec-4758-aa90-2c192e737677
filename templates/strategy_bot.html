{% extends "base.html" %}

{% block title %}Strategy-bot{% endblock %}

{% block content %}
<div class="container">
    <h1>Strategy-bot</h1>
    <p>De Strategy-bot optimaliseert automatisch de parameters van de master strategy om de beste resultaten te behalen.</p>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h2>Parameters</h2>
                </div>
                <div class="card-body">
                    <form id="strategy-bot-form">
                        <div class="form-group">
                            <label for="pair">Trading Pair:</label>
                            <select id="pair" name="pair" class="form-control">
                                <option value="BTC-USD">BTC-USD</option>
                                <option value="ETH-USD">ETH-USD</option>
                                <option value="SOL-USD">SOL-USD</option>
                                <option value="AVAX-USD">AVAX-USD</option>
                                <option value="LINK-USD">LINK-USD</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="start-date">Start Date:</label>
                            <input type="date" id="start-date" name="start_date" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="end-date">End Date:</label>
                            <input type="date" id="end-date" name="end_date" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="iterations">Iterations:</label>
                            <input type="number" id="iterations" name="iterations" class="form-control" value="20" min="1" max="100">
                            <small class="form-text text-muted">Aantal iteraties voor de optimalisatie</small>
                        </div>

                        <h3>Strategy Parameters</h3>

                        <div class="accordion" id="strategyAccordion">
                            <!-- Strategy Selection -->
                            <div class="accordion-item bg-dark">
                                <h2 class="accordion-header" id="headingStrategySelection">
                                    <button class="accordion-button bg-dark text-light" type="button" data-bs-toggle="collapse" data-bs-target="#collapseStrategySelection" aria-expanded="true" aria-controls="collapseStrategySelection">
                                        Strategy Selection
                                    </button>
                                </h2>
                                <div id="collapseStrategySelection" class="accordion-collapse collapse show" aria-labelledby="headingStrategySelection">
                                    <div class="accordion-body">
                                        <div class="form-check">
                                            <input type="checkbox" id="use-sma" name="use_sma" class="form-check-input" checked>
                                            <label for="use-sma" class="form-check-label">Use SMA Strategy</label>
                                        </div>

                                        <div class="form-check">
                                            <input type="checkbox" id="use-rsi" name="use_rsi" class="form-check-input" checked>
                                            <label for="use-rsi" class="form-check-label">Use RSI Strategy</label>
                                        </div>

                                        <div class="form-check">
                                            <input type="checkbox" id="use-macd" name="use_macd" class="form-check-input" checked>
                                            <label for="use-macd" class="form-check-label">Use MACD Strategy</label>
                                        </div>

                                        <div class="form-check">
                                            <input type="checkbox" id="use-bollinger" name="use_bollinger" class="form-check-input" checked>
                                            <label for="use-bollinger" class="form-check-label">Use Bollinger Bands Strategy</label>
                                        </div>

                                        <div class="form-check">
                                            <input type="checkbox" id="use-stochastic" name="use_stochastic" class="form-check-input">
                                            <label for="use-stochastic" class="form-check-label">Use Stochastic Oscillator</label>
                                        </div>

                                        <div class="form-check">
                                            <input type="checkbox" id="use-volume" name="use_volume" class="form-check-input">
                                            <label for="use-volume" class="form-check-label">Use Volume Analysis</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Strategy Dependencies -->
                            <div class="accordion-item bg-dark">
                                <h2 class="accordion-header" id="headingDependencies">
                                    <button class="accordion-button bg-dark text-light collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDependencies" aria-expanded="false" aria-controls="collapseDependencies">
                                        Strategy Dependencies
                                    </button>
                                </h2>
                                <div id="collapseDependencies" class="accordion-collapse collapse" aria-labelledby="headingDependencies">
                                    <div class="accordion-body">
                                        <div class="form-group">
                                            <label for="sma-depends-on-volume">SMA depends on Volume:</label>
                                            <input type="range" id="sma-depends-on-volume" name="sma_depends_on_volume" class="form-range" min="0" max="1" step="0.1" value="0">
                                            <small class="form-text text-muted">How much SMA signals depend on volume confirmation</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="rsi-depends-on-sma">RSI depends on SMA:</label>
                                            <input type="range" id="rsi-depends-on-sma" name="rsi_depends_on_sma" class="form-range" min="0" max="1" step="0.1" value="0">
                                            <small class="form-text text-muted">How much RSI signals depend on SMA confirmation</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="macd-depends-on-rsi">MACD depends on RSI:</label>
                                            <input type="range" id="macd-depends-on-rsi" name="macd_depends_on_rsi" class="form-range" min="0" max="1" step="0.1" value="0">
                                            <small class="form-text text-muted">How much MACD signals depend on RSI confirmation</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="bollinger-depends-on-volume">Bollinger depends on Volume:</label>
                                            <input type="range" id="bollinger-depends-on-volume" name="bollinger_depends_on_volume" class="form-range" min="0" max="1" step="0.1" value="0">
                                            <small class="form-text text-muted">How much Bollinger signals depend on volume confirmation</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Signal Thresholds -->
                            <div class="accordion-item bg-dark">
                                <h2 class="accordion-header" id="headingThresholds">
                                    <button class="accordion-button bg-dark text-light collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThresholds" aria-expanded="false" aria-controls="collapseThresholds">
                                        Signal Thresholds
                                    </button>
                                </h2>
                                <div id="collapseThresholds" class="accordion-collapse collapse" aria-labelledby="headingThresholds">
                                    <div class="accordion-body">
                                        <div class="form-group">
                                            <label for="threshold-buy">Buy Threshold:</label>
                                            <input type="range" id="threshold-buy" name="threshold_buy" class="form-range" min="0.1" max="0.9" step="0.1" value="0.3">
                                            <small class="form-text text-muted">Threshold for combined buy signals (lower = more signals)</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="threshold-sell">Sell Threshold:</label>
                                            <input type="range" id="threshold-sell" name="threshold_sell" class="form-range" min="0.1" max="0.9" step="0.1" value="0.3">
                                            <small class="form-text text-muted">Threshold for combined sell signals (lower = more signals)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Risk Management -->
                            <div class="accordion-item bg-dark">
                                <h2 class="accordion-header" id="headingRisk">
                                    <button class="accordion-button bg-dark text-light collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRisk" aria-expanded="false" aria-controls="collapseRisk">
                                        Risk Management
                                    </button>
                                </h2>
                                <div id="collapseRisk" class="accordion-collapse collapse" aria-labelledby="headingRisk">
                                    <div class="accordion-body">
                                        <div class="form-group">
                                            <label for="take-profit">Take Profit (%):</label>
                                            <input type="number" id="take-profit" name="take_profit" class="form-control" value="5.0" min="0.5" max="20" step="0.5">
                                        </div>

                                        <div class="form-group">
                                            <label for="stop-loss">Stop Loss (%):</label>
                                            <input type="number" id="stop-loss" name="stop_loss" class="form-control" value="2.0" min="0.5" max="10" step="0.5">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="button" id="start-bot-button" class="btn btn-primary mt-3">Start Strategy-bot</button>
                        <button type="button" id="stop-bot-button" class="btn btn-danger mt-3">Stop Strategy-bot</button>
                        <button type="button" id="reset-bot-button" class="btn btn-warning mt-3">Reset Results</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h2>Status</h2>
                </div>
                <div class="card-body">
                    <div id="strategy-bot-status">
                        <p>Status: <span id="status-text">Niet actief</span></p>
                        <p>Voortgang: <span id="progress-text">0%</span></p>
                        <div class="progress">
                            <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        <p>Huidige prestatie: <span id="current-performance">-</span></p>
                        <p>Win ratio: <span id="current-win-ratio">-</span></p>
                        <p>Aantal trades: <span id="current-trades">-</span></p>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h2>Visualisatie</h2>
                </div>
                <div class="card-body">
                    <div id="strategy-bot-visualization"></div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h2>Resultaten</h2>
                </div>
                <div class="card-body">
                    <div id="strategy-bot-results">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Versie</th>
                                    <th>Strategie</th>
                                    <th>Prestatie</th>
                                    <th>Win Ratio</th>
                                    <th>Trades</th>
                                    <th>Acties</th>
                                </tr>
                            </thead>
                            <tbody id="results-table-body">
                                <!-- Results will be inserted here by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/strategy_bot_visualization.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize visualization
        if (window.strategyBotVisualization) {
            window.strategyBotVisualization.init();
        }

        // Set default dates
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);

        document.getElementById('end-date').value = today.toISOString().split('T')[0];
        document.getElementById('start-date').value = thirtyDaysAgo.toISOString().split('T')[0];

        // Add event listeners for buttons
        document.getElementById('start-bot-button').addEventListener('click', startStrategyBot);
        document.getElementById('stop-bot-button').addEventListener('click', stopStrategyBot);
        document.getElementById('reset-bot-button').addEventListener('click', resetStrategyBotResults);

        // Load initial status and results
        updateStatus();
        loadResults();
    });

    // Function to start the Strategy-bot
    function startStrategyBot() {
        // Get form values
        const pair = document.getElementById('pair').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        const iterations = document.getElementById('iterations').value;

        // Strategy selection
        const useSma = document.getElementById('use-sma').checked;
        const useRsi = document.getElementById('use-rsi').checked;
        const useMacd = document.getElementById('use-macd').checked;
        const useBollinger = document.getElementById('use-bollinger').checked;
        const useStochastic = document.getElementById('use-stochastic').checked;
        const useVolume = document.getElementById('use-volume').checked;

        // Strategy dependencies
        const smaDepengsOnVolume = parseFloat(document.getElementById('sma-depends-on-volume').value);
        const rsiDependsOnSma = parseFloat(document.getElementById('rsi-depends-on-sma').value);
        const macdDependsOnRsi = parseFloat(document.getElementById('macd-depends-on-rsi').value);
        const bollingerDependsOnVolume = parseFloat(document.getElementById('bollinger-depends-on-volume').value);

        // Signal thresholds
        const thresholdBuy = parseFloat(document.getElementById('threshold-buy').value);
        const thresholdSell = parseFloat(document.getElementById('threshold-sell').value);

        // Risk management
        const takeProfit = parseFloat(document.getElementById('take-profit').value);
        const stopLoss = parseFloat(document.getElementById('stop-loss').value);

        // Create request data
        const data = {
            pair: pair,
            start_date: startDate,
            end_date: endDate,
            iterations: parseInt(iterations),
            strategy: 'master_strategy',

            // Strategy selection
            use_sma: useSma,
            use_rsi: useRsi,
            use_macd: useMacd,
            use_bollinger: useBollinger,
            use_stochastic: useStochastic,
            use_volume: useVolume,

            // Strategy dependencies
            sma_depends_on_volume: smaDepengsOnVolume,
            rsi_depends_on_sma: rsiDependsOnSma,
            macd_depends_on_rsi: macdDependsOnRsi,
            bollinger_depends_on_volume: bollingerDependsOnVolume,

            // Signal thresholds
            threshold_buy: thresholdBuy,
            threshold_sell: thresholdSell,

            // Risk management
            take_profit: takeProfit,
            stop_loss: stopLoss
        };

        // Send request to start the bot
        fetch('/strategy_bot/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.status === 'success') {
                alert('Strategy-bot gestart!');
                updateStatus();
            } else {
                alert('Fout bij het starten van de Strategy-bot: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error starting Strategy-bot:', error);
            alert('Fout bij het starten van de Strategy-bot');
        });
    }

    // Function to stop the Strategy-bot
    function stopStrategyBot() {
        fetch('/strategy_bot/stop')
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    alert('Strategy-bot gestopt!');
                    updateStatus();
                } else {
                    alert('Fout bij het stoppen van de Strategy-bot: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error stopping Strategy-bot:', error);
                alert('Fout bij het stoppen van de Strategy-bot');
            });
    }

    // Function to reset Strategy-bot results
    function resetStrategyBotResults() {
        if (confirm('Weet je zeker dat je alle resultaten wilt verwijderen?')) {
            fetch('/strategy_bot/reset')
                .then(response => response.json())
                .then(result => {
                    if (result.status === 'success') {
                        alert('Resultaten verwijderd!');
                        loadResults();
                        updateStatus();
                    } else {
                        alert('Fout bij het verwijderen van resultaten: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error resetting results:', error);
                    alert('Fout bij het verwijderen van resultaten');
                });
        }
    }

    // Function to update status
    function updateStatus() {
        fetch('/strategy_bot/status')
            .then(response => response.json())
            .then(data => {
                document.getElementById('status-text').textContent = data.status;
                document.getElementById('progress-text').textContent = data.progress + '%';
                document.getElementById('progress-bar').style.width = data.progress + '%';
                document.getElementById('progress-bar').setAttribute('aria-valuenow', data.progress);
                document.getElementById('progress-bar').textContent = data.progress + '%';

                // Update performance metrics if available
                if (data.current_performance !== null && data.current_performance !== undefined) {
                    document.getElementById('current-performance').textContent = data.current_performance.toFixed(2) + '%';
                } else {
                    document.getElementById('current-performance').textContent = '-';
                }

                if (data.current_win_ratio !== null && data.current_win_ratio !== undefined) {
                    document.getElementById('current-win-ratio').textContent = data.current_win_ratio.toFixed(2) + '%';
                } else {
                    document.getElementById('current-win-ratio').textContent = '-';
                }

                if (data.current_trades !== null && data.current_trades !== undefined) {
                    document.getElementById('current-trades').textContent = data.current_trades;
                } else {
                    document.getElementById('current-trades').textContent = '-';
                }

                // Schedule next update if bot is running
                if (data.running) {
                    setTimeout(updateStatus, 1000);
                }
            })
            .catch(error => {
                console.error('Error updating status:', error);
            });
    }

    // Function to load results
    function loadResults() {
        fetch('/strategy_bot/results')
            .then(response => response.json())
            .then(data => {
                const resultsTableBody = document.getElementById('results-table-body');
                resultsTableBody.innerHTML = '';

                if (data.results && data.results.length > 0) {
                    // Sort results by performance (highest first)
                    data.results.sort((a, b) => b.performance - a.performance);

                    data.results.forEach(result => {
                        const row = document.createElement('tr');

                        // Version
                        const versionCell = document.createElement('td');
                        versionCell.textContent = result.version || '-';
                        row.appendChild(versionCell);

                        // Strategy ID
                        const strategyCell = document.createElement('td');
                        strategyCell.textContent = result.strategy_id || '-';
                        row.appendChild(strategyCell);

                        // Performance
                        const performanceCell = document.createElement('td');
                        if (result.performance !== undefined) {
                            const performance = parseFloat(result.performance);
                            performanceCell.textContent = performance.toFixed(2) + '%';
                            performanceCell.style.color = performance >= 0 ? 'green' : 'red';
                        } else {
                            performanceCell.textContent = '-';
                        }
                        row.appendChild(performanceCell);

                        // Win Ratio
                        const winRatioCell = document.createElement('td');
                        if (result.win_ratio !== undefined) {
                            winRatioCell.textContent = parseFloat(result.win_ratio).toFixed(2) + '%';
                        } else {
                            winRatioCell.textContent = '-';
                        }
                        row.appendChild(winRatioCell);

                        // Number of Trades
                        const tradesCell = document.createElement('td');
                        tradesCell.textContent = result.number_of_trades || result.num_trades || '-';
                        row.appendChild(tradesCell);

                        // Actions
                        const actionsCell = document.createElement('td');
                        const useButton = document.createElement('button');
                        useButton.textContent = 'Gebruik';
                        useButton.className = 'btn btn-sm btn-primary';
                        useButton.onclick = function() {
                            useStrategy(result.strategy_id);
                        };
                        actionsCell.appendChild(useButton);
                        row.appendChild(actionsCell);

                        resultsTableBody.appendChild(row);
                    });
                } else {
                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.colSpan = 6;
                    cell.textContent = 'Geen resultaten beschikbaar';
                    cell.style.textAlign = 'center';
                    row.appendChild(cell);
                    resultsTableBody.appendChild(row);
                }
            })
            .catch(error => {
                console.error('Error loading results:', error);
            });
    }

    // Function to use a strategy
    function useStrategy(strategyId) {
        // Redirect to backtesting page with the selected strategy
        window.location.href = `/?tab=Backtesting&strategy=${strategyId}`;
    }
</script>
{% endblock %}
