<!DOCTYPE html>
<html>
<head>
    <title>Trade Results</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .trade-summary {
            margin-top: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        .trade-summary h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }

        .summary-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #444;
        }

        .summary-table td:first-child {
            font-weight: bold;
            width: 40%;
        }

        .trade-table-container {
            margin-top: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            overflow-x: auto;
        }

        .trade-table-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .trade-table {
            width: 100%;
            border-collapse: collapse;
        }

        .trade-table th {
            background-color: #333;
            color: #f0f0f0;
            font-weight: bold;
            padding: 10px;
            text-align: left;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .trade-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #444;
        }

        .profit {
            color: #4CAF50;
        }

        .loss {
            color: #f44336;
        }

        .profit-row {
            background-color: rgba(76, 175, 80, 0.1);
        }

        .loss-row {
            background-color: rgba(244, 67, 54, 0.1);
        }

        .chart-container {
            margin-top: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            height: 400px;
        }

        .chart-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .recent-trades-container {
            margin-top: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        .recent-trades-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .recent-trades-table {
            width: 100%;
            border-collapse: collapse;
            font-family: monospace;
            font-size: 14px;
        }

        .recent-trades-table th {
            background-color: #333;
            color: #f0f0f0;
            font-weight: bold;
            padding: 10px;
            text-align: left;
            white-space: nowrap;
        }

        .recent-trades-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #444;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="trade-summary">
            <h3>Trade Summary</h3>
            <table class="summary-table">
                <tr>
                    <td>Strategy:</td>
                    <td>{{ strategy_name }}</td>
                </tr>
                <tr>
                    <td>Total Trades:</td>
                    <td>{{ metrics.number_of_trades }}</td>
                </tr>
                <tr>
                    <td>Win Ratio:</td>
                    <td>{{ metrics.win_ratio_pct }}%</td>
                </tr>
                <tr>
                    <td>Total P/L:</td>
                    <td class="{% if metrics.total_pnl_pct >= 0 %}profit{% else %}loss{% endif %}">{{ metrics.total_pnl_pct }}%</td>
                </tr>
                <tr>
                    <td>Initial Capital:</td>
                    <td>${{ metrics.initial_capital }}</td>
                </tr>
                <tr>
                    <td>Final Capital:</td>
                    <td>${{ metrics.final_capital }}</td>
                </tr>
                <tr>
                    <td>Growth:</td>
                    <td class="{% if metrics.capital_growth_pct >= 0 %}profit{% else %}loss{% endif %}">{{ metrics.capital_growth_pct }}%</td>
                </tr>
            </table>
        </div>

        <div class="recent-trades-container">
            <h3>Recent Trades</h3>
            <table class="recent-trades-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Type</th>
                        <th>Inkoopdatum</th>
                        <th>Inkooptijd</th>
                        <th>Inkoopprijs</th>
                        <th>Verkoopdatum</th>
                        <th>Verkooptijd</th>
                        <th>Verkoopprijs</th>
                        <th>Resultaat</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trade in trades[:5] %}
                    {% set entry_time = trade.entry_time|timestamp_to_datetime %}
                    {% set exit_time = trade.exit_time|timestamp_to_datetime %}
                    {% set price_diff = trade.exit_price - trade.entry_price %}
                    {% set price_diff_pct = (price_diff / trade.entry_price) * 100 %}
                    {% set row_class = 'profit-row' if trade.pnl_amount > 0 else 'loss-row' %}
                    {% set diff_class = 'profit' if price_diff > 0 else 'loss' %}
                    <tr class="{{ row_class }}">
                        <td>{{ loop.index }}</td>
                        <td>{{ trade.position_type|default('long')|upper }}</td>
                        <td>{{ entry_time.strftime('%d-%m-%Y') }}</td>
                        <td>{{ entry_time.strftime('%H:%M') }}</td>
                        <td>${{ "%.2f"|format(trade.entry_price) }}</td>
                        <td>{{ exit_time.strftime('%d-%m-%Y') }}</td>
                        <td>{{ exit_time.strftime('%H:%M') }}</td>
                        <td>${{ "%.2f"|format(trade.exit_price) }}</td>
                        <td class="{{ diff_class }}">{{ '+' if price_diff_pct > 0 else '' }}{{ "%.2f"|format(price_diff_pct) }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="chart-container">
            <h3>Trade Performance</h3>
            <canvas id="tradeChart"></canvas>
        </div>

        <div class="trade-table-container">
            <h3>Trade Details</h3>
            <table class="trade-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Type</th>
                        <th>Inkoop</th>
                        <th>Verkoop</th>
                        <th>Verschil</th>
                    </tr>
                </thead>
                <tbody>
                    {% for trade in trades %}
                    {% set entry_time = trade.entry_time|timestamp_to_datetime %}
                    {% set exit_time = trade.exit_time|timestamp_to_datetime %}
                    {% set price_diff = trade.exit_price - trade.entry_price %}
                    {% set price_diff_pct = (price_diff / trade.entry_price) * 100 %}
                    {% set row_class = 'profit-row' if trade.pnl_amount > 0 else 'loss-row' %}
                    {% set diff_class = 'profit' if price_diff > 0 else 'loss' %}
                    <tr class="{{ row_class }}">
                        <td>{{ loop.index }}</td>
                        <td>{{ trade.position_type|default('long')|upper }}</td>
                        <td>{{ entry_time.strftime('%d-%m-%Y %H:%M') }} @ ${{ "%.2f"|format(trade.entry_price) }}</td>
                        <td>{{ exit_time.strftime('%d-%m-%Y %H:%M') }} @ ${{ "%.2f"|format(trade.exit_price) }}</td>
                        <td class="{{ diff_class }}">{{ '+' if price_diff > 0 else '' }}${{ "%.2f"|format(price_diff|abs) }} ({{ '+' if price_diff_pct > 0 else '' }}{{ "%.2f"|format(price_diff_pct) }}%)</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Extract trade data from the table
            const trades = [
                {% for trade in trades %}
                {% set entry_time = trade.entry_time|timestamp_to_datetime %}
                {% set exit_time = trade.exit_time|timestamp_to_datetime %}
                {% set price_diff_pct = ((trade.exit_price - trade.entry_price) / trade.entry_price) * 100 %}
                {
                    index: {{ loop.index }},
                    entryDate: "{{ entry_time.strftime('%d-%m-%Y') }}",
                    exitDate: "{{ exit_time.strftime('%d-%m-%Y') }}",
                    pnlPercent: {{ price_diff_pct }},
                    isProfit: {{ 'true' if trade.pnl_amount > 0 else 'false' }}
                }{{ ',' if not loop.last else '' }}
                {% endfor %}
            ];

            // Prepare data for the chart
            const labels = trades.map(trade => `Trade ${trade.index}`);
            const data = trades.map(trade => trade.pnlPercent);
            const backgroundColors = trades.map(trade => trade.isProfit ? 'rgba(76, 175, 80, 0.6)' : 'rgba(244, 67, 54, 0.6)');
            const borderColors = trades.map(trade => trade.isProfit ? 'rgba(76, 175, 80, 1)' : 'rgba(244, 67, 54, 1)');

            // Create the chart
            const ctx = document.getElementById('tradeChart').getContext('2d');
            const tradeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Trade P/L (%)',
                        data: data,
                        backgroundColor: backgroundColors,
                        borderColor: borderColors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#e0e0e0'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#e0e0e0'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: '#e0e0e0'
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
