from py_ecc.fields import (
    optimized_bls12_381_FQ as FQ,
    optimized_bls12_381_FQ2 as FQ2,
)

#
# Ciphersuite BLS12381G2-SHA256-SSWU-RO parameters
#
ISO_3_A = FQ2([0, 240])
ISO_3_B = FQ2([1012, 1012])
ISO_3_Z = FQ2([-2, -1])
P_MINUS_9_DIV_16 = 1001205140483106588246484290269935788605945006208159541241399033561623546780709821462541004956387089373434649096260670658193992783731681621012512651314777238193313314641988297376025498093520728838658813979860931248214124593092835  # noqa: E501

EV1 = 1015919005498129635886032702454337503112659152043614931979881174103627376789972962005013361970813319613593700736144  # noqa: E501
EV2 = 1244231661155348484223428017511856347821538750986231559855759541903146219579071812422210818684355842447591283616181  # noqa: E501
EV3 = 1646015993121829755895883253076789309308090876275172350194834453434199515639474951814226234213676147507404483718679  # noqa: E501
EV4 = 1637752706019426886789797193293828301565549384974986623510918743054325021588194075665960171838131772227885159387073  # noqa: E501
ETAS = [FQ2([EV1, EV2]), FQ2([-EV2, EV1]), FQ2([EV3, EV4]), FQ2([-EV4, EV3])]

RV1 = 1028732146235106349975324479215795277384839936929757896155643118032610843298655225875571310552543014690878354869257  # noqa: E501
POSITIVE_EIGHTH_ROOTS_OF_UNITY = (
    FQ2([1, 0]),
    FQ2([0, 1]),
    FQ2([RV1, RV1]),
    FQ2([RV1, -RV1]),
)

# X Numerator
ISO_3_K_1_0_VAL = 889424345604814976315064405719089812568196182208668418962679585805340366775741747653930584250892369786198727235542  # noqa: E501
ISO_3_K_1_0 = FQ2([ISO_3_K_1_0_VAL, ISO_3_K_1_0_VAL])  # noqa: E501
ISO_3_K_1_1 = FQ2(
    [
        0,
        2668273036814444928945193217157269437704588546626005256888038757416021100327225242961791752752677109358596181706522,  # noqa: E501
    ]
)
ISO_3_K_1_2 = FQ2(
    [
        2668273036814444928945193217157269437704588546626005256888038757416021100327225242961791752752677109358596181706526,  # noqa: E501
        1334136518407222464472596608578634718852294273313002628444019378708010550163612621480895876376338554679298090853261,  # noqa: E501
    ]
)
ISO_3_K_1_3 = FQ2(
    [
        3557697382419259905260257622876359250272784728834673675850718343221361467102966990615722337003569479144794908942033,  # noqa: E501
        0,
    ]
)
ISO_3_X_NUMERATOR = (ISO_3_K_1_0, ISO_3_K_1_1, ISO_3_K_1_2, ISO_3_K_1_3)

# X Denominator
ISO_3_K_2_0 = FQ2(
    [
        0,
        4002409555221667393417789825735904156556882819939007885332058136124031650490837864442687629129015664037894272559715,  # noqa: E501
    ]
)
ISO_3_K_2_1 = FQ2(
    [
        12,
        4002409555221667393417789825735904156556882819939007885332058136124031650490837864442687629129015664037894272559775,  # noqa: E501
    ]
)
ISO_3_K_2_2 = FQ2.one()
ISO_3_K_2_3 = FQ2.zero()
ISO_3_X_DENOMINATOR = (ISO_3_K_2_0, ISO_3_K_2_1, ISO_3_K_2_2, ISO_3_K_2_3)

# Y Numerator
ISO_3_K_3_0_VAL = 3261222600550988246488569487636662646083386001431784202863158481286248011511053074731078808919938689216061999863558  # noqa: E501
ISO_3_K_3_0 = FQ2([ISO_3_K_3_0_VAL, ISO_3_K_3_0_VAL])  # noqa: E501
ISO_3_K_3_1 = FQ2(
    [
        0,
        889424345604814976315064405719089812568196182208668418962679585805340366775741747653930584250892369786198727235518,  # noqa: E501
    ]
)
ISO_3_K_3_2 = FQ2(
    [
        2668273036814444928945193217157269437704588546626005256888038757416021100327225242961791752752677109358596181706524,  # noqa: E501
        1334136518407222464472596608578634718852294273313002628444019378708010550163612621480895876376338554679298090853263,  # noqa: E501
    ]
)
ISO_3_K_3_3 = FQ2(
    [
        2816510427748580758331037284777117739799287910327449993381818688383577828123182200904113516794492504322962636245776,  # noqa: E501
        0,
    ]
)
ISO_3_Y_NUMERATOR = (ISO_3_K_3_0, ISO_3_K_3_1, ISO_3_K_3_2, ISO_3_K_3_3)

# Y Denominator
ISO_3_K_4_0_VAL = 4002409555221667393417789825735904156556882819939007885332058136124031650490837864442687629129015664037894272559355  # noqa: E501
ISO_3_K_4_0 = FQ2([ISO_3_K_4_0_VAL, ISO_3_K_4_0_VAL])  # noqa: E501
ISO_3_K_4_1 = FQ2(
    [
        0,
        4002409555221667393417789825735904156556882819939007885332058136124031650490837864442687629129015664037894272559571,  # noqa: E501
    ]
)
ISO_3_K_4_2 = FQ2(
    [
        18,
        4002409555221667393417789825735904156556882819939007885332058136124031650490837864442687629129015664037894272559769,  # noqa: E501
    ]
)
ISO_3_K_4_3 = FQ2.one()
ISO_3_Y_DENOMINATOR = (ISO_3_K_4_0, ISO_3_K_4_1, ISO_3_K_4_2, ISO_3_K_4_3)

ISO_3_MAP_COEFFICIENTS = (
    ISO_3_X_NUMERATOR,
    ISO_3_X_DENOMINATOR,
    ISO_3_Y_NUMERATOR,
    ISO_3_Y_DENOMINATOR,
)

# from https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-hash-to-curve-09#section-8.8.2  # noqa: E501
H_EFF_G2 = 209869847837335686905080341498658477663839067235703451875306851526599783796572738804459333109033834234622528588876978987822447936461846631641690358257586228683615991308971558879306463436166481  # noqa: E501

# G1
P_MINUS_3_DIV_4 = (FQ.field_modulus - 3) // 4
SQRT_MINUS_11_CUBED = FQ(
    0x3D689D1E0E762CEF9F2BEC6130316806B4C80EDA6FC10CE77AE83EAB1EA8B8B8A407C9C6DB195E06F2DBEABC2BAEFF5  # noqa: E501
)
ISO_11_Z = FQ(11)
ISO_11_A = FQ(
    0x144698A3B8E9433D693A02C96D4982B0EA985383EE66A8D8E8981AEFD881AC98936F8DA0E0F97F5CF428082D584C1D  # noqa: E501
)
ISO_11_B = FQ(
    0x12E2908D11688030018B12E8753EEE3B2016C1F0F24F4070A0B9C14FCEF35EF55A23215A316CEAA5D1CC48E98E172BE0  # noqa: E501
)


ISO_11_X_NUMERATOR = (
    FQ(
        0x11A05F2B1E833340B809101DD99815856B303E88A2D7005FF2627B56CDB4E2C85610C2D5F2E62D6EAEAC1662734649B7  # noqa: E501
    ),
    FQ(
        0x17294ED3E943AB2F0588BAB22147A81C7C17E75B2F6A8417F565E33C70D1E86B4838F2A6F318C356E834EEF1B3CB83BB  # noqa: E501
    ),
    FQ(
        0xD54005DB97678EC1D1048C5D10A9A1BCE032473295983E56878E501EC68E25C958C3E3D2A09729FE0179F9DAC9EDCB0  # noqa: E501
    ),
    FQ(
        0x1778E7166FCC6DB74E0609D307E55412D7F5E4656A8DBF25F1B33289F1B330835336E25CE3107193C5B388641D9B6861  # noqa: E501
    ),
    FQ(
        0xE99726A3199F4436642B4B3E4118E5499DB995A1257FB3F086EEB65982FAC18985A286F301E77C451154CE9AC8895D9  # noqa: E501
    ),
    FQ(
        0x1630C3250D7313FF01D1201BF7A74AB5DB3CB17DD952799B9ED3AB9097E68F90A0870D2DCAE73D19CD13C1C66F652983  # noqa: E501
    ),
    FQ(
        0xD6ED6553FE44D296A3726C38AE652BFB11586264F0F8CE19008E218F9C86B2A8DA25128C1052ECADDD7F225A139ED84  # noqa: E501
    ),
    FQ(
        0x17B81E7701ABDBE2E8743884D1117E53356DE5AB275B4DB1A682C62EF0F2753339B7C8F8C8F475AF9CCB5618E3F0C88E  # noqa: E501
    ),
    FQ(
        0x80D3CF1F9A78FC47B90B33563BE990DC43B756CE79F5574A2C596C928C5D1DE4FA295F296B74E956D71986A8497E317  # noqa: E501
    ),
    FQ(
        0x169B1F8E1BCFA7C42E0C37515D138F22DD2ECB803A0C5C99676314BAF4BB1B7FA3190B2EDC0327797F241067BE390C9E  # noqa: E501
    ),
    FQ(
        0x10321DA079CE07E272D8EC09D2565B0DFA7DCCDDE6787F96D50AF36003B14866F69B771F8C285DECCA67DF3F1605FB7B  # noqa: E501
    ),
    FQ(
        0x6E08C248E260E70BD1E962381EDEE3D31D79D7E22C837BC23C0BF1BC24C6B68C24B1B80B64D391FA9C8BA2E8BA2D229  # noqa: E501
    ),
)
ISO_11_X_DENOMINATOR = (
    FQ(
        0x8CA8D548CFF19AE18B2E62F4BD3FA6F01D5EF4BA35B48BA9C9588617FC8AC62B558D681BE343DF8993CF9FA40D21B1C  # noqa: E501
    ),
    FQ(
        0x12561A5DEB559C4348B4711298E536367041E8CA0CF0800C0126C2588C48BF5713DAA8846CB026E9E5C8276EC82B3BFF  # noqa: E501
    ),
    FQ(
        0xB2962FE57A3225E8137E629BFF2991F6F89416F5A718CD1FCA64E00B11ACEACD6A3D0967C94FEDCFCC239BA5CB83E19  # noqa: E501
    ),
    FQ(
        0x3425581A58AE2FEC83AAFEF7C40EB545B08243F16B1655154CCA8ABC28D6FD04976D5243EECF5C4130DE8938DC62CD8  # noqa: E501
    ),
    FQ(
        0x13A8E162022914A80A6F1D5F43E7A07DFFDFC759A12062BB8D6B44E833B306DA9BD29BA81F35781D539D395B3532A21E  # noqa: E501
    ),
    FQ(
        0xE7355F8E4E667B955390F7F0506C6E9395735E9CE9CAD4D0A43BCEF24B8982F7400D24BC4228F11C02DF9A29F6304A5  # noqa: E501
    ),
    FQ(
        0x772CAACF16936190F3E0C63E0596721570F5799AF53A1894E2E073062AEDE9CEA73B3538F0DE06CEC2574496EE84A3A  # noqa: E501
    ),
    FQ(
        0x14A7AC2A9D64A8B230B3F5B074CF01996E7F63C21BCA68A81996E1CDF9822C580FA5B9489D11E2D311F7D99BBDCC5A5E  # noqa: E501
    ),
    FQ(
        0xA10ECF6ADA54F825E920B3DAFC7A3CCE07F8D1D7161366B74100DA67F39883503826692ABBA43704776EC3A79A1D641  # noqa: E501
    ),
    FQ(
        0x95FC13AB9E92AD4476D6E3EB3A56680F682B4EE96F7D03776DF533978F31C1593174E4B4B7865002D6384D168ECDD0A  # noqa: E501
    ),
    FQ(1),
)
ISO_11_Y_NUMERATOR = (
    FQ(
        0x90D97C81BA24EE0259D1F094980DCFA11AD138E48A869522B52AF6C956543D3CD0C7AEE9B3BA3C2BE9845719707BB33  # noqa: E501
    ),
    FQ(
        0x134996A104EE5811D51036D776FB46831223E96C254F383D0F906343EB67AD34D6C56711962FA8BFE097E75A2E41C696  # noqa: E501
    ),
    FQ(
        0xCC786BAA966E66F4A384C86A3B49942552E2D658A31CE2C344BE4B91400DA7D26D521628B00523B8DFE240C72DE1F6  # noqa: E501
    ),
    FQ(
        0x1F86376E8981C217898751AD8746757D42AA7B90EEB791C09E4A3EC03251CF9DE405ABA9EC61DECA6355C77B0E5F4CB  # noqa: E501
    ),
    FQ(
        0x8CC03FDEFE0FF135CAF4FE2A21529C4195536FBE3CE50B879833FD221351ADC2EE7F8DC099040A841B6DAECF2E8FEDB  # noqa: E501
    ),
    FQ(
        0x16603FCA40634B6A2211E11DB8F0A6A074A7D0D4AFADB7BD76505C3D3AD5544E203F6326C95A807299B23AB13633A5F0  # noqa: E501
    ),
    FQ(
        0x4AB0B9BCFAC1BBCB2C977D027796B3CE75BB8CA2BE184CB5231413C4D634F3747A87AC2460F415EC961F8855FE9D6F2  # noqa: E501
    ),
    FQ(
        0x987C8D5333AB86FDE9926BD2CA6C674170A05BFE3BDD81FFD038DA6C26C842642F64550FEDFE935A15E4CA31870FB29  # noqa: E501
    ),
    FQ(
        0x9FC4018BD96684BE88C9E221E4DA1BB8F3ABD16679DC26C1E8B6E6A1F20CABE69D65201C78607A360370E577BDBA587  # noqa: E501
    ),
    FQ(
        0xE1BBA7A1186BDB5223ABDE7ADA14A23C42A0CA7915AF6FE06985E7ED1E4D43B9B3F7055DD4EBA6F2BAFAAEBCA731C30  # noqa: E501
    ),
    FQ(
        0x19713E47937CD1BE0DFD0B8F1D43FB93CD2FCBCB6CAF493FD1183E416389E61031BF3A5CCE3FBAFCE813711AD011C132  # noqa: E501
    ),
    FQ(
        0x18B46A908F36F6DEB918C143FED2EDCC523559B8AAF0C2462E6BFE7F911F643249D9CDF41B44D606CE07C8A4D0074D8E  # noqa: E501
    ),
    FQ(
        0xB182CAC101B9399D155096004F53F447AA7B12A3426B08EC02710E807B4633F06C851C1919211F20D4C04F00B971EF8  # noqa: E501
    ),
    FQ(
        0x245A394AD1ECA9B72FC00AE7BE315DC757B3B080D4C158013E6632D3C40659CC6CF90AD1C232A6442D9D3F5DB980133  # noqa: E501
    ),
    FQ(
        0x5C129645E44CF1102A159F748C4A3FC5E673D81D7E86568D9AB0F5D396A7CE46BA1049B6579AFB7866B1E715475224B  # noqa: E501
    ),
    FQ(
        0x15E6BE4E990F03CE4EA50B3B42DF2EB5CB181D8F84965A3957ADD4FA95AF01B2B665027EFEC01C7704B456BE69C8B604  # noqa: E501
    ),
)
ISO_11_Y_DENOMINATOR = (
    FQ(
        0x16112C4C3A9C98B252181140FAD0EAE9601A6DE578980BE6EEC3232B5BE72E7A07F3688EF60C206D01479253B03663C1  # noqa: E501
    ),
    FQ(
        0x1962D75C2381201E1A0CBD6C43C348B885C84FF731C4D59CA4A10356F453E01F78A4260763529E3532F6102C2E49A03D  # noqa: E501
    ),
    FQ(
        0x58DF3306640DA276FAAAE7D6E8EB15778C4855551AE7F310C35A5DD279CD2ECA6757CD636F96F891E2538B53DBF67F2  # noqa: E501
    ),
    FQ(
        0x16B7D288798E5395F20D23BF89EDB4D1D115C5DBDDBCD30E123DA489E726AF41727364F2C28297ADA8D26D98445F5416  # noqa: E501
    ),
    FQ(
        0xBE0E079545F43E4B00CC912F8228DDCC6D19C9F0F69BBB0542EDA0FC9DEC916A20B15DC0FD2EDEDDA39142311A5001D  # noqa: E501
    ),
    FQ(
        0x8D9E5297186DB2D9FB266EAAC783182B70152C65550D881C5ECD87B6F0F5A6449F38DB9DFA9CCE202C6477FAAF9B7AC  # noqa: E501
    ),
    FQ(
        0x166007C08A99DB2FC3BA8734ACE9824B5EECFDFA8D0CF8EF5DD365BC400A0051D5FA9C01A58B1FB93D1A1399126A775C  # noqa: E501
    ),
    FQ(
        0x16A3EF08BE3EA7EA03BCDDFABBA6FF6EE5A4375EFA1F4FD7FEB34FD206357132B920F5B00801DEE460EE415A15812ED9  # noqa: E501
    ),
    FQ(
        0x1866C8ED336C61231A1BE54FD1D74CC4F9FB0CE4C6AF5920ABC5750C4BF39B4852CFE2F7BB9248836B233D9D55535D4A  # noqa: E501
    ),
    FQ(
        0x167A55CDA70A6E1CEA820597D94A84903216F763E13D87BB5308592E7EA7D4FBC7385EA3D529B35E346EF48BB8913F55  # noqa: E501
    ),
    FQ(
        0x4D2F259EEA405BD48F010A01AD2911D9C6DD039BB61A6290E591B36E636A5C871A5C29F4F83060400F8B49CBA8F6AA8  # noqa: E501
    ),
    FQ(
        0xACCBB67481D033FF5852C1E48C50C477F94FF8AEFCE42D28C0F9A88CEA7913516F968986F7EBBEA9684B529E2561092  # noqa: E501
    ),
    FQ(
        0xAD6B9514C767FE3C3613144B45F1496543346D98ADF02267D5CEEF9A00D9B8693000763E3B90AC11E99B138573345CC  # noqa: E501
    ),
    FQ(
        0x2660400EB2E4F3B628BDD0D53CD76F2BF565B94E72927C1CB748DF27942480E420517BD8714CC80D1FADC1326ED06F7  # noqa: E501
    ),
    FQ(
        0xE0FA1D816DDC03E6B24255E0D7819C171C40F65E273B853324EFCD6356CAA205CA2F570F13497804415473A1D634B8F  # noqa: E501
    ),
    FQ(1),
)

ISO_11_MAP_COEFFICIENTS = (
    ISO_11_X_NUMERATOR,
    ISO_11_X_DENOMINATOR,
    ISO_11_Y_NUMERATOR,
    ISO_11_Y_DENOMINATOR,
)

# from https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-hash-to-curve-09#section-8.8.1  # noqa: E501
H_EFF_G1 = 0xD201000000010001
