Metadata-Version: 2.4
Name: py-ecc
Version: 8.0.0
Summary: py-ecc: Elliptic curve crypto in python including secp256k1, alt_bn128, and bls12_381
Home-page: https://github.com/ethereum/py_ecc
Author: The Ethereum Foundation
Author-email: snakecharm<PERSON>@ethereum.org
License: MIT
Keywords: ethereum
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.8, <4
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: eth-typing>=3.0.0
Requires-Dist: eth-utils>=2.0.0
Provides-Extra: dev
Requires-Dist: build>=0.9.0; extra == "dev"
Requires-Dist: bump_my_version>=0.19.0; extra == "dev"
Requires-Dist: ipython; extra == "dev"
Requires-Dist: mypy==1.10.0; extra == "dev"
Requires-Dist: pre-commit>=3.4.0; extra == "dev"
Requires-Dist: tox>=4.0.0; extra == "dev"
Requires-Dist: twine; extra == "dev"
Requires-Dist: wheel; extra == "dev"
Requires-Dist: sphinx>=6.0.0; extra == "dev"
Requires-Dist: sphinx-autobuild>=2021.3.14; extra == "dev"
Requires-Dist: sphinx_rtd_theme>=1.0.0; extra == "dev"
Requires-Dist: towncrier<25,>=24; extra == "dev"
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-xdist>=2.4.0; extra == "dev"
Provides-Extra: docs
Requires-Dist: sphinx>=6.0.0; extra == "docs"
Requires-Dist: sphinx-autobuild>=2021.3.14; extra == "docs"
Requires-Dist: sphinx_rtd_theme>=1.0.0; extra == "docs"
Requires-Dist: towncrier<25,>=24; extra == "docs"
Provides-Extra: test
Requires-Dist: pytest>=7.0.0; extra == "test"
Requires-Dist: pytest-xdist>=2.4.0; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# py_ecc

[![Join the conversation on Discord](https://img.shields.io/discord/809793915578089484?color=blue&label=chat&logo=discord&logoColor=white)](https://discord.gg/GHryRvPB84)
[![Build Status](https://circleci.com/gh/ethereum/py_ecc.svg?style=shield)](https://circleci.com/gh/ethereum/py_ecc)
[![PyPI version](https://badge.fury.io/py/py-ecc.svg)](https://badge.fury.io/py/py-ecc)
[![Python versions](https://img.shields.io/pypi/pyversions/py-ecc.svg)](https://pypi.python.org/pypi/py-ecc)

Elliptic curve crypto in python including secp256k1, alt_bn128, and bls12_381.

> **Warning**: This library contains some experimental code and has **NOT** been audited.

Read the [documentation](https://py-ecc.readthedocs.io/).

View the [change log](https://py-ecc.readthedocs.io/en/latest/release_notes.html).

## Installation

```sh
python -m pip install py_ecc
```
