"""
Improved minimal backtester for trading strategies.
This is a simplified version that focuses on reliability.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import traceback
import os
import importlib.util
from typing import Dict, Any, List, Optional, Tuple, Callable

from hyperliquid_api import fetch_historical_data

def run_backtest(pair: str, strategy_id: str, start_date: str, end_date: str, custom_params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Run a backtest for a given strategy on historical data.

    Args:
        pair: Trading pair symbol (e.g., 'BTC-USD')
        strategy_id: ID of the strategy to use
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        custom_params: Optional custom parameters for the strategy

    Returns:
        Dict[str, Any]: Backtest results
    """
    try:
        print(f"Starting backtest for {strategy_id} on {pair} from {start_date} to {end_date}")

        # Convert date strings to timestamps
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            # For end_date, include the whole day by setting time to 23:59:59
            end_dt = datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59)

            # Adjust end date to current time if it's in the future
            current_dt = datetime.now()
            if end_dt > current_dt:
                end_dt = current_dt
                print(f"Adjusted end date to current time: {end_date}")

            # Convert to millisecond timestamps
            start_time_ms = int(start_dt.timestamp() * 1000)
            end_time_ms = int(end_dt.timestamp() * 1000)

            if start_time_ms >= end_time_ms:
                return {'error': 'Start date must be before end date.'}

        except ValueError as e:
            return {'error': f"Invalid date format: {str(e)}. Please use 'YYYY-MM-DD'."}

        # Fetch historical data
        print(f"Fetching data for {pair} from {start_date} to {end_date}")
        print(f"Fetching candles for {pair} from {start_time_ms} to {end_time_ms}")
        data = fetch_historical_data(pair, start_time_ms, end_time_ms)

        if data is None or len(data) == 0:
            return {'error': f"No data available for {pair} from {start_date} to {end_date}"}

        # Prepare data for strategy
        df = data.copy()

        # Select strategy function based on strategy_id
        strategy_func = get_strategy_function(strategy_id)
        if not strategy_func:
            return {'error': f"Strategy '{strategy_id}' not found"}

        # Run the strategy
        try:
            # Pass custom parameters if provided
            if custom_params and strategy_id == 'master_strategy':
                print(f"Running strategy with custom parameters: {custom_params}")
                df = strategy_func(df, custom_params=custom_params)
            else:
                df = strategy_func(df)
            print(f"Strategy executed successfully")
        except Exception as e:
            traceback.print_exc()
            return {'error': f"Error executing strategy: {str(e)}"}

        # Process signals and generate trades
        df, signals, trades = process_signals_and_trades(df)

        # Calculate performance metrics
        result = calculate_performance_metrics(df, trades)

        # Add signals and trades to result
        result['signals'] = signals
        result['trades'] = trades

        # Add historical data for charting
        historical_data = []
        for idx, row in df.iterrows():
            historical_data.append({
                'timestamp': int(idx.timestamp() * 1000),
                'open': row['open'],
                'high': row['high'],
                'low': row['low'],
                'close': row['close'],
                'volume': row['volume']
            })
        result['historical_data'] = historical_data

        print(f"Backtest completed successfully with {len(trades)} trades")
        return result

    except Exception as e:
        traceback.print_exc()
        return {'error': f"Backtest failed: {str(e)}"}

def get_strategy_function(strategy_id: str) -> Optional[Callable]:
    """
    Get the strategy function based on the strategy ID.

    Args:
        strategy_id: ID of the strategy

    Returns:
        Strategy function or None if not found
    """
    # Import strategies module to access all strategy functions
    try:
        from strategies import STRATEGY_PARAMS
        import strategies

        # Check if the strategy exists in the strategies module
        if strategy_id in STRATEGY_PARAMS:
            # Get the strategy function from the strategies module
            if hasattr(strategies, strategy_id):
                strategy_func = getattr(strategies, strategy_id)

                # Create a wrapper function to match the expected interface
                def strategy_wrapper(data, custom_params=None):
                    # Get the parameters for this strategy
                    params = STRATEGY_PARAMS.get(strategy_id, {})

                    # Special handling for master_strategy
                    if strategy_id == 'master_strategy':
                        # Use default parameters for master strategy if not provided
                        master_params = {
                            'use_sma': True,
                            'use_rsi': True,
                            'use_macd': True,
                            'use_bollinger': True,
                            'use_stochastic': False,
                            'use_volume': False,
                            'use_pattern_recognition': False,
                            'use_support_resistance': False,
                            'weight_sma_buy': 1.0,
                            'weight_sma_sell': 1.0,
                            'weight_rsi_buy': 1.0,
                            'weight_rsi_sell': 1.0,
                            'weight_macd_buy': 1.0,
                            'weight_macd_sell': 1.0,
                            'weight_bollinger_buy': 1.0,
                            'weight_bollinger_sell': 1.0,
                            'weight_stochastic_buy': 1.0,
                            'weight_stochastic_sell': 1.0,
                            'weight_volume_buy': 1.0,
                            'weight_volume_sell': 1.0,
                            'weight_pattern_buy': 1.0,
                            'weight_pattern_sell': 1.0,
                            'weight_support_resistance_buy': 1.0,
                            'weight_support_resistance_sell': 1.0,
                            'threshold_buy': 0.3,  # Lower threshold to generate more signals
                            'threshold_sell': 0.3,  # Lower threshold to generate more signals
                            'sma_depends_on_volume': 0.0,
                            'rsi_depends_on_sma': 0.0,
                            'macd_depends_on_rsi': 0.0,
                            'bollinger_depends_on_volume': 0.0,
                            'sma_short_window': 10,
                            'sma_long_window': 20,
                            'rsi_period': 14,
                            'rsi_oversold': 30,
                            'rsi_overbought': 70,
                            'macd_fast_period': 12,
                            'macd_slow_period': 26,
                            'macd_signal_period': 9,
                            'bollinger_window': 20,
                            'bollinger_num_std': 2,
                            'stochastic_k_period': 14,
                            'stochastic_d_period': 3,
                            'stochastic_slowing': 3,
                            'stochastic_oversold': 20,
                            'stochastic_overbought': 80,
                            'volume_window': 20,
                            'volume_threshold': 1.5,
                            'pattern_window': 5,
                            'support_resistance_window': 50,
                            'support_resistance_threshold': 0.02,
                            'take_profit': 5.0,
                            'stop_loss': 2.0,
                            'timeframe_multiplier': 1.0
                        }

                        # Update with parameters from STRATEGY_PARAMS
                        master_params.update(params)

                        # Update with custom parameters if provided
                        if custom_params:
                            print(f"Applying custom parameters: {custom_params}")
                            master_params.update(custom_params)

                        print(f"Running master strategy with parameters: {master_params}")

                        # Call the master strategy function with the data and parameters
                        buy_signals, sell_signals = strategy_func(data, **master_params)

                        # Convert the signals to the format expected by the backtester
                        data_copy = data.copy()
                        data_copy['signal'] = 0
                        data_copy.loc[buy_signals, 'signal'] = 1
                        data_copy.loc[sell_signals, 'signal'] = -1

                        # Print some debug info
                        num_buy = buy_signals.sum()
                        num_sell = sell_signals.sum()
                        print(f"Master strategy generated {num_buy} buy signals and {num_sell} sell signals")

                        return data_copy

                    # For other strategies
                    try:
                        # First try with all parameters
                        buy_signals, sell_signals = strategy_func(data, **params)
                    except TypeError as e:
                        # If that fails, try with just the specific parameters the function accepts
                        import inspect
                        sig = inspect.signature(strategy_func)
                        # Get the parameter names from the function signature
                        param_names = list(sig.parameters.keys())
                        # Remove 'data' from the list if it's there
                        if 'data' in param_names:
                            param_names.remove('data')
                        # Filter params to only include those in the function signature
                        filtered_params = {k: v for k, v in params.items() if k in param_names}
                        # Try again with filtered parameters
                        buy_signals, sell_signals = strategy_func(data, **filtered_params)

                    # Convert the signals to the format expected by the backtester
                    data = data.copy()
                    data['signal'] = 0
                    data.loc[buy_signals, 'signal'] = 1
                    data.loc[sell_signals, 'signal'] = -1

                    return data

                return strategy_wrapper

    except Exception as e:
        print(f"Error loading strategy from strategies module: {str(e)}")
        traceback.print_exc()

    # Define built-in strategies as fallback
    if strategy_id == 'simple_sma_cross':
        return simple_sma_cross
    elif strategy_id == 'rsi_strategy':
        return rsi_strategy

    # Try to load from strategies directory
    try:
        strategies_dir = os.path.join(os.path.dirname(__file__), 'strategies')
        strategy_file = os.path.join(strategies_dir, f"{strategy_id}.py")

        if os.path.exists(strategy_file):
            spec = importlib.util.spec_from_file_location(strategy_id, strategy_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            if hasattr(module, 'run_strategy'):
                return module.run_strategy
    except Exception as e:
        print(f"Error loading strategy from file: {str(e)}")

    return None

def simple_sma_cross(data: pd.DataFrame) -> pd.DataFrame:
    """
    Simple Moving Average Crossover Strategy

    Args:
        data: DataFrame with OHLCV data

    Returns:
        DataFrame with signals
    """
    print(f"Running simple SMA crossover strategy")
    df = data.copy()

    # Calculate SMAs
    df['short_sma'] = df['close'].rolling(window=10).mean()
    df['long_sma'] = df['close'].rolling(window=50).mean()

    # Generate signals
    df['signal'] = 0

    # Buy signal: Short SMA crosses above Long SMA
    df.loc[(df['short_sma'] > df['long_sma']) & (df['short_sma'].shift(1) <= df['long_sma'].shift(1)), 'signal'] = 1

    # Sell signal: Short SMA crosses below Long SMA
    df.loc[(df['short_sma'] < df['long_sma']) & (df['short_sma'].shift(1) >= df['long_sma'].shift(1)), 'signal'] = -1

    # Remove NaN values
    df = df.dropna()

    return df

def rsi_strategy(data: pd.DataFrame) -> pd.DataFrame:
    """
    RSI Strategy

    Args:
        data: DataFrame with OHLCV data

    Returns:
        DataFrame with signals
    """
    print(f"Running RSI strategy")
    df = data.copy()

    # Calculate RSI
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0).rolling(window=14).mean()
    loss = -delta.where(delta < 0, 0).rolling(window=14).mean()

    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))

    # Generate signals
    df['signal'] = 0

    # Buy signal: RSI crosses below oversold level (30)
    df.loc[(df['rsi'] < 30) & (df['rsi'].shift(1) >= 30), 'signal'] = 1

    # Sell signal: RSI crosses above overbought level (70)
    df.loc[(df['rsi'] > 70) & (df['rsi'].shift(1) <= 70), 'signal'] = -1

    # Remove NaN values
    df = df.dropna()

    return df

def process_signals_and_trades(df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Process signals and generate trades

    Args:
        df: DataFrame with signals

    Returns:
        Tuple of (DataFrame, signals list, trades list)
    """
    # Calculate signal changes
    df['signal_change'] = df['signal'].diff().fillna(0)

    # Initialize lists for signals and trades
    signals = []
    trades = []

    # Initialize variables for tracking positions
    active_position = False
    entry_price = 0
    entry_time = None
    position_type = None
    trade_id = 0

    # Process each row
    for idx, row in df.iterrows():
        # Check for signal changes
        if row['signal_change'] != 0:
            signal_type = None

            # Buy signal
            if row['signal_change'] > 0 and row['signal'] == 1:
                signal_type = 'buy'
                signals.append({
                    'timestamp': int(idx.timestamp() * 1000),
                    'price': row['close'],
                    'type': 'buy'
                })

            # Sell signal
            elif row['signal_change'] < 0 and row['signal'] == -1:
                signal_type = 'sell'
                signals.append({
                    'timestamp': int(idx.timestamp() * 1000),
                    'price': row['close'],
                    'type': 'sell'
                })

            # Handle trade logic
            if signal_type == 'buy' and not active_position:
                # Open long position
                active_position = True
                entry_price = row['close']
                entry_time = idx
                position_type = 'long'
                trade_id += 1
            elif signal_type == 'sell' and active_position and position_type == 'long':
                # Close long position
                exit_price = row['close']
                pnl_amount = exit_price - entry_price
                # Calculate percentage profit/loss
                if position_type.lower() == 'long':
                    pnl_percent = (exit_price - entry_price) / entry_price * 100
                else:  # Short position
                    pnl_percent = (entry_price - exit_price) / entry_price * 100

                # Calculate shares based on $10,000 initial capital
                initial_capital = 10000
                shares = initial_capital / entry_price
                profit_usd = pnl_amount * shares

                # Add trade to list
                trades.append({
                    'id': trade_id,
                    'entry_time': int(entry_time.timestamp() * 1000),
                    'exit_time': int(idx.timestamp() * 1000),
                    'entry_price': float(entry_price),  # Ensure it's a float
                    'exit_price': float(exit_price),    # Ensure it's a float
                    'position_type': position_type,
                    'shares': float(shares),            # Ensure it's a float
                    'pnl_amount': float(round(pnl_amount, 2)),
                    'pnl_percent': float(round(pnl_percent, 2)),
                    'profit_usd': float(round(profit_usd, 2))
                })

                # Reset position
                active_position = False
                entry_price = 0
                entry_time = None
                position_type = None

    # Calculate strategy returns
    df['returns'] = df['close'].pct_change()
    df['strategy_returns'] = df['signal'].shift(1) * df['returns']
    df['cumulative_returns'] = (1 + df['strategy_returns']).cumprod() - 1

    return df, signals, trades

def calculate_performance_metrics(df: pd.DataFrame, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate performance metrics

    Args:
        df: DataFrame with strategy returns
        trades: List of trades

    Returns:
        Dictionary with performance metrics
    """
    # Calculate metrics
    num_trades = len(trades)
    winning_trades = sum(1 for trade in trades if trade['pnl_amount'] > 0)
    losing_trades = sum(1 for trade in trades if trade['pnl_amount'] <= 0)
    win_ratio = winning_trades / num_trades if num_trades > 0 else 0

    # Calculate total PnL
    total_pnl_amount = sum(trade['pnl_amount'] for trade in trades)
    total_pnl_percent = df['cumulative_returns'].iloc[-1] * 100 if len(df) > 0 else 0

    # Calculate performance with initial capital
    initial_capital = 10000  # $10,000 USD
    final_capital = initial_capital

    # Calculate final capital based on trades
    for trade in trades:
        if 'profit_usd' in trade:
            final_capital += trade['profit_usd']

    # Print sample trades for debugging
    if num_trades > 0:
        print("\nSample trades:")
        for i, trade in enumerate(trades[:3], 1):
            entry_time = datetime.fromtimestamp(trade['entry_time'] / 1000)
            exit_time = datetime.fromtimestamp(trade['exit_time'] / 1000)
            print(f"Trade {i}:")
            print(f"  Entry time: {entry_time}")
            print(f"  Exit time: {exit_time}")
            print(f"  Entry price: ${trade['entry_price']}")
            print(f"  Exit price: ${trade['exit_price']}")
            print(f"  Shares: {trade['shares']:.4f}")
            print(f"  P/L: ${trade['pnl_amount']} ({trade['pnl_percent']}%)")

    # Return results
    return {
        'number_of_trades': num_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'win_ratio_pct': round(win_ratio * 100, 2),
        'total_pnl_pct': round(total_pnl_percent, 2),
        'total_pnl_amount': round(total_pnl_amount, 2),
        'initial_capital': initial_capital,
        'final_capital': round(final_capital, 2),
        'capital_growth_pct': round((final_capital - initial_capital) / initial_capital * 100, 2)
    }

if __name__ == "__main__":
    # Test the backtester
    result = run_backtest("BTC-USD", "simple_sma_cross", "2023-01-01", "2023-01-31")
    print(result)
