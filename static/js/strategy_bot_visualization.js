// Strategy-bot Visualization
// This script creates a real-time visualization of the Strategy-bot's activities

// Configuration
const config = {
    canvasWidth: 800,
    canvasHeight: 600,  // Increased height for more information
    backgroundColor: '#1a1a1a',
    gridColor: '#333333',
    textColor: '#f0f0f0',
    accentColor: '#4CAF50',
    errorColor: '#f44336',
    warningColor: '#ff9800',
    infoColor: '#2196F3',
    particleCount: 80,  // More particles
    particleSpeed: 2,
    updateInterval: 100, // ms
    statusUpdateInterval: 1000, // ms
    chartHeight: 120,   // Height for performance chart
};

// Global variables
let canvas, ctx;
let particles = [];
let botStatus = {
    status: 'Niet actief',
    progress: 0,
    running: false,
    results: [],
    current_performance: 0,
    current_win_ratio: 0,
    current_trades: 0,
    iteration: 0,
    total_iterations: 0,
    start_time: null,
    elapsed_time: 0,
    estimated_time_remaining: 0,
    parameter_history: []  // Track parameter changes over time
};
let animationId;
let lastStatusUpdate = 0;
let optimizationSteps = [];
let currentOptimizationStep = 0;
let performanceHistory = [];  // Track performance over time
let winRatioHistory = [];     // Track win ratio over time

// Initialize the visualization
function initVisualization() {
    // Create canvas if it doesn't exist
    if (!canvas) {
        canvas = document.createElement('canvas');
        canvas.width = config.canvasWidth;
        canvas.height = config.canvasHeight;
        canvas.style.display = 'block';
        canvas.style.margin = '20px auto';
        canvas.style.borderRadius = '8px';
        canvas.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';

        // Add canvas to the DOM
        const container = document.getElementById('strategy-bot-visualization');
        if (container) {
            container.innerHTML = '';
            container.appendChild(canvas);
        } else {
            console.error('Strategy-bot visualization container not found');
            return;
        }

        ctx = canvas.getContext('2d');
    }

    // Initialize particles
    initParticles();

    // Define optimization steps for visualization
    optimizationSteps = [
        { name: "Data Collection", description: "Downloading historical price data" },
        { name: "Population Initialization", description: "Creating initial set of strategy parameters" },
        { name: "Fitness Evaluation", description: "Testing strategies against historical data" },
        { name: "Selection", description: "Choosing the best performing strategies" },
        { name: "Crossover", description: "Combining successful strategies" },
        { name: "Mutation", description: "Introducing random variations" },
        { name: "Convergence Check", description: "Checking if optimal solution is found" },
        { name: "Parameter Refinement", description: "Fine-tuning the best parameters" },
        { name: "Final Validation", description: "Confirming strategy performance" },
        { name: "Results Compilation", description: "Preparing final optimization results" }
    ];

    // Start animation loop
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
    animate();

    // Start status updates
    updateStatus();
}

// Initialize particles
function initParticles() {
    particles = [];
    for (let i = 0; i < config.particleCount; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            radius: Math.random() * 3 + 1,
            color: getRandomColor(),
            speedX: (Math.random() - 0.5) * config.particleSpeed,
            speedY: (Math.random() - 0.5) * config.particleSpeed,
            connected: []
        });
    }

    // Create connections between particles
    for (let i = 0; i < particles.length; i++) {
        const connectionCount = Math.floor(Math.random() * 3) + 1;
        for (let j = 0; j < connectionCount; j++) {
            const targetIndex = Math.floor(Math.random() * particles.length);
            if (targetIndex !== i && !particles[i].connected.includes(targetIndex)) {
                particles[i].connected.push(targetIndex);
            }
        }
    }
}

// Get a random color for particles
function getRandomColor() {
    const colors = [
        config.accentColor,
        '#3498db', // Blue
        '#9b59b6', // Purple
        '#f1c40f', // Yellow
        '#e74c3c', // Red
        '#1abc9c', // Teal
    ];
    return colors[Math.floor(Math.random() * colors.length)];
}

// Animation loop
function animate() {
    // Clear canvas
    ctx.fillStyle = config.backgroundColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid
    drawGrid();

    // Update and draw particles
    updateParticles();
    drawParticles();

    // Draw status information
    drawStatusInfo();

    // Draw optimization visualization
    drawOptimizationProcess();

    // Continue animation loop
    animationId = requestAnimationFrame(animate);
}

// Draw background grid
function drawGrid() {
    ctx.strokeStyle = config.gridColor;
    ctx.lineWidth = 0.5;

    // Vertical lines
    for (let x = 0; x < canvas.width; x += 50) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
    }

    // Horizontal lines
    for (let y = 0; y < canvas.height; y += 50) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
    }
}

// Update particle positions
function updateParticles() {
    particles.forEach(particle => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;

        // Bounce off walls
        if (particle.x < 0 || particle.x > canvas.width) {
            particle.speedX *= -1;
        }
        if (particle.y < 0 || particle.y > canvas.height) {
            particle.speedY *= -1;
        }

        // Adjust speed based on bot status
        if (botStatus.running) {
            const speedMultiplier = 1 + (botStatus.progress / 100);
            particle.speedX = (particle.speedX > 0 ? 1 : -1) * Math.min(Math.abs(particle.speedX * speedMultiplier), 3);
            particle.speedY = (particle.speedY > 0 ? 1 : -1) * Math.min(Math.abs(particle.speedY * speedMultiplier), 3);
        } else {
            // Slow down when not running
            particle.speedX *= 0.98;
            particle.speedY *= 0.98;

            // Add some random movement to keep things interesting
            if (Math.random() < 0.02) {
                particle.speedX += (Math.random() - 0.5) * 0.2;
                particle.speedY += (Math.random() - 0.5) * 0.2;
            }
        }
    });
}

// Draw particles and connections
function drawParticles() {
    // Draw connections first
    ctx.lineWidth = 0.5;
    particles.forEach((particle, i) => {
        particle.connected.forEach(targetIndex => {
            const target = particles[targetIndex];

            // Calculate distance
            const dx = target.x - particle.x;
            const dy = target.y - particle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            // Only draw connections within a certain distance
            if (distance < 150) {
                // Gradient based on distance
                const gradient = ctx.createLinearGradient(
                    particle.x, particle.y, target.x, target.y
                );
                gradient.addColorStop(0, particle.color);
                gradient.addColorStop(1, target.color);

                ctx.strokeStyle = gradient;
                ctx.globalAlpha = 1 - (distance / 150);

                ctx.beginPath();
                ctx.moveTo(particle.x, particle.y);
                ctx.lineTo(target.x, target.y);
                ctx.stroke();
            }
        });
    });

    // Reset alpha
    ctx.globalAlpha = 1;

    // Draw particles
    particles.forEach(particle => {
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
        ctx.fill();
    });
}

// Draw status information
function drawStatusInfo() {
    ctx.fillStyle = config.textColor;
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'left';

    // Status text
    ctx.fillText(`Status: ${botStatus.status}`, 20, 30);

    // Progress bar
    const progressBarWidth = canvas.width - 40;
    const progressBarHeight = 20;
    const progressBarX = 20;
    const progressBarY = 50;

    // Draw progress bar background
    ctx.fillStyle = '#333';
    ctx.fillRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);

    // Draw progress
    ctx.fillStyle = getStatusColor();
    ctx.fillRect(progressBarX, progressBarY, progressBarWidth * (botStatus.progress / 100), progressBarHeight);

    // Progress text
    ctx.fillStyle = config.textColor;
    ctx.textAlign = 'center';
    ctx.fillText(`${botStatus.progress}%`, progressBarX + progressBarWidth / 2, progressBarY + 15);

    // Time information
    ctx.textAlign = 'left';
    let timeInfoY = 90;

    if (botStatus.running && botStatus.elapsed_time) {
        // Format elapsed time
        const elapsedHours = Math.floor(botStatus.elapsed_time / 3600);
        const elapsedMinutes = Math.floor((botStatus.elapsed_time % 3600) / 60);
        const elapsedSeconds = botStatus.elapsed_time % 60;
        const elapsedTimeStr = `${elapsedHours.toString().padStart(2, '0')}:${elapsedMinutes.toString().padStart(2, '0')}:${elapsedSeconds.toString().padStart(2, '0')}`;

        // Format estimated time remaining
        const remainingHours = Math.floor(botStatus.estimated_time_remaining / 3600);
        const remainingMinutes = Math.floor((botStatus.estimated_time_remaining % 3600) / 60);
        const remainingSeconds = botStatus.estimated_time_remaining % 60;
        const remainingTimeStr = `${remainingHours.toString().padStart(2, '0')}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;

        ctx.fillText(`Verstreken tijd: ${elapsedTimeStr}`, 20, timeInfoY);
        ctx.fillText(`Geschatte resterende tijd: ${remainingTimeStr}`, canvas.width - 300, timeInfoY);
        timeInfoY += 25;
    }

    // Iteration information
    if (botStatus.running && botStatus.total_iterations > 0) {
        ctx.fillText(`Iteratie: ${botStatus.iteration}/${botStatus.total_iterations}`, 20, timeInfoY);
        timeInfoY += 25;
    }

    // Current optimization metrics
    if (botStatus.running) {
        ctx.fillText(`Huidige prestaties:`, 20, timeInfoY);
        timeInfoY += 25;

        // Current performance
        const perfValue = Number(botStatus.current_performance || 0).toFixed(2);
        ctx.fillStyle = botStatus.current_performance >= 0 ? 'lightgreen' : 'salmon';
        ctx.fillText(`Prestatie: ${perfValue}%`, 40, timeInfoY);
        ctx.fillStyle = config.textColor;

        // Current win ratio
        const winRatioValue = Number(botStatus.current_win_ratio || 0).toFixed(2);
        ctx.fillStyle = botStatus.current_win_ratio >= 50 ? 'lightgreen' : 'salmon';
        ctx.fillText(`Win ratio: ${winRatioValue}%`, canvas.width / 2 - 50, timeInfoY);
        ctx.fillStyle = config.textColor;

        // Current trades
        ctx.fillText(`Aantal trades: ${botStatus.current_trades || 0}`, canvas.width - 200, timeInfoY);
        timeInfoY += 35;
    }

    // Results count
    ctx.fillText(`Resultaten: ${botStatus.results.length}`, 20, timeInfoY);
    timeInfoY += 25;

    // If we have results, show the best ones
    if (botStatus.results.length > 0) {
        // Show best result
        const bestResult = botStatus.results[0]; // Already sorted in updateStatus

        ctx.fillText(`Beste resultaat: ${bestResult.strategy_id}`, 20, timeInfoY);
        timeInfoY += 25;

        // Performance
        const performance = Number(bestResult.performance || 0);
        const performanceStr = performance.toFixed(2);
        ctx.fillStyle = performance >= 0 ? 'lightgreen' : 'salmon';
        ctx.fillText(`Prestatie: ${performanceStr}%`, 40, timeInfoY);
        ctx.fillStyle = config.textColor;

        // Win ratio
        if (bestResult.win_ratio !== undefined) {
            const winRatio = Number(bestResult.win_ratio || 0).toFixed(2);
            ctx.fillStyle = Number(bestResult.win_ratio || 0) >= 50 ? 'lightgreen' : 'salmon';
            ctx.fillText(`Win ratio: ${winRatio}%`, canvas.width / 2 - 50, timeInfoY);
            ctx.fillStyle = config.textColor;
        }

        // Number of trades
        if (bestResult.number_of_trades !== undefined) {
            ctx.fillText(`Aantal trades: ${bestResult.number_of_trades}`, canvas.width - 200, timeInfoY);
        }

        timeInfoY += 35;

        // Draw performance history chart if we have data
        if (performanceHistory.length > 1) {
            drawPerformanceChart(20, timeInfoY);
            timeInfoY += config.chartHeight + 40;
        }

        // Show top 3 results in a table if we have enough results
        if (botStatus.results.length >= 3) {
            drawResultsTable(20, timeInfoY);
        }
    }
}

// Draw performance history chart
function drawPerformanceChart(x, y) {
    const chartWidth = canvas.width - 40;
    const chartHeight = config.chartHeight;

    // Draw chart background
    ctx.fillStyle = '#222';
    ctx.fillRect(x, y, chartWidth, chartHeight);

    // Draw chart title
    ctx.fillStyle = config.textColor;
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Prestatie Geschiedenis', x, y - 10);

    // Draw axes
    ctx.strokeStyle = '#555';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x, y + chartHeight);
    ctx.lineTo(x + chartWidth, y + chartHeight);
    ctx.stroke();

    // Draw horizontal grid lines and labels
    ctx.textAlign = 'right';
    ctx.font = '12px Arial';

    // Find min and max values for scaling
    let minValue = Math.min(0, ...performanceHistory.map(p => p.value));
    let maxValue = Math.max(0, ...performanceHistory.map(p => p.value));

    // Add some padding
    const valuePadding = Math.max(1, (maxValue - minValue) * 0.1);
    minValue -= valuePadding;
    maxValue += valuePadding;

    // Draw grid lines
    const gridLines = 5;
    for (let i = 0; i <= gridLines; i++) {
        const lineY = y + chartHeight - (i / gridLines) * chartHeight;
        const value = minValue + (i / gridLines) * (maxValue - minValue);

        // Grid line
        ctx.strokeStyle = '#333';
        ctx.beginPath();
        ctx.moveTo(x, lineY);
        ctx.lineTo(x + chartWidth, lineY);
        ctx.stroke();

        // Label
        ctx.fillStyle = '#888';
        ctx.fillText(value.toFixed(1) + '%', x - 5, lineY + 4);
    }

    // Draw performance line
    if (performanceHistory.length > 1) {
        ctx.strokeStyle = config.accentColor;
        ctx.lineWidth = 2;
        ctx.beginPath();

        // Calculate x and y scales
        const timeRange = performanceHistory[performanceHistory.length - 1].time - performanceHistory[0].time;
        const xScale = chartWidth / timeRange;
        const yScale = chartHeight / (maxValue - minValue);

        // Move to first point
        const firstPoint = performanceHistory[0];
        const firstX = x + (firstPoint.time - performanceHistory[0].time) * xScale;
        const firstY = y + chartHeight - (firstPoint.value - minValue) * yScale;
        ctx.moveTo(firstX, firstY);

        // Draw line through all points
        for (let i = 1; i < performanceHistory.length; i++) {
            const point = performanceHistory[i];
            const pointX = x + (point.time - performanceHistory[0].time) * xScale;
            const pointY = y + chartHeight - (point.value - minValue) * yScale;
            ctx.lineTo(pointX, pointY);
        }

        ctx.stroke();

        // Draw points
        ctx.fillStyle = config.accentColor;
        performanceHistory.forEach(point => {
            const pointX = x + (point.time - performanceHistory[0].time) * xScale;
            const pointY = y + chartHeight - (point.value - minValue) * yScale;
            ctx.beginPath();
            ctx.arc(pointX, pointY, 3, 0, Math.PI * 2);
            ctx.fill();
        });

        // Draw current value
        const lastPoint = performanceHistory[performanceHistory.length - 1];
        const lastX = x + (lastPoint.time - performanceHistory[0].time) * xScale;
        const lastY = y + chartHeight - (lastPoint.value - minValue) * yScale;

        ctx.fillStyle = config.textColor;
        ctx.textAlign = 'left';
        ctx.font = 'bold 12px Arial';
        ctx.fillText(`${lastPoint.value.toFixed(2)}%`, lastX + 5, lastY - 5);
    }
}

// Draw results table
function drawResultsTable(x, y) {
    const tableWidth = canvas.width - 40;
    const rowHeight = 30;
    const colWidths = [tableWidth * 0.4, tableWidth * 0.2, tableWidth * 0.2, tableWidth * 0.2];

    // Draw table title
    ctx.fillStyle = config.textColor;
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Top Resultaten', x, y - 10);

    // Draw table header
    ctx.fillStyle = '#333';
    ctx.fillRect(x, y, tableWidth, rowHeight);

    ctx.fillStyle = config.textColor;
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'left';

    let colX = x + 10;
    ctx.fillText('Strategie', colX, y + 20);
    colX += colWidths[0];
    ctx.fillText('Prestatie', colX, y + 20);
    colX += colWidths[1];
    ctx.fillText('Win Ratio', colX, y + 20);
    colX += colWidths[2];
    ctx.fillText('Trades', colX, y + 20);

    // Draw top 3 results
    const topResults = botStatus.results.slice(0, 3);

    topResults.forEach((result, index) => {
        const rowY = y + (index + 1) * rowHeight;

        // Row background (alternating)
        ctx.fillStyle = index % 2 === 0 ? '#222' : '#2a2a2a';
        ctx.fillRect(x, rowY, tableWidth, rowHeight);

        // Row data
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';

        // Strategy ID
        colX = x + 10;
        ctx.fillStyle = config.textColor;
        ctx.fillText(result.strategy_id, colX, rowY + 20);

        // Performance
        colX += colWidths[0];
        const performance = Number(result.performance || 0);
        ctx.fillStyle = performance >= 0 ? 'lightgreen' : 'salmon';
        ctx.fillText(`${performance.toFixed(2)}%`, colX, rowY + 20);

        // Win ratio
        colX += colWidths[1];
        const winRatio = Number(result.win_ratio || 0);
        ctx.fillStyle = winRatio >= 50 ? 'lightgreen' : 'salmon';
        ctx.fillText(`${winRatio.toFixed(2)}%`, colX, rowY + 20);

        // Trades
        colX += colWidths[2];
        ctx.fillStyle = config.textColor;
        ctx.fillText(`${result.number_of_trades || 0}`, colX, rowY + 20);
    });
}

// Get color based on status
function getStatusColor() {
    if (!botStatus.running) {
        return '#555';
    }

    if (botStatus.status.includes('Fout')) {
        return config.errorColor;
    }

    return config.accentColor;
}

// Draw optimization process visualization
function drawOptimizationProcess() {
    if (!botStatus.running) {
        return;
    }

    const startY = 160;
    const boxHeight = 30;
    const boxWidth = canvas.width - 40;
    const boxX = 20;

    // Determine current step based on progress
    currentOptimizationStep = Math.min(
        Math.floor(botStatus.progress / (100 / optimizationSteps.length)),
        optimizationSteps.length - 1
    );

    // Draw steps
    optimizationSteps.forEach((step, index) => {
        const boxY = startY + (index * (boxHeight + 10));

        // Box background
        ctx.fillStyle = index === currentOptimizationStep ? config.accentColor : '#333';
        ctx.fillRect(boxX, boxY, boxWidth, boxHeight);

        // Step name
        ctx.fillStyle = config.textColor;
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(step.name, boxX + 10, boxY + 20);

        // Step description
        ctx.font = '12px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(step.description, boxX + boxWidth - 10, boxY + 20);

        // If this is the current step, add some animation
        if (index === currentOptimizationStep) {
            // Pulsing effect
            const time = Date.now() / 1000;
            const pulseSize = Math.sin(time * 4) * 3 + 3;

            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.strokeRect(
                boxX - pulseSize,
                boxY - pulseSize,
                boxWidth + pulseSize * 2,
                boxHeight + pulseSize * 2
            );

            // Activity indicators
            const indicatorCount = 5;
            const indicatorWidth = 10;
            const indicatorSpacing = 15;
            const indicatorStartX = boxX + boxWidth - (indicatorCount * indicatorSpacing) - 10;

            for (let i = 0; i < indicatorCount; i++) {
                const isActive = Math.sin(time * 5 + i) > 0;
                ctx.fillStyle = isActive ? '#fff' : '#555';
                ctx.fillRect(
                    indicatorStartX + (i * indicatorSpacing),
                    boxY + boxHeight - 10,
                    indicatorWidth,
                    5
                );
            }
        }
    });
}

// Update status from server
function updateStatus() {
    const now = Date.now();
    if (now - lastStatusUpdate > config.statusUpdateInterval) {
        lastStatusUpdate = now;

        fetch('/strategy_bot/status')
            .then(response => response.json())
            .then(data => {
                // Store previous status for comparison
                const wasRunning = botStatus.running;
                const previousPerformance = botStatus.current_performance;
                const previousWinRatio = botStatus.current_win_ratio;

                // Update status with new data
                Object.assign(botStatus, data);

                // Force numeric values to be numbers
                botStatus.current_performance = Number(botStatus.current_performance || 0);
                botStatus.current_win_ratio = Number(botStatus.current_win_ratio || 0);
                botStatus.current_trades = Number(botStatus.current_trades || 0);
                botStatus.progress = Number(botStatus.progress || 0);
                botStatus.iteration = Number(botStatus.iteration || 0);
                botStatus.total_iterations = Number(botStatus.total_iterations || 0);

                // Track start time if bot just started running
                if (botStatus.running && !wasRunning) {
                    botStatus.start_time = Date.now();
                    performanceHistory = [];
                    winRatioHistory = [];
                    botStatus.parameter_history = [];
                }

                // Calculate elapsed time and estimated time remaining
                if (botStatus.running && botStatus.start_time) {
                    botStatus.elapsed_time = Math.floor((Date.now() - botStatus.start_time) / 1000);

                    if (botStatus.progress > 0) {
                        const timePerPercent = botStatus.elapsed_time / botStatus.progress;
                        botStatus.estimated_time_remaining = Math.floor(timePerPercent * (100 - botStatus.progress));
                    }
                }

                // Track performance history if value changed
                if (botStatus.current_performance !== previousPerformance) {
                    performanceHistory.push({
                        time: Date.now(),
                        value: botStatus.current_performance
                    });

                    // Keep history at a reasonable size
                    if (performanceHistory.length > 100) {
                        performanceHistory.shift();
                    }
                }

                // Track win ratio history if value changed
                if (botStatus.current_win_ratio !== previousWinRatio) {
                    winRatioHistory.push({
                        time: Date.now(),
                        value: botStatus.current_win_ratio
                    });

                    // Keep history at a reasonable size
                    if (winRatioHistory.length > 100) {
                        winRatioHistory.shift();
                    }
                }

                // Force performance values in results to be numbers
                if (botStatus.results && botStatus.results.length > 0) {
                    botStatus.results.forEach(result => {
                        if (result.performance !== undefined) {
                            result.performance = Number(result.performance);
                        }
                        if (result.win_ratio !== undefined) {
                            result.win_ratio = Number(result.win_ratio);
                        }
                        if (result.number_of_trades !== undefined) {
                            result.number_of_trades = Number(result.number_of_trades);
                        }
                    });

                    // Sort results by performance
                    botStatus.results.sort((a, b) => b.performance - a.performance);
                }

                // If we're not running but have a high progress, check results
                if (!botStatus.running && botStatus.progress > 0) {
                    fetch('/strategy_bot/results')
                        .then(response => response.json())
                        .then(resultsData => {
                            if (resultsData.results) {
                                // Force performance values to be numbers
                                resultsData.results.forEach(result => {
                                    if (result.performance !== undefined) {
                                        result.performance = Number(result.performance);
                                    }
                                    if (result.win_ratio !== undefined) {
                                        result.win_ratio = Number(result.win_ratio);
                                    }
                                    if (result.number_of_trades !== undefined) {
                                        result.number_of_trades = Number(result.number_of_trades);
                                    }
                                });

                                // Sort results by performance
                                resultsData.results.sort((a, b) => b.performance - a.performance);
                                botStatus.results = resultsData.results;
                            }
                        })
                        .catch(error => console.error('Error fetching results:', error));
                }
            })
            .catch(error => console.error('Error fetching status:', error));
    }

    // Schedule next update
    setTimeout(updateStatus, config.statusUpdateInterval);
}

// Export functions
window.strategyBotVisualization = {
    init: initVisualization
};
