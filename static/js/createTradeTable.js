/**
 * Creates an HTML table to display trade details
 * @param {Array} trades - Array of trade objects
 * @returns {string} HTML string for the trade table
 */
function createTradeTable(trades) {
    console.log('Creating trade table with trades:', trades);

    if (!trades || trades.length === 0) {
        return '<div class="trade-summary"><h3>No trades available</h3></div>';
    }

    // Calculate summary statistics
    const totalTrades = trades.length;
    let winningTrades = 0;
    let totalPnlAmount = 0;
    let totalPnlPercent = 0;
    let initialCapital = 10000;
    let finalCapital = 10000;

    // Extract strategy name if available
    const strategyName = trades[0].strategy_name || 'Unknown Strategy';

    trades.forEach(trade => {
        // Calculate profit/loss
        const pnlAmount = trade.profit_usd || trade.pnl_amount || 0;
        const pnlPercent = trade.pnl_percent || trade.pnl_percentage || 0;

        if (pnlAmount > 0) {
            winningTrades++;
        }

        totalPnlAmount += pnlAmount;
        totalPnlPercent += pnlPercent;
    });

    // Calculate final capital and growth
    finalCapital = initialCapital * (1 + totalPnlPercent / 100);
    const growthPercent = ((finalCapital / initialCapital) - 1) * 100;

    const winRatio = (winningTrades / totalTrades) * 100;

    console.log('Creating trade table with summary:', {
        totalTrades,
        winningTrades,
        winRatio,
        totalPnlAmount,
        totalPnlPercent,
        initialCapital,
        finalCapital,
        growthPercent
    });

    // Create summary HTML
    let summaryHtml = `
    <div class="trade-summary">
        <h3>Trade Summary</h3>
        <table class="summary-table">
            <tr>
                <td>Strategy:</td>
                <td>${strategyName}</td>
            </tr>
            <tr>
                <td>Total Trades:</td>
                <td>${totalTrades}</td>
            </tr>
            <tr>
                <td>Win Ratio:</td>
                <td>${winRatio.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total P/L:</td>
                <td class="${totalPnlPercent >= 0 ? 'profit' : 'loss'}">${totalPnlPercent.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Initial Capital:</td>
                <td>$${initialCapital.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Final Capital:</td>
                <td>$${finalCapital.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Growth:</td>
                <td class="${growthPercent >= 0 ? 'profit' : 'loss'}">${growthPercent.toFixed(2)}%</td>
            </tr>
        </table>
    </div>
    `;

    // Create detailed trade table
    let tableHtml = `
    <div class="trade-table-container">
        <h3>Trade Details</h3>
        <table class="trade-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Type</th>
                    <th>Inkoopdatum</th>
                    <th>Inkooptijd</th>
                    <th>Inkoopprijs</th>
                    <th>Verkoopdatum</th>
                    <th>Verkooptijd</th>
                    <th>Verkoopprijs</th>
                    <th>Resultaat (€)</th>
                    <th>Resultaat (%)</th>
                </tr>
            </thead>
            <tbody>
    `;

    trades.forEach((trade, index) => {
        try {
            console.log('Processing trade:', trade);
            // Format dates
            let entryDate = 'N/A';
            let entryTime = 'N/A';
            let exitDate = 'N/A';
            let exitTime = 'N/A';
            let entryDateTime = null;
            let exitDateTime = null;

            try {
                if (trade.entry_time) {
                    entryDateTime = new Date(trade.entry_time);
                    if (!isNaN(entryDateTime.getTime())) {
                        entryDate = entryDateTime.toLocaleDateString();
                        entryTime = entryDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    }
                } else if (trade.entry_timestamp) {
                    entryDateTime = new Date(trade.entry_timestamp);
                    if (!isNaN(entryDateTime.getTime())) {
                        entryDate = entryDateTime.toLocaleDateString();
                        entryTime = entryDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    }
                }
            } catch (e) {
                console.error('Error formatting entry date:', e);
            }

            try {
                if (trade.exit_time) {
                    exitDateTime = new Date(trade.exit_time);
                    if (!isNaN(exitDateTime.getTime())) {
                        exitDate = exitDateTime.toLocaleDateString();
                        exitTime = exitDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    }
                } else if (trade.exit_timestamp) {
                    exitDateTime = new Date(trade.exit_timestamp);
                    if (!isNaN(exitDateTime.getTime())) {
                        exitDate = exitDateTime.toLocaleDateString();
                        exitTime = exitDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    }
                }
            } catch (e) {
                console.error('Error formatting exit date:', e);
            }

            // Calculate price difference
            let priceDiffAmount = 'N/A';
            let priceDiffPercent = 'N/A';
            let priceDiffClass = '';

            if (trade.entry_price !== undefined && trade.exit_price !== undefined) {
                const diff = trade.exit_price - trade.entry_price;
                const diffPercent = (diff / trade.entry_price) * 100;
                const sign = diff > 0 ? '+' : '-';

                priceDiffAmount = `${sign}$${Math.abs(diff).toFixed(2)}`;
                priceDiffPercent = `${sign}${diffPercent.toFixed(2)}%`;
                priceDiffClass = diff > 0 ? 'profit' : 'loss';

                // Store the calculated percentage in the trade object for row coloring
                trade.pnl_percent = diffPercent;
            }

            // Get profit/loss for row coloring
            const pnlAmount = trade.profit_usd || trade.pnl_amount || 0;
            // Use the calculated pnl_percent if available, otherwise fall back to pnlAmount
            const rowClass = (trade.pnl_percent !== undefined) ?
                (trade.pnl_percent >= 0 ? 'profit-row' : 'loss-row') :
                (pnlAmount >= 0 ? 'profit-row' : 'loss-row');

            tableHtml += `
                <tr class="${rowClass}">
                    <td>${index + 1}</td>
                    <td>${trade.position_type ? trade.position_type.toUpperCase() : 'LONG'}</td>
                    <td>${entryDate}</td>
                    <td>${entryTime}</td>
                    <td>$${trade.entry_price ? trade.entry_price.toFixed(4) : 'N/A'}</td>
                    <td>${exitDate}</td>
                    <td>${exitTime}</td>
                    <td>$${trade.exit_price ? trade.exit_price.toFixed(4) : 'N/A'}</td>
                    <td class="${priceDiffClass}">${priceDiffAmount}</td>
                    <td class="${priceDiffClass}">${priceDiffPercent}</td>
                </tr>
            `;
        } catch (error) {
            console.error('Error processing trade:', error, trade);
        }
    });

    tableHtml += `
            </tbody>
        </table>
    </div>
    `;

    return summaryHtml + tableHtml;
}
