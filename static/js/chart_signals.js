/**
 * Add buy/sell signals to the price chart
 * @param {Array} signals - Array of signal objects with timestamp, price, and type properties
 */
function updateChartWithSignals(signals) {
    console.log('updateChartWithSignals called with', signals?.length || 0, 'signals');

    // Check if chart exists
    if (!window.priceChart) {
        console.error('Price chart not available');
        return;
    }

    // Remove existing signal datasets
    window.priceChart.data.datasets = window.priceChart.data.datasets.filter(dataset =>
        dataset.label !== 'Buy Signals' && dataset.label !== 'Sell Signals'
    );

    // If no signals, just update the chart and return
    if (!signals || signals.length === 0) {
        console.log('No signals to display');
        window.priceChart.update();
        return;
    }

    // Process signals
    const buySignals = [];
    const sellSignals = [];

    signals.forEach(signal => {
        if (!signal.timestamp || !signal.price || !signal.type) {
            console.warn('Invalid signal:', signal);
            return;
        }

        // Convert timestamp to date
        try {
            const date = new Date(signal.timestamp);

            // Check if date is valid
            if (isNaN(date.getTime())) {
                console.warn('Invalid timestamp:', signal.timestamp);
                return;
            }

            // Format date for chart - use timestamp directly
            const formattedDate = signal.timestamp; // Use timestamp directly for better compatibility

            // Create point object
            const point = {
                x: formattedDate,
                y: signal.price,
                timestamp: signal.timestamp, // Keep original timestamp for debugging
                type: signal.type // Keep type for debugging
            };

            // Add to appropriate array
            if (signal.type === 'buy') {
                buySignals.push(point);
            } else if (signal.type === 'sell') {
                sellSignals.push(point);
            }
        } catch (error) {
            console.error('Error processing signal timestamp:', error, signal);
        }
    });

    console.log('Processed signals:', {
        buy: buySignals.length,
        sell: sellSignals.length
    });

    // Log first few signals for debugging
    if (buySignals.length > 0) {
        console.log('First buy signal:', buySignals[0]);
    }
    if (sellSignals.length > 0) {
        console.log('First sell signal:', sellSignals[0]);
    }

    // Get colors based on current theme
    const colors = window.getChartColors ? window.getChartColors() : {
        buy: {
            backgroundColor: 'rgba(0, 255, 0, 1)',  // Bright green
            borderColor: 'rgba(0, 128, 0, 1)'      // Darker green border
        },
        sell: {
            backgroundColor: 'rgba(255, 0, 0, 1)',  // Bright red
            borderColor: 'rgba(139, 0, 0, 1)'      // Darker red border
        }
    };

    // Add buy signals to chart
    if (buySignals.length > 0) {
        // Find the buy signals dataset
        const buySignalsDataset = window.priceChart.data.datasets.find(dataset => dataset.label === 'Buy Signals');

        if (buySignalsDataset) {
            // Update existing dataset
            buySignalsDataset.data = buySignals;
        } else {
            // Create new dataset if not found
            window.priceChart.data.datasets.push({
                type: 'scatter',
                label: 'Buy Signals',
                data: buySignals,
                backgroundColor: colors.buy.backgroundColor,
                borderColor: colors.buy.borderColor,
                pointHoverBackgroundColor: colors.buy.backgroundColor,
                pointHoverBorderColor: colors.buy.borderColor,
                pointStyle: 'triangle',
                pointRadius: 18,
                pointRotation: 0,
                borderWidth: 3,
                hoverRadius: 22,
                hoverBorderWidth: 4,
                z: 10, // Make sure signals are on top
                yAxisID: 'y'
            });
        }
    }

    // Add sell signals to chart
    if (sellSignals.length > 0) {
        // Find the sell signals dataset
        const sellSignalsDataset = window.priceChart.data.datasets.find(dataset => dataset.label === 'Sell Signals');

        if (sellSignalsDataset) {
            // Update existing dataset
            sellSignalsDataset.data = sellSignals;
        } else {
            // Create new dataset if not found
            window.priceChart.data.datasets.push({
                type: 'scatter',
                label: 'Sell Signals',
                data: sellSignals,
                backgroundColor: colors.sell.backgroundColor,
                borderColor: colors.sell.borderColor,
                pointHoverBackgroundColor: colors.sell.backgroundColor,
                pointHoverBorderColor: colors.sell.borderColor,
                pointStyle: 'triangle',
                pointRadius: 18,
                pointRotation: 180,
                borderWidth: 3,
                hoverRadius: 22,
                hoverBorderWidth: 4,
                z: 10, // Make sure signals are on top
                yAxisID: 'y'
            });
        }
    }

    // Update chart
    window.priceChart.update();
    console.log('Chart updated with signals');
}
