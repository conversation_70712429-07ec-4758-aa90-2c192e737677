// Strategy-bot JavaScript functions

// Function to update the Strategy-bot results table
function updateStrategyBotResults(results) {
    const resultsBody = document.getElementById('strategy-bot-results-body');
    if (!resultsBody) return;

    // Clear existing results
    resultsBody.innerHTML = '';

    // Add new results
    results.forEach(result => {
        const row = document.createElement('tr');

        // Format parameters as a string
        const paramsStr = Object.entries(result.parameters || {})
            .map(([key, value]) => {
                // Special handling for multi-strategy parameters
                if (key === 'strategies' || key === 'weights_buy' || key === 'weights_sell' || key === 'strategy_params') {
                    return `${key}: [complex]`;
                }
                return `${key}: ${value}`;
            })
            .join(', ');

        // Format date
        const date = new Date(result.timestamp);
        const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

        // Format performance values with proper handling for undefined values
        const performanceValue = typeof result.performance === 'number' ? result.performance : 0;
        const performance = performanceValue.toFixed(2);
        console.log('Result performance:', result.strategy_id, typeof performanceValue, performanceValue, 'formatted as:', performance);

        const winRatio = result.win_ratio !== undefined ? result.win_ratio.toFixed(2) : '0.00';
        const numTrades = result.number_of_trades !== undefined ? result.number_of_trades : '0';

        // Create cells for better control
        const versionCell = document.createElement('td');
        versionCell.textContent = result.version;

        const dateCell = document.createElement('td');
        dateCell.textContent = formattedDate;

        const strategyCell = document.createElement('td');

        // Check if it's a multi-strategy and format it nicely
        if (result.strategy_id && result.strategy_id.startsWith('multi_')) {
            strategyCell.innerHTML = `<strong>${result.strategy_id}</strong>`;
            strategyCell.title = 'Gecombineerde strategie met prioriteiten';
        } else {
            strategyCell.textContent = result.strategy_id;
        }

        const paramsCell = document.createElement('td');
        paramsCell.textContent = paramsStr;

        const performanceCell = document.createElement('td');
        performanceCell.textContent = `${performance}%`;
        performanceCell.style.backgroundColor = performanceValue >= 0 ? 'lightgreen' : 'salmon';

        const winRatioCell = document.createElement('td');
        winRatioCell.textContent = `${winRatio}%`;
        winRatioCell.style.color = parseFloat(winRatio) >= 50 ? 'lightgreen' : 'salmon';

        const tradesCell = document.createElement('td');
        tradesCell.textContent = numTrades;

        const actionsCell = document.createElement('td');

        // Apply button
        const applyButton = document.createElement('button');
        applyButton.className = 'button small';
        applyButton.textContent = 'Toepassen';
        applyButton.onclick = function() {
            applyStrategy(result.strategy_id, result.parameters);
        };
        actionsCell.appendChild(applyButton);

        // Save button
        const saveButton = document.createElement('button');
        saveButton.className = 'button small';
        saveButton.textContent = 'Opslaan';
        saveButton.style.marginLeft = '5px';
        saveButton.onclick = function() {
            showSaveStrategyModal(result.strategy_id, result.parameters);
        };
        actionsCell.appendChild(saveButton);

        // Details button for multi-strategies
        if (result.strategy_id && result.strategy_id.startsWith('multi_')) {
            const detailsButton = document.createElement('button');
            detailsButton.className = 'button small';
            detailsButton.textContent = 'Details';
            detailsButton.style.marginLeft = '5px';
            detailsButton.onclick = function() {
                showStrategyDetails(result);
            };
            actionsCell.appendChild(detailsButton);
        }

        // Append all cells to the row
        row.appendChild(versionCell);
        row.appendChild(dateCell);
        row.appendChild(strategyCell);
        row.appendChild(paramsCell);
        row.appendChild(performanceCell);
        row.appendChild(winRatioCell);
        row.appendChild(tradesCell);
        row.appendChild(actionsCell);

        resultsBody.appendChild(row);
    });
}

// Function to show the save strategy modal
function showSaveStrategyModal(strategyId, parameters) {
    // Set the strategy ID and parameters in hidden fields
    document.getElementById('strategyIdToSave').value = strategyId;
    document.getElementById('strategyParamsToSave').value = JSON.stringify(parameters);

    // Show the modal
    const modal = document.getElementById('saveStrategyModal');
    modal.style.display = 'block';

    // Focus on the name input
    document.getElementById('newStrategyName').focus();
}

// Function to apply a strategy from the results
function applyStrategy(strategyId, parameters) {
    console.log(`Applying strategy ${strategyId} with parameters:`, parameters);
    // Redirect to the Backtesting tab with the selected strategy
    window.location.href = `/?tab=Backtesting&strategy=${encodeURIComponent(strategyId)}`;
}

// Function to show strategy details in a modal
function showStrategyDetails(result) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('strategy-details-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'strategy-details-modal';
        modal.className = 'modal';
        modal.style.display = 'none';
        modal.style.position = 'fixed';
        modal.style.zIndex = '1000';
        modal.style.left = '0';
        modal.style.top = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.overflow = 'auto';
        modal.style.backgroundColor = 'rgba(0,0,0,0.4)';

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        modalContent.style.backgroundColor = '#fefefe';
        modalContent.style.margin = '15% auto';
        modalContent.style.padding = '20px';
        modalContent.style.border = '1px solid #888';
        modalContent.style.width = '80%';
        modalContent.style.maxWidth = '600px';

        const closeBtn = document.createElement('span');
        closeBtn.className = 'close';
        closeBtn.innerHTML = '&times;';
        closeBtn.style.color = '#aaa';
        closeBtn.style.float = 'right';
        closeBtn.style.fontSize = '28px';
        closeBtn.style.fontWeight = 'bold';
        closeBtn.style.cursor = 'pointer';
        closeBtn.onclick = function() {
            modal.style.display = 'none';
        };

        const modalTitle = document.createElement('h2');
        modalTitle.id = 'strategy-details-title';

        const modalBody = document.createElement('div');
        modalBody.id = 'strategy-details-content';

        modalContent.appendChild(closeBtn);
        modalContent.appendChild(modalTitle);
        modalContent.appendChild(modalBody);
        modal.appendChild(modalContent);

        document.body.appendChild(modal);
    }

    // Update modal content
    const titleElement = document.getElementById('strategy-details-title');
    const contentDiv = document.getElementById('strategy-details-content');

    titleElement.textContent = `Strategie Details: ${result.strategy_id}`;

    // Format the details
    let detailsHtml = `
        <p><strong>Performance:</strong> ${parseFloat(result.performance).toFixed(2)}%</p>
        <p><strong>Win Ratio:</strong> ${result.win_ratio ? result.win_ratio.toFixed(2) + '%' : 'N/A'}</p>
        <p><strong>Aantal Trades:</strong> ${result.number_of_trades || result.num_trades || 'N/A'}</p>
    `;

    // Add strategy parameters if available
    if (result.parameters) {
        detailsHtml += '<h3>Parameters:</h3>';

        // Check if it's a multi-strategy
        if (result.parameters.strategies) {
            detailsHtml += '<h4>Gecombineerde Strategieën:</h4>';
            detailsHtml += '<ul>';
            result.parameters.strategies.forEach(strategy => {
                detailsHtml += `<li>${strategy}</li>`;
            });
            detailsHtml += '</ul>';

            // Show buy weights
            if (result.parameters.weights_buy) {
                detailsHtml += '<h4>Koop Gewichten:</h4>';
                detailsHtml += '<ul>';
                Object.entries(result.parameters.weights_buy).forEach(([strategy, weight]) => {
                    detailsHtml += `<li>${strategy}: ${weight}</li>`;
                });
                detailsHtml += '</ul>';
            }

            // Show sell weights
            if (result.parameters.weights_sell) {
                detailsHtml += '<h4>Verkoop Gewichten:</h4>';
                detailsHtml += '<ul>';
                Object.entries(result.parameters.weights_sell).forEach(([strategy, weight]) => {
                    detailsHtml += `<li>${strategy}: ${weight}</li>`;
                });
                detailsHtml += '</ul>';
            }

            // Show thresholds
            detailsHtml += `<p><strong>Koop Drempel:</strong> ${result.parameters.threshold_buy}</p>`;
            detailsHtml += `<p><strong>Verkoop Drempel:</strong> ${result.parameters.threshold_sell}</p>`;
        } else {
            // Regular strategy parameters
            detailsHtml += '<ul>';
            Object.entries(result.parameters).forEach(([param, value]) => {
                detailsHtml += `<li>${param}: ${value}</li>`;
            });
            detailsHtml += '</ul>';
        }
    }

    contentDiv.innerHTML = detailsHtml;

    // Show the modal
    modal.style.display = 'block';

    // Close when clicking outside the modal
    window.onclick = function(event) {
        if (event.target == modal) {
            modal.style.display = 'none';
        }
    };
}

// Function to start the Strategy-bot
async function startStrategyBot() {
    const strategySelect = document.getElementById('strategy-bot-strategy');
    const iterationsInput = document.getElementById('strategy-bot-iterations');
    const populationInput = document.getElementById('strategy-bot-population');
    const generationsInput = document.getElementById('strategy-bot-generations');
    const pairSelect = document.getElementById('strategy-bot-pair');

    if (!strategySelect || !iterationsInput || !populationInput || !generationsInput || !pairSelect) {
        console.error('Strategy-bot form elements not found');
        return;
    }

    const strategy = strategySelect.value;
    const iterations = parseInt(iterationsInput.value);
    const population = parseInt(populationInput.value);
    const generations = parseInt(generationsInput.value);
    const pair = pairSelect.value;

    if (!strategy) {
        alert('Please select a strategy');
        return;
    }

    try {
        const response = await fetch('/strategy_bot/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                strategy,
                iterations,
                population,
                generations,
                pair
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            document.getElementById('start-strategy-bot-button').disabled = true;
            document.getElementById('stop-strategy-bot-button').disabled = false;

            // Initialize the visualization if it exists
            if (window.strategyBotVisualization && typeof window.strategyBotVisualization.init === 'function') {
                window.strategyBotVisualization.init();
            }

            // Start polling for status updates
            startStatusPolling();
        } else {
            alert(`Failed to start Strategy-bot: ${result.message}`);
        }
    } catch (error) {
        console.error('Error starting Strategy-bot:', error);
        alert('Error starting Strategy-bot. Check console for details.');
    }
}
