// Global chart variables
window.priceChart = null;
window.volumeChart = null;

// Function to get chart colors based on theme
window.getChartColors = function() {
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    return {
        price: {
            borderColor: isDarkMode ? 'rgb(75, 192, 192)' : 'rgb(75, 192, 192)',
            backgroundColor: isDarkMode ? 'rgba(75, 192, 192, 0.1)' : 'rgba(75, 192, 192, 0.1)'
        },
        buy: { // Style for Green Up Triangle (Long Entry / Short Exit)
            backgroundColor: isDarkMode ? 'rgba(0, 255, 0, 1)' : 'rgba(0, 255, 0, 1)',
            borderColor: isDarkMode ? 'rgba(0, 128, 0, 1)' : 'rgba(0, 128, 0, 1)'
        },
        sell: { // Style for Red Down Triangle (Short Entry / Long Exit)
            backgroundColor: isDarkMode ? 'rgba(255, 0, 0, 1)' : 'rgba(255, 0, 0, 1)',
            borderColor: isDarkMode ? 'rgba(139, 0, 0, 1)' : 'rgba(139, 0, 0, 1)'
        },
        grid: {
            color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
        },
        text: {
            color: isDarkMode ? '#aaa' : '#666'
        }
    };
}

// Create static charts on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing charts');

    // Check if we're on the backtesting page
    const priceChartCanvas = document.getElementById('price-chart');
    const volumeChartCanvas = document.getElementById('volume-chart');

    if (priceChartCanvas && volumeChartCanvas) {
        console.log('Chart canvases found, creating static charts');

        try {
            // Get colors based on current theme
            const colors = getChartColors();

            // Create static price chart with test data
            window.priceChart = new Chart(priceChartCanvas, {
                type: 'line',
                data: {
                    // labels: [], // Implicitly handled by timeseries scale
                    datasets: [
                        {
                            label: 'Price',
                            data: [],
                            borderColor: colors.price.borderColor,
                            backgroundColor: colors.price.backgroundColor,
                            borderWidth: 2,
                            tension: 0.1,
                            fill: true,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Long Entry / Short Exit', // Green Up Triangle
                            type: 'scatter', // Ensure type is scatter
                            data: [], // buyMarkers go here
                            backgroundColor: colors.buy.backgroundColor,
                            borderColor: colors.buy.borderColor,
                            pointRadius: 9, // Slightly larger
                            pointStyle: 'triangle',
                            rotation: 0, // Pointing up
                            borderWidth: 1,
                            hoverRadius: 12, // Larger hover
                            hoverBorderWidth: 2,
                            showLine: false, // Ensure no line connects markers
                            yAxisID: 'y' // Ensure plotting against price axis
                        },
                        {
                            label: 'Short Entry / Long Exit', // Red Down Triangle
                            type: 'scatter', // Ensure type is scatter
                            data: [], // sellMarkers go here
                            backgroundColor: colors.sell.backgroundColor,
                            borderColor: colors.sell.borderColor,
                            pointRadius: 9, // Slightly larger
                            pointStyle: 'triangle',
                            rotation: 180, // Pointing down
                            borderWidth: 1,
                            hoverRadius: 12, // Larger hover
                            hoverBorderWidth: 2,
                            showLine: false, // Ensure no line connects markers
                            yAxisID: 'y' // Ensure plotting against price axis
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const dataPoint = context.raw;
                                    const datasetLabel = context.dataset.label || '';

                                    let tooltipText = '';

                                    // Display Price
                                    tooltipText += `Price: ${context.parsed.y.toFixed(2)}`; // Format price

                                    // Display Info (e.g., "long entry") if available
                                    if (dataPoint && dataPoint.info) {
                                        tooltipText += ` (${dataPoint.info})`;
                                    } else {
                                        // Fallback to dataset label if info is missing (shouldn't happen with new logic)
                                        tooltipText += ` (${datasetLabel})`;
                                    }

                                    // Display PnL if available (only on exit markers)
                                    if (dataPoint && dataPoint.pnl !== undefined) {
                                        tooltipText += ` | PnL: ${dataPoint.pnl.toFixed(2)}%`;
                                    }

                                    return tooltipText;
                                }
                            }
                        },
                        legend: {
                            labels: {
                                color: colors.text.color
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'timeseries',
                            adapters: { date: { locale: 'en-US' } },
                            time: {
                                unit: 'day',
                                tooltipFormat: 'DD T',
                                displayFormats: { day: 'MMM d' }
                            },
                            ticks: {
                                maxRotation: 45, minRotation: 45, color: colors.text.color, source: 'auto'
                            },
                            grid: { color: colors.grid.color }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: 'Price', color: colors.text.color },
                            ticks: { color: colors.text.color },
                            grid: { color: colors.grid.color }
                        }
                    }
                }
            });

            // Create static volume chart with empty data
            window.volumeChart = new Chart(volumeChartCanvas, {
                type: 'bar',
                data: {
                    // labels: [], // Implicitly handled by timeseries scale
                    datasets: [{
                        label: 'Volume',
                        data: [],
                        backgroundColor: colors.price.backgroundColor,
                        borderColor: colors.price.borderColor,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { labels: { color: colors.text.color } } },
                    scales: {
                        x: {
                            type: 'timeseries',
                            adapters: { date: { locale: 'en-US' } },
                            time: {
                                unit: 'day',
                                displayFormats: { day: 'MMM d' }
                            },
                            ticks: { color: colors.text.color, source: 'auto', maxRotation: 45, minRotation: 45 },
                            grid: { color: colors.grid.color }
                        },
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: 'Volume', color: colors.text.color },
                            ticks: { color: colors.text.color },
                            grid: { color: colors.grid.color }
                        }
                    }
                }
            });

            console.log('Charts created successfully');
        } catch (error) {
            console.error('Error creating charts:', error);
        }
    } else {
        console.log('Chart canvases not found');
    }
});

// Function to update charts with historical data and backtest results
function updateChartsWithBacktestResults(historicalData, backtestResults) {
    console.log('Updating charts with backtest results');

    // --- START NEW DETAILED LOGGING ---
    console.log('Received backtestResults object:', backtestResults);
    if (backtestResults) {
        console.log('Type of backtestResults:', typeof backtestResults);
        console.log('backtestResults.result:', backtestResults.result);
        if (backtestResults.result) {
            console.log('Type of backtestResults.result:', typeof backtestResults.result);
            console.log('backtestResults.result.trades:', backtestResults.result.trades);
            console.log('Is backtestResults.result.trades an array?', Array.isArray(backtestResults.result.trades));
        } else {
            console.log('backtestResults.result is falsy.');
        }
    } else {
        console.log('backtestResults is falsy.');
    }
    // --- END NEW DETAILED LOGGING ---

    if (!historicalData || !Array.isArray(historicalData) || historicalData.length === 0) {
        console.error("No historical data available to display chart");
        return;
    }

    // More robust check: Ensure results object and the trades array within it exist and are valid
    // Check if trades are directly in backtestResults or in backtestResults.result
    let trades = [];
    if (backtestResults && Array.isArray(backtestResults.trades)) {
        trades = backtestResults.trades;
        console.log('Found trades directly in backtestResults object');
    } else if (backtestResults && backtestResults.result && Array.isArray(backtestResults.result.trades)) {
        trades = backtestResults.result.trades;
        console.log('Found trades in backtestResults.result object');
    } else {
        console.error("Backtest results object is missing or does not contain a valid 'trades' array.");
        console.log('Backtest results structure:', backtestResults);
        // Optionally, still try to display historical data if available
        // Ensure historicalData is valid before using it
        if (historicalData && Array.isArray(historicalData) && typeof createPriceChart === 'function') {
             console.log("Attempting to display historical data only.");
             createPriceChart(historicalData); // Assumes createPriceChart exists and handles clearing markers
        } else {
             console.warn("Historical data also missing, invalid, or createPriceChart function not found.");
        }
        return; // Stop processing if trades data is invalid
    }

    // If we reach here, we have a valid trades array (it might be empty)
    console.log(`Processing ${trades.length} trades for chart markers.`);

    // Handle case where trades array is empty (valid result, but no markers)
    if (trades.length === 0) {
        console.log("Backtest results contain an empty 'trades' array. No markers to display.");
        // Display historical data only
        if (historicalData && Array.isArray(historicalData) && typeof createPriceChart === 'function') {
             createPriceChart(historicalData);
        }
        return; // Exit function if no trades
    }

    // Get chart canvases
    const priceChartCanvas = document.getElementById('price-chart');
    const volumeChartCanvas = document.getElementById('volume-chart');

    if (!priceChartCanvas || !volumeChartCanvas || !window.priceChart || !window.volumeChart) {
        console.error('Chart canvases or chart instances not found/initialized!');
        return;
    }

    try {
        // Prepare data for charts
        const priceData = [];
        const volumeData = [];

        historicalData.forEach(item => {
            const timestamp = new Date(item.timestamp).getTime();
            if (!isNaN(timestamp)) {
                priceData.push({ x: timestamp, y: item.close });
                volumeData.push({ x: timestamp, y: item.volume });
            } else {
                console.warn('Invalid historical data timestamp:', item.timestamp);
            }
        });

        // Prepare markers from trades
        const buyMarkers = []; // Green Up Triangle (Long Entry / Short Exit)
        const sellMarkers = []; // Red Down Triangle (Short Entry / Long Exit)
        // const trades = backtestResults.result.trades || []; // trades variable already defined above
        console.log('Raw backtest results object passed to function:', backtestResults); // Log the wrapper object
        console.log('Actual trades array being processed:', trades); // Log the trades array

        trades.forEach((trade, index) => {
            console.log(`--- Processing Trade ${index + 1} ---`);
            console.log('Raw trade data:', trade); // Log raw trade object

            const positionType = (trade.position || trade.position_type || 'long').toLowerCase();
            const entryTimestampStr = trade.entry_timestamp || trade.entry_time;
            const exitTimestampStr = trade.exit_timestamp || trade.exit_time;
            const entryTimestamp = new Date(entryTimestampStr).getTime();
            const exitTimestamp = new Date(exitTimestampStr).getTime();
            const entryPrice = trade.entry_price;
            const exitPrice = trade.exit_price;
            const pnl = trade.pnl_percentage;

            console.log(`  Position Type: ${positionType}`);
            console.log(`  Entry Timestamp Str: ${entryTimestampStr}, Parsed: ${entryTimestamp}, Price: ${entryPrice}`);
            console.log(`  Exit Timestamp Str: ${exitTimestampStr}, Parsed: ${exitTimestamp}, Price: ${exitPrice}`);
            console.log(`  PnL: ${pnl}`);


            // Entry Marker
            if (!isNaN(entryTimestamp) && entryPrice !== undefined && entryPrice !== null) { // Added null check for price
                const entryMarker = {
                    x: entryTimestamp,
                    y: entryPrice,
                    info: `${positionType} entry`
                };
                console.log(`  Creating Entry Marker: x=${entryMarker.x} (type: ${typeof entryMarker.x}), y=${entryMarker.y} (type: ${typeof entryMarker.y})`); // Log marker data
                if (positionType === 'long') {
                    buyMarkers.push(entryMarker);
                    console.log('    Added to buyMarkers (Long Entry)');
                } else {
                    sellMarkers.push(entryMarker);
                    console.log('    Added to sellMarkers (Short Entry)');
                }
            } else {
                console.warn(`  Skipping Entry Marker: Invalid timestamp (${entryTimestampStr} -> ${entryTimestamp}) or price (${entryPrice})`);
            }

            // Exit Marker
            if (!isNaN(exitTimestamp) && exitPrice !== undefined && exitPrice !== null) { // Added null check for price
                const exitMarker = {
                    x: exitTimestamp,
                    y: exitPrice,
                    pnl: pnl,
                    info: `${positionType} exit`
                };
                 console.log(`  Creating Exit Marker: x=${exitMarker.x} (type: ${typeof exitMarker.x}), y=${exitMarker.y} (type: ${typeof exitMarker.y}), pnl=${exitMarker.pnl}`); // Log marker data
                 if (positionType === 'long') {
                    sellMarkers.push(exitMarker);
                    console.log('    Added to sellMarkers (Long Exit)');
                 } else {
                    buyMarkers.push(exitMarker);
                    console.log('    Added to buyMarkers (Short Exit)');
                 }
            } else {
                 console.warn(`  Skipping Exit Marker: Invalid timestamp (${exitTimestampStr} -> ${exitTimestamp}) or price (${exitPrice})`);
            }
            console.log('--- Finished Processing Trade ---');
        });

        console.log('Final buyMarkers:', buyMarkers);
        console.log('Final sellMarkers:', sellMarkers);

        // Update price chart datasets
        window.priceChart.data.datasets[0].data = priceData; // Price
        window.priceChart.data.datasets[1].data = buyMarkers; // Long Entry / Short Exit
        window.priceChart.data.datasets[2].data = sellMarkers; // Short Entry / Long Exit

        // Update volume chart dataset
        window.volumeChart.data.datasets[0].data = volumeData;

        // Update charts - Ensure this happens *after* data is assigned
        window.priceChart.update();
        window.volumeChart.update();

        console.log('Charts updated successfully with backtest results and markers.');

    } catch (error) {
        console.error('Error updating charts with backtest results:', error);
    }
}

// Function to revert chart changes (remove markers)
function revertChartChanges() {
    console.log('Reverting chart changes');
    if (window.historicalData) {
        createPriceChart(window.historicalData); // Re-renders chart with only historical data
        console.log('Chart reverted to show only historical data.');
    } else {
        console.warn('No historical data found to revert chart.');
        // Optionally clear charts completely if no data
        if (window.priceChart) {
            window.priceChart.data.datasets[0].data = []; // Clear price
            window.priceChart.data.datasets[1].data = []; // Clear buy markers
            window.priceChart.data.datasets[2].data = []; // Clear sell markers
            window.priceChart.update();
        }
        if (window.volumeChart) {
            window.volumeChart.data.datasets[0].data = []; // Clear volume
            window.volumeChart.update();
        }
    }
}

// Function to create/update chart with only historical data (clears markers)
function createPriceChart(historicalData) {
    console.log('Updating chart with historical data (clearing markers)');

    if (!historicalData || !Array.isArray(historicalData) || historicalData.length === 0) {
        console.error("No historical data available to display chart");
        return;
    }

    const priceChartCanvas = document.getElementById('price-chart');
    const volumeChartCanvas = document.getElementById('volume-chart');

    if (!priceChartCanvas || !volumeChartCanvas || !window.priceChart || !window.volumeChart) {
        console.error('Chart canvases or chart instances not found/initialized!');
        return;
    }

    try {
        const priceData = [];
        const volumeData = [];

        historicalData.forEach(item => {
            const timestamp = new Date(item.timestamp).getTime();
            if (!isNaN(timestamp)) {
                priceData.push({ x: timestamp, y: item.close });
                volumeData.push({ x: timestamp, y: item.volume });
            } else {
                console.warn('Invalid historical data timestamp:', item.timestamp);
            }
        });

        // Update price chart (only price data, clear markers)
        window.priceChart.data.datasets[0].data = priceData;
        window.priceChart.data.datasets[1].data = []; // Clear buy markers
        window.priceChart.data.datasets[2].data = []; // Clear sell markers

        // Update volume chart
        window.volumeChart.data.datasets[0].data = volumeData;

        // Update charts
        window.priceChart.update();
        window.volumeChart.update();

        console.log('Charts updated successfully with only historical data');

    } catch (error) {
        console.error('Error updating charts with historical data:', error);
    }
}
