// Simple signals display script
console.log('Simple signals script loaded');

// Function to add signals to the chart
function displaySignalsOnChart(signals) {
    console.log('Displaying signals on chart:', signals.length);

    if (!signals || signals.length === 0) {
        console.log('No signals to display');
        return;
    }

    // Clear existing buy/sell signals from chart
    if (window.priceChart && window.priceChart.data && window.priceChart.data.datasets) {
        window.priceChart.data.datasets = window.priceChart.data.datasets.filter(dataset =>
            dataset.label !== 'Buy Signals' && dataset.label !== 'Sell Signals'
        );
    }

    // Prepare data arrays
    const buySignals = [];
    const sellSignals = [];

    // Process signals
    signals.forEach(signal => {
        // Format date
        const date = new Date(parseInt(signal.timestamp));
        const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD

        // Create point
        const point = {
            x: dateStr,
            y: signal.price
        };

        // Add to appropriate array
        if (signal.type === 'buy') {
            buySignals.push(point);
            console.log('Added buy signal:', point);
        } else if (signal.type === 'sell') {
            sellSignals.push(point);
            console.log('Added sell signal:', point);
        }
    });

    console.log('Processed buy signals:', buySignals.length);
    console.log('Processed sell signals:', sellSignals.length);

    // Add buy signals to chart
    if (buySignals.length > 0) {
        window.priceChart.data.datasets.push({
            type: 'scatter',
            label: 'Buy Signals',
            data: buySignals,
            backgroundColor: '#00FF00',  // Bright green
            borderColor: '#FFFFFF',      // White border
            pointBackgroundColor: '#00FF00',  // Ensure points are green
            pointRadius: 20,             // Very large points
            pointStyle: 'circle',
            borderWidth: 3,
            hoverRadius: 20,
            hoverBorderWidth: 4,
            yAxisID: 'y',
            z: 1000                      // Ensure it's on top
        });
    }

    // Add sell signals to chart
    if (sellSignals.length > 0) {
        window.priceChart.data.datasets.push({
            type: 'scatter',
            label: 'Sell Signals',
            data: sellSignals,
            backgroundColor: '#FF0000',  // Bright red
            borderColor: '#FFFFFF',      // White border
            pointBackgroundColor: '#FF0000',  // Ensure points are red
            pointRadius: 20,             // Very large points
            pointStyle: 'circle',
            borderWidth: 3,
            hoverRadius: 20,
            hoverBorderWidth: 4,
            yAxisID: 'y',
            z: 1000                      // Ensure it's on top
        });
    }

    // Update chart
    window.priceChart.update();
    console.log('Chart updated with signals');
}

// Export function
window.displaySignalsOnChart = displaySignalsOnChart;
