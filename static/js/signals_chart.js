// Simple signals chart script
console.log('Signals chart script loaded');

// Function to create a signals chart
function createSignalsChart(signals, historicalData) {
    console.log('Creating signals chart with', signals.length, 'signals');
    
    if (!signals || signals.length === 0 || !historicalData || historicalData.length === 0) {
        console.log('No signals or historical data to display');
        return;
    }
    
    // Create container for the signals chart
    const signalsContainer = document.createElement('div');
    signalsContainer.id = 'signals-chart-container';
    signalsContainer.style.width = '100%';
    signalsContainer.style.height = '300px';
    signalsContainer.style.marginTop = '20px';
    signalsContainer.style.marginBottom = '20px';
    
    // Create title for the chart
    const title = document.createElement('h3');
    title.textContent = 'Buy and Sell Signals';
    title.style.textAlign = 'center';
    title.style.marginBottom = '10px';
    
    // Create canvas for the chart
    const canvas = document.createElement('canvas');
    canvas.id = 'signals-chart';
    signalsContainer.appendChild(canvas);
    
    // Find the trade results container to insert before
    const tradeResultsContainer = document.getElementById('trade-results-container');
    if (tradeResultsContainer) {
        // Insert the title and container before the trade results
        tradeResultsContainer.parentNode.insertBefore(title, tradeResultsContainer);
        tradeResultsContainer.parentNode.insertBefore(signalsContainer, tradeResultsContainer);
        console.log('Added signals chart container to the page');
    } else {
        // Fallback: append to the backtesting chart panel
        const chartPanel = document.querySelector('.backtesting-chart-panel');
        if (chartPanel) {
            chartPanel.appendChild(title);
            chartPanel.appendChild(signalsContainer);
            console.log('Added signals chart container to chart panel');
        } else {
            console.error('Could not find a suitable container for the signals chart');
            return;
        }
    }
    
    // Prepare data for the chart
    const priceData = [];
    const buySignals = [];
    const sellSignals = [];
    
    // Process historical data for price line
    historicalData.forEach(dataPoint => {
        priceData.push({
            x: new Date(dataPoint.timestamp),
            y: dataPoint.close
        });
    });
    
    // Process signals
    signals.forEach(signal => {
        const point = {
            x: new Date(parseInt(signal.timestamp)),
            y: signal.price
        };
        
        if (signal.type === 'buy') {
            buySignals.push(point);
        } else if (signal.type === 'sell') {
            sellSignals.push(point);
        }
    });
    
    console.log('Processed price data points:', priceData.length);
    console.log('Processed buy signals:', buySignals.length);
    console.log('Processed sell signals:', sellSignals.length);
    
    // Get min and max values for the chart
    const allPrices = priceData.map(point => point.y);
    const minPrice = Math.min(...allPrices) * 0.99; // Add some padding
    const maxPrice = Math.max(...allPrices) * 1.01; // Add some padding
    
    const allDates = priceData.map(point => point.x);
    const minDate = new Date(Math.min(...allDates.map(d => d.getTime())));
    const maxDate = new Date(Math.max(...allDates.map(d => d.getTime())));
    
    // Create the chart
    const ctx = document.getElementById('signals-chart').getContext('2d');
    
    // Create the chart
    const signalsChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [
                {
                    label: 'Price',
                    data: priceData,
                    borderColor: 'rgba(150, 150, 150, 0.8)',
                    backgroundColor: 'rgba(150, 150, 150, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    fill: false,
                    tension: 0.1,
                    yAxisID: 'y'
                },
                {
                    label: 'Buy Signals',
                    data: buySignals,
                    backgroundColor: '#00FF00',  // Bright green
                    borderColor: '#FFFFFF',      // White border
                    pointBackgroundColor: '#00FF00',  // Ensure points are green
                    pointRadius: 10,             // Large points
                    pointStyle: 'circle',
                    borderWidth: 2,
                    hoverRadius: 15,
                    hoverBorderWidth: 3,
                    fill: false,
                    showLine: false,
                    yAxisID: 'y'
                },
                {
                    label: 'Sell Signals',
                    data: sellSignals,
                    backgroundColor: '#FF0000',  // Bright red
                    borderColor: '#FFFFFF',      // White border
                    pointBackgroundColor: '#FF0000',  // Ensure points are red
                    pointRadius: 10,             // Large points
                    pointStyle: 'circle',
                    borderWidth: 2,
                    hoverRadius: 15,
                    hoverBorderWidth: 3,
                    fill: false,
                    showLine: false,
                    yAxisID: 'y'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const point = context.raw;
                            if (context.dataset.label === 'Buy Signals') {
                                return `Buy at $${point.y.toLocaleString()}`;
                            } else if (context.dataset.label === 'Sell Signals') {
                                return `Sell at $${point.y.toLocaleString()}`;
                            }
                            return `Price: $${point.y.toLocaleString()}`;
                        }
                    }
                },
                legend: {
                    labels: {
                        color: window.isDarkMode ? '#ffffff' : '#333333'
                    }
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day',
                        displayFormats: {
                            day: 'MMM dd'
                        }
                    },
                    min: minDate,
                    max: maxDate,
                    ticks: {
                        color: window.isDarkMode ? '#ffffff' : '#333333'
                    },
                    grid: {
                        color: window.isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    min: minPrice,
                    max: maxPrice,
                    ticks: {
                        color: window.isDarkMode ? '#ffffff' : '#333333',
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    },
                    grid: {
                        color: window.isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
    
    console.log('Signals chart created successfully');
    
    // Store the chart in the window object for later reference
    window.signalsChart = signalsChart;
}

// Export function
window.createSignalsChart = createSignalsChart;
