// Debug signals script
console.log('Debug signals script loaded');

// Function to create a simple HTML overlay for signals
function createSignalOverlay() {
    console.log('Creating signal overlay');

    // Remove existing container if it exists
    const existingContainer = document.getElementById('debug-signal-container');
    if (existingContainer) {
        existingContainer.remove();
    }

    // Create container for signals
    const container = document.createElement('div');
    container.id = 'debug-signal-container';
    container.style.position = 'absolute';
    container.style.top = '0';
    container.style.left = '0';
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.pointerEvents = 'none';
    container.style.zIndex = '1000';

    // Add container to the page
    const chartCanvas = document.getElementById('price-chart');
    if (chartCanvas) {
        const chartParent = chartCanvas.parentElement;
        chartParent.style.position = 'relative';
        chartParent.appendChild(container);
        console.log('Signal container added to chart parent');
    } else {
        console.error('Price chart canvas not found');
    }
}

// Function to add a signal to the overlay
function addSignalToOverlay(type, x, y, price) {
    console.log(`Adding ${type} signal at (${x}, ${y}) - Price: ${price}`);

    const container = document.getElementById('debug-signal-container');
    if (!container) {
        console.error('Signal container not found');
        return;
    }

    // Create signal element
    const signal = document.createElement('div');
    signal.className = `debug-signal ${type}-signal`;
    signal.style.position = 'absolute';
    signal.style.left = `${x}px`;
    signal.style.top = `${y}px`;
    signal.style.width = '30px';
    signal.style.height = '30px';
    signal.style.borderRadius = '50%';
    signal.style.backgroundColor = type === 'buy' ? '#00ff00' : '#ff0000';
    signal.style.border = '3px solid #ffffff';
    signal.style.transform = 'translate(-50%, -50%)';
    signal.style.pointerEvents = 'auto';
    signal.style.cursor = 'pointer';
    signal.style.zIndex = '1000';

    // Add tooltip
    signal.title = `${type.toUpperCase()} at $${price}`;

    // Add signal to container
    container.appendChild(signal);
    console.log('Signal added to overlay');
}

// Function to display signals from backtest result
function displaySignals(signals) {
    console.log('Displaying signals:', signals.length);

    // Always recreate the overlay to ensure it's properly positioned
    createSignalOverlay();

    // Get chart dimensions
    const chartCanvas = document.getElementById('price-chart');
    if (!chartCanvas) {
        console.error('Price chart canvas not found');
        return;
    }

    const chartRect = chartCanvas.getBoundingClientRect();
    const chartWidth = chartRect.width;
    const chartHeight = chartRect.height;

    console.log('Chart dimensions:', chartWidth, 'x', chartHeight);

    // Get price data from chart
    if (!window.priceChart || !window.priceChart.data || !window.priceChart.data.datasets || !window.priceChart.data.datasets[0]) {
        console.error('Price chart data not available');
        return;
    }

    const priceData = window.priceChart.data.datasets[0].data;

    // Get min/max values for scaling
    const prices = priceData.map(point => point.y);
    const minPrice = Math.min(...prices) * 0.99; // Add some padding
    const maxPrice = Math.max(...prices) * 1.01; // Add some padding
    const priceRange = maxPrice - minPrice;

    // Get date range
    const dates = priceData.map(point => new Date(point.x).getTime());
    const minDate = Math.min(...dates);
    const maxDate = Math.max(...dates);
    const dateRange = maxDate - minDate;

    console.log('Price range:', minPrice, 'to', maxPrice);
    console.log('Date range:', new Date(minDate), 'to', new Date(maxDate));

    // Add each signal to the overlay
    signals.forEach(signal => {
        const signalDate = new Date(parseInt(signal.timestamp)).getTime();
        const signalPrice = signal.price;

        // Calculate position
        const xPercent = (signalDate - minDate) / dateRange;
        const yPercent = 1 - ((signalPrice - minPrice) / priceRange);

        const xPos = xPercent * chartWidth;
        const yPos = yPercent * chartHeight;

        // Add signal to overlay
        addSignalToOverlay(signal.type, xPos, yPos, signalPrice);
        console.log(`Added ${signal.type} signal at (${xPos}, ${yPos}) - Price: ${signalPrice}`);
    });
}

// Add event listener for backtest results
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up debug signals');

    // Create initial overlay
    createSignalOverlay();
});

// Export functions
window.debugSignals = {
    displaySignals: displaySignals
};
