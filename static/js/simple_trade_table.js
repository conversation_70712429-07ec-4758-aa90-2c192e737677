/**
 * Creates a simple HTML table to display trade details
 * @param {Array} trades - Array of trade objects
 * @returns {string} HTML string for the trade table
 */
function createSimpleTradeTable(trades) {
    console.log('Creating simple trade table with trades:', trades);

    if (!trades || trades.length === 0) {
        return '<div class="trade-summary"><h3>No trades available</h3></div>';
    }

    // Calculate summary statistics
    const totalTrades = trades.length;
    let winningTrades = 0;
    let totalPnlAmount = 0;
    let totalPnlPercent = 0;
    let initialCapital = 10000;
    let finalCapital = 10000;

    // Extract strategy name if available
    const strategyName = trades[0].strategy_name || 'Unknown Strategy';

    trades.forEach(trade => {
        // Calculate profit/loss
        const pnlAmount = trade.profit_usd || trade.pnl_amount || 0;
        const pnlPercent = trade.pnl_percent || trade.pnl_percentage || 0;

        if (pnlAmount > 0) {
            winningTrades++;
        }

        totalPnlAmount += pnlAmount;
        totalPnlPercent += pnlPercent;
    });

    // Calculate final capital and growth
    finalCapital = initialCapital * (1 + totalPnlPercent / 100);
    const growthPercent = ((finalCapital / initialCapital) - 1) * 100;

    const winRatio = (winningTrades / totalTrades) * 100;

    // Create HTML output
    let html = `
    <style>
        .trade-summary {
            margin: 20px 0;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        .trade-summary h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }

        .summary-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #444;
        }

        .summary-table td:first-child {
            font-weight: bold;
            width: 40%;
        }

        .recent-trades-container {
            margin: 20px 0;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        .recent-trades-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .recent-trades-table {
            width: 100%;
            border-collapse: collapse;
            font-family: monospace;
            font-size: 14px;
        }

        .recent-trades-table th {
            background-color: #333;
            color: #f0f0f0;
            font-weight: bold;
            padding: 10px;
            text-align: left;
            white-space: nowrap;
        }

        .recent-trades-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #444;
            white-space: nowrap;
        }

        .chart-container {
            margin: 20px 0;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            height: 300px;
        }

        .chart-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .trade-table-container {
            margin: 20px 0;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            overflow-x: auto;
        }

        .trade-table-container h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            color: #f0f0f0;
        }

        .trade-table {
            width: 100%;
            border-collapse: collapse;
        }

        .trade-table th {
            background-color: #333;
            color: #f0f0f0;
            font-weight: bold;
            padding: 10px;
            text-align: left;
        }

        .trade-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #444;
        }

        .profit {
            color: #4CAF50;
        }

        .loss {
            color: #f44336;
        }

        .profit-row {
            background-color: rgba(76, 175, 80, 0.1);
        }

        .loss-row {
            background-color: rgba(244, 67, 54, 0.1);
        }
    </style>

    <div class="trade-summary">
        <h3>Trade Summary</h3>
        <table class="summary-table">
            <tr>
                <td>Strategy:</td>
                <td>${strategyName}</td>
            </tr>
            <tr>
                <td>Total Trades:</td>
                <td>${totalTrades}</td>
            </tr>
            <tr>
                <td>Win Ratio:</td>
                <td>${winRatio.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Total P&L:</td>
                <td class="${totalPnlPercent >= 0 ? 'profit' : 'loss'}">${totalPnlPercent.toFixed(2)}%</td>
            </tr>
            <tr>
                <td>Initial Capital:</td>
                <td>$${initialCapital.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Final Capital:</td>
                <td>$${finalCapital.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Growth:</td>
                <td class="${growthPercent >= 0 ? 'profit' : 'loss'}">${growthPercent.toFixed(2)}%</td>
            </tr>
        </table>
    </div>

    <div class="recent-trades-container">
        <h3>Handelsdetail</h3>
        <table class="recent-trades-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Type</th>
                    <th>Inkoopdatum</th>
                    <th>Inkooptijd</th>
                    <th>Inkoopprijs</th>
                    <th>Verkoopdatum</th>
                    <th>Verkooptijd</th>
                    <th>Verkoopprijs</th>
                    <th>Resultaat (€)</th>
                    <th>Resultaat (%)</th>
                </tr>
            </thead>
            <tbody>
    `;

    // Add rows for each trade
    trades.forEach((trade, index) => {
        // Format dates
        let entryDate = 'N/A';
        let entryTime = 'N/A';
        let exitDate = 'N/A';
        let exitTime = 'N/A';
        let entryDateTime = null;
        let exitDateTime = null;

        try {
            if (trade.entry_time) {
                entryDateTime = new Date(parseInt(trade.entry_time));
                if (!isNaN(entryDateTime.getTime())) {
                    entryDate = entryDateTime.toLocaleDateString();
                    entryTime = entryDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }
            } else if (trade.entry_timestamp) {
                entryDateTime = new Date(parseInt(trade.entry_timestamp));
                if (!isNaN(entryDateTime.getTime())) {
                    entryDate = entryDateTime.toLocaleDateString();
                    entryTime = entryDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }
            }
            console.log('Entry date/time:', entryDate, entryTime, 'from:', trade.entry_time || trade.entry_timestamp);
        } catch (e) {
            console.error('Error formatting entry date:', e, trade.entry_time || trade.entry_timestamp);
        }

        try {
            if (trade.exit_time) {
                exitDateTime = new Date(parseInt(trade.exit_time));
                if (!isNaN(exitDateTime.getTime())) {
                    exitDate = exitDateTime.toLocaleDateString();
                    exitTime = exitDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }
            } else if (trade.exit_timestamp) {
                exitDateTime = new Date(parseInt(trade.exit_timestamp));
                if (!isNaN(exitDateTime.getTime())) {
                    exitDate = exitDateTime.toLocaleDateString();
                    exitTime = exitDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }
            }
            console.log('Exit date/time:', exitDate, exitTime, 'from:', trade.exit_time || trade.exit_timestamp);
        } catch (e) {
            console.error('Error formatting exit date:', e, trade.exit_time || trade.exit_timestamp);
        }

        // Calculate price difference
        let priceDiffAmount = 'N/A';
        let priceDiffPercent = 'N/A';

        if (trade.entry_price !== undefined && trade.exit_price !== undefined) {
            const diff = trade.exit_price - trade.entry_price;
            const diffPercent = (diff / trade.entry_price) * 100;

            priceDiffAmount = diff;
            priceDiffPercent = diffPercent;

            console.log(`Trade ${index+1} diff calculation:`, {
                entry_price: trade.entry_price,
                exit_price: trade.exit_price,
                diff: diff,
                diffPercent: diffPercent
            });
        }

        // Determine row and text classes
        const rowClass = typeof priceDiffPercent === 'number' ? (priceDiffPercent >= 0 ? 'profit-row' : 'loss-row') : '';
        const diffClass = typeof priceDiffPercent === 'number' ? (priceDiffPercent >= 0 ? 'profit' : 'loss') : '';
        const positionType = trade.position_type ? trade.position_type.toUpperCase() : 'LONG';

        html += `
            <tr class="${rowClass}">
                <td>${index + 1}</td>
                <td>${positionType}</td>
                <td>${entryDate}</td>
                <td>${entryTime}</td>
                <td>$${trade.entry_price ? trade.entry_price.toFixed(4) : 'N/A'}</td>
                <td>${exitDate}</td>
                <td>${exitTime}</td>
                <td>$${trade.exit_price ? trade.exit_price.toFixed(4) : 'N/A'}</td>
                <td class="${diffClass}">${priceDiffAmount >= 0 ? '+' : '-'}$${typeof priceDiffAmount === 'number' ? Math.abs(priceDiffAmount).toFixed(2) : priceDiffAmount}</td>
                <td class="${diffClass}">${priceDiffPercent >= 0 ? '+' : '-'}${typeof priceDiffPercent === 'number' ? Math.abs(priceDiffPercent).toFixed(2) : priceDiffPercent}%</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    </div>

    <div class="chart-container">
        <h3>Trade Performance</h3>
        <canvas id="tradeChart"></canvas>
    </div>

    <div class="trade-table-container">
        <h3>All Trades</h3>
        <table class="trade-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Inkoopdatum</th>
                    <th>Inkooptijd</th>
                    <th>Inkoopprijs</th>
                    <th>Verkoopdatum</th>
                    <th>Verkooptijd</th>
                    <th>Verkoopprijs</th>
                    <th>Resultaat (%)</th>
                </tr>
            </thead>
            <tbody>
    `;

    // Add rows for all trades
    trades.forEach((trade, index) => {
        // Format dates
        let entryDate = 'N/A';
        let entryTime = 'N/A';
        let exitDate = 'N/A';
        let exitTime = 'N/A';
        let entryDateTime = null;
        let exitDateTime = null;

        try {
            if (trade.entry_time) {
                entryDateTime = new Date(parseInt(trade.entry_time));
                if (!isNaN(entryDateTime.getTime())) {
                    entryDate = entryDateTime.toLocaleDateString();
                    entryTime = entryDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }
            } else if (trade.entry_timestamp) {
                entryDateTime = new Date(parseInt(trade.entry_timestamp));
                if (!isNaN(entryDateTime.getTime())) {
                    entryDate = entryDateTime.toLocaleDateString();
                    entryTime = entryDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }
            }
            console.log('Entry date/time:', entryDate, entryTime, 'from:', trade.entry_time || trade.entry_timestamp);
        } catch (e) {
            console.error('Error formatting entry date:', e, trade.entry_time || trade.entry_timestamp);
        }

        try {
            if (trade.exit_time) {
                exitDateTime = new Date(parseInt(trade.exit_time));
                if (!isNaN(exitDateTime.getTime())) {
                    exitDate = exitDateTime.toLocaleDateString();
                    exitTime = exitDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }
            } else if (trade.exit_timestamp) {
                exitDateTime = new Date(parseInt(trade.exit_timestamp));
                if (!isNaN(exitDateTime.getTime())) {
                    exitDate = exitDateTime.toLocaleDateString();
                    exitTime = exitDateTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                }
            }
            console.log('Exit date/time:', exitDate, exitTime, 'from:', trade.exit_time || trade.exit_timestamp);
        } catch (e) {
            console.error('Error formatting exit date:', e, trade.exit_time || trade.exit_timestamp);
        }

        // Calculate price difference
        let priceDiffAmount = 'N/A';
        let priceDiffPercent = 'N/A';

        if (trade.entry_price !== undefined && trade.exit_price !== undefined) {
            const diff = trade.exit_price - trade.entry_price;
            const diffPercent = (diff / trade.entry_price) * 100;

            priceDiffAmount = diff;
            priceDiffPercent = diffPercent;
        }

        // Determine row and text classes
        const rowClass = priceDiffPercent >= 0 ? 'profit-row' : 'loss-row';
        const diffClass = priceDiffPercent >= 0 ? 'profit' : 'loss';
        const positionType = trade.position_type ? trade.position_type.toUpperCase() : 'LONG';

        html += `
            <tr class="${rowClass}">
                <td>${index + 1}</td>
                <td>${positionType}</td>
                <td>${entryDate}</td>
                <td>${entryTime}</td>
                <td>$${trade.entry_price ? trade.entry_price.toFixed(4) : 'N/A'}</td>
                <td>${exitDate}</td>
                <td>${exitTime}</td>
                <td>$${trade.exit_price ? trade.exit_price.toFixed(4) : 'N/A'}</td>
                <td class="${diffClass}">${priceDiffPercent >= 0 ? '+' : '-'}$${typeof priceDiffAmount === 'number' ? Math.abs(priceDiffAmount).toFixed(2) : priceDiffAmount}</td>
                <td class="${diffClass}">${priceDiffPercent >= 0 ? '+' : '-'}${typeof priceDiffPercent === 'number' ? Math.abs(priceDiffPercent).toFixed(2) : priceDiffPercent}%</td>
            </tr>
        `;
    });

    html += `
            </tbody>
        </table>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Extract trade data
            const trades = ${JSON.stringify(trades)};

            // Prepare data for the chart
            const labels = trades.map((_, i) => \`Trade \${i + 1}\`);
            const data = trades.map(trade => ((trade.exit_price - trade.entry_price) / trade.entry_price) * 100);
            const backgroundColors = trades.map(trade =>
                ((trade.exit_price - trade.entry_price) / trade.entry_price) * 100 >= 0
                    ? 'rgba(76, 175, 80, 0.6)'
                    : 'rgba(244, 67, 54, 0.6)'
            );
            const borderColors = trades.map(trade =>
                ((trade.exit_price - trade.entry_price) / trade.entry_price) * 100 >= 0
                    ? 'rgba(76, 175, 80, 1)'
                    : 'rgba(244, 67, 54, 1)'
            );

            // Create the chart
            const ctx = document.getElementById('tradeChart').getContext('2d');
            const tradeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Trade P/L (%)',
                        data: data,
                        backgroundColor: backgroundColors,
                        borderColor: borderColors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#e0e0e0'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: '#e0e0e0'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: '#e0e0e0'
                            }
                        }
                    }
                }
            });
        });
    </script>
    `;

    return html;
}
