// --- Global Variables ---
let priceChart = null; // To hold the main price/candlestick Chart.js instance
let volumeChart = null; // To hold the volume Chart.js instance
let autoOptimizationInterval = null;

// --- Utility Functions ---
function displayStatusMessage(message, type = 'info') {
    console.log(`Status message: ${message} (${type})`); // Add console log
    const statusDiv = document.getElementById('status-message');
    if (!statusDiv) {
        console.error('Status message div not found');
        return;
    }
    statusDiv.textContent = message;
    statusDiv.className = `status-message ${type}`; // Apply 'success' or 'error' class
    statusDiv.style.display = 'block'; // Make sure it's visible
    // Clear message after 5 seconds
    setTimeout(() => {
        // Check if the message is still the same before clearing
        if (statusDiv.textContent === message) {
             statusDiv.textContent = '';
             statusDiv.className = 'status-message';
             statusDiv.style.display = 'none';
        }
    }, 5000);
}

// Helper function to populate a select dropdown
function populatePairSelect(selectElement, pairs) {
    if (!selectElement || selectElement.tagName !== 'SELECT') {
        console.warn("Attempted to populate non-select element or element not found:", selectElement);
        return; // Don't try to populate non-select elements
    }
    selectElement.innerHTML = '<option value="">-- Select Pair --</option>'; // Clear existing options and add default
    if (pairs && Array.isArray(pairs)) {
        pairs.forEach(pair => {
            const option = document.createElement('option');
            option.value = pair; // Assuming pairs is an array of strings like "BTC-USD"
            option.textContent = pair;
            selectElement.appendChild(option);
        });
    } else {
        console.warn("No pairs data provided to populate select:", selectElement?.id); // Use optional chaining
        selectElement.innerHTML = '<option value="">Error loading pairs</option>'; // Show error in dropdown
    }
}


// --- Tab Navigation ---
function openTab(evt, tabName) {
    // Hide all tab content
    var tabcontent = document.getElementsByClassName("tab-content");
    for (var i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }

    // Remove active class from all tab buttons
    var tablinks = document.getElementsByClassName("tab-button");
    for (var i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Show the current tab and add active class to the button
    const currentTab = document.getElementById(tabName);
    if (currentTab) {
        currentTab.style.display = "block";
    }
    // Ensure evt and evt.currentTarget exist before accessing className
    if (evt && evt.currentTarget) {
        evt.currentTarget.className += " active";
    }
}

// --- Configuration Tab ---
const configForm = document.getElementById('config-form');
const testApiButton = document.getElementById('test-api-button');
const configStatusDiv = document.getElementById('config-status');

// Function to populate form fields from config data
function populateConfigForm(config) {
    if (!config) return;

    // Credentials
    if (config.credentials) {
        const walletAddrInput = document.getElementById('wallet_address');
        const privateKeyInput = document.getElementById('private_key');
        if (walletAddrInput) walletAddrInput.value = config.credentials.wallet_address || '';
        if (privateKeyInput) privateKeyInput.value = config.credentials.private_key || '';
    }

    // Trading Parameters (ensure elements exist before setting values)
    if (config.trading) {
        const livePairSelect = document.getElementById('live-pair');
        const liveStrategySelect = document.getElementById('live-strategy');
        const capitalInput = document.getElementById('capital');
        const takeProfitInput = document.getElementById('take-profit');
        const stopLossInput = document.getElementById('stop-loss');

        if (livePairSelect) livePairSelect.value = config.trading.pair || '';
        if (liveStrategySelect) liveStrategySelect.value = config.trading.strategy || '';
        if (capitalInput) capitalInput.value = config.trading.capital || 100.0;
        if (takeProfitInput) takeProfitInput.value = config.trading.take_profit || 5.0;
        if (stopLossInput) stopLossInput.value = config.trading.stop_loss || 2.0;
    }
}


if (configForm) {
    configForm.addEventListener('submit', async (event) => {
        event.preventDefault(); // Prevent default form submission

        // Collect all relevant data for the config endpoint
        const formData = new FormData();
        // Use optional chaining in case elements are missing
        formData.append('wallet_address', document.getElementById('wallet_address')?.value || '');
        formData.append('private_key', document.getElementById('private_key')?.value || '');
        formData.append('selected_pair', document.getElementById('live-pair')?.value || '');
        formData.append('selected_strategy', document.getElementById('live-strategy')?.value || '');
        formData.append('capital', document.getElementById('capital')?.value || '');
        formData.append('take_profit', document.getElementById('take-profit')?.value || '');
        formData.append('stop_loss', document.getElementById('stop-loss')?.value || '');

        if (configStatusDiv) {
            configStatusDiv.textContent = 'Saving...';
            configStatusDiv.style.color = 'black'; // Reset color
        }

        try {
            const response = await fetch('/config', {
                method: 'POST',
                body: formData // Send combined form data
            });
            const result = await response.json();

            // Handle different success/warning/error statuses from backend
            if (configStatusDiv) {
                if (result.status === 'success') {
                    configStatusDiv.textContent = `Success: ${result.message}`;
                    configStatusDiv.style.color = 'green';
                    displayStatusMessage(result.message, 'success');
                } else if (result.status === 'warning') {
                    configStatusDiv.textContent = `Warning: ${result.message}`;
                    configStatusDiv.style.color = 'orange';
                    displayStatusMessage(result.message, 'warning'); // Use a 'warning' type if defined, else 'info'
                } else { // Error case
                    configStatusDiv.textContent = `Error: ${result.message}`;
                    configStatusDiv.style.color = 'red';
                    displayStatusMessage(result.message, 'error');
                }
            }
        } catch (error) {
            console.error('Error saving config:', error);
            if (configStatusDiv) {
                configStatusDiv.textContent = 'Error saving configuration. Check console.';
                configStatusDiv.style.color = 'red';
            }
            displayStatusMessage('Error saving configuration.', 'error');
        }
    });
}

if (testApiButton) {
    testApiButton.addEventListener('click', async () => {
        if (configStatusDiv) {
            configStatusDiv.textContent = 'Testing credentials...';
            configStatusDiv.style.color = 'black'; // Reset color
        }
        try {
            // The backend /test_api now uses the saved credentials, no need to send body
            const response = await fetch('/test_api', { method: 'POST' });
            const result = await response.json();
             if (configStatusDiv) {
                 if (response.ok) { // Check if response status is 2xx
                    configStatusDiv.textContent = `Success: ${result.message}`;
                    configStatusDiv.style.color = 'green';
                    displayStatusMessage(result.message, 'success');
                } else {
                    configStatusDiv.textContent = `Error: ${result.message}`;
                    configStatusDiv.style.color = 'red';
                    displayStatusMessage(result.message || 'Test failed.', 'error');
                }
             }
        } catch (error) {
            console.error('Error testing API credentials:', error);
            if (configStatusDiv) {
                configStatusDiv.textContent = 'Error testing credentials. Check console.';
                configStatusDiv.style.color = 'red';
            }
            displayStatusMessage('Error testing credentials.', 'error');
        }
    });
}


// --- Backtesting Tab ---
const backtestPairSelect = document.getElementById('backtest-pair');
const livePairSelect = document.getElementById('live-pair'); // Used in config and live trading
const startDateInput = document.getElementById('start-date');
const endDateInput = document.getElementById('end-date');
const fetchDataButton = document.getElementById('fetch-data-button');
const runBacktestButton = document.getElementById('run-backtest-button');
const backtestResultsDiv = document.getElementById('backtest-results')?.querySelector('pre'); // Use optional chaining
const priceChartCanvas = document.getElementById('price-chart'); // Renamed for clarity
const volumeChartCanvas = document.getElementById('volume-chart'); // Get volume canvas
const backtestStrategySelect = document.getElementById('backtest-strategy');
const liveStrategySelect = document.getElementById('live-strategy'); // Used in config and live trading
const strategySelect = document.getElementById('strategy-select'); // Dropdown in Strategy tab

// Global variables for debugging
let debugInfo = {
    fetchDataClicked: false,
    historicalDataReceived: false,
    chartInitialized: false
};
const strategyEditor = document.getElementById('strategy-editor');
const strategyNameInput = document.getElementById('strategy-name');
const saveStrategyButton = document.getElementById('save-strategy-button');
const loadStrategyButton = document.getElementById('load-strategy-button');
const testPineScriptButton = document.getElementById('test-pine-script-button'); // Get the new button
const strategyStatusDiv = document.getElementById('strategy-status');
const pineTestResultsDiv = document.getElementById('pine-test-results'); // Get the results div

// Add strategy optimization button and results div
const optimizeStrategyButton = document.getElementById('optimize-strategy-button');
const optimizationResultsDiv = document.getElementById('optimization-results');

// Function to fetch pairs and populate select dropdowns
async function fetchAndPopulatePairs() {
    try {
        const response = await fetch('/hyperliquid/pairs');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();

        // Populate the actual select dropdowns
        populatePairSelect(livePairSelect, data.pairs); // For Config/Live Trading
        // Assuming backtestPairSelect is an INPUT, not a SELECT
        // populatePairSelect(backtestPairSelect, data.pairs); // For Backtesting
        populateAutoOptPairs(data.pairs); // This function correctly targets 'auto-opt-pair' select

    } catch (error) {
        console.error("Failed to fetch or populate pairs:", error); // Updated error message
        displayStatusMessage("Failed to load trading pairs.", "error");
    }
}

// Function to fetch strategies and populate select dropdowns
async function fetchAndPopulateStrategies() {
    try {
        const response = await fetch('/strategies');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();

        // Clear all relevant strategy dropdowns
        if (backtestStrategySelect) backtestStrategySelect.innerHTML = '';
        if (liveStrategySelect) liveStrategySelect.innerHTML = '';
        if (strategySelect) strategySelect.innerHTML = '<option value="">-- Select Strategy --</option>'; // Clear and add default

        if (data.strategies && data.strategies.length > 0) {
            console.log('Loaded strategies:', data.strategies);

            // Filter strategies by type
            const pineScriptStrategies = data.strategies.filter(s => s.type === 'pine');
            const pythonStrategies = data.strategies.filter(s => s.type === 'python');

            // Populate the main load dropdown (Strategies Tab) - only Pine Script strategies
            if (strategySelect && pineScriptStrategies.length > 0) {
                const pineGroup = document.createElement('optgroup');
                pineGroup.label = 'Pine Script Strategies';

                pineScriptStrategies.forEach(strategy => {
                    const option = document.createElement('option');
                    option.value = strategy.id;
                    option.textContent = strategy.name;
                    option.dataset.type = 'pine';
                    pineGroup.appendChild(option);
                });

                strategySelect.appendChild(pineGroup);
            }

            // Populate Backtesting and Live Trading dropdowns (all strategies)
            const populateDropdown = (selectElem, label, strategies) => {
                if (!selectElem || strategies.length === 0) return;
                const group = document.createElement('optgroup');
                group.label = label;
                strategies.forEach(strategy => {
                    const option = document.createElement('option');
                    option.value = strategy.id;
                    option.textContent = strategy.name.replace(' (Pine Script)', ''); // Clean name if needed
                    option.dataset.type = strategy.type;
                    group.appendChild(option);
                });
                selectElem.appendChild(group);
            };

            populateDropdown(backtestStrategySelect, 'Python Strategies', pythonStrategies);
            populateDropdown(backtestStrategySelect, 'Pine Script Strategies', pineScriptStrategies);
            populateDropdown(liveStrategySelect, 'Python Strategies', pythonStrategies);
            populateDropdown(liveStrategySelect, 'Pine Script Strategies', pineScriptStrategies);

        } else {
            // Add a default placeholder if no strategies are loaded
            const defaultOption = "<option value=''>No custom strategies saved</option>";
            const defaultOptionAll = "<option value=''>No strategies available</option>";
            if (strategySelect) strategySelect.innerHTML = defaultOption; // Specific message for custom
            if (backtestStrategySelect) backtestStrategySelect.innerHTML = defaultOptionAll;
            if (liveStrategySelect) liveStrategySelect.innerHTML = defaultOptionAll;
        }
    } catch (error) {
        console.error("Failed to fetch strategies:", error);
        displayStatusMessage("Failed to load strategies.", "error");
        const errorOption = "<option value=''>Error loading strategies</option>";
        if (backtestStrategySelect) backtestStrategySelect.innerHTML = errorOption;
        if (liveStrategySelect) liveStrategySelect.innerHTML = errorOption;
        if (strategySelect) strategySelect.innerHTML = errorOption; // Also update the strategy tab dropdown
    }
}

// Function to display status messages specifically for the strategy tab
function displayStrategyStatus(message, type = 'info') {
    if (strategyStatusDiv) {
        strategyStatusDiv.textContent = message;
        strategyStatusDiv.className = `status-message ${type}`;
        setTimeout(() => {
            // Check if the message is still the same before clearing
            if (strategyStatusDiv.textContent === message) {
                strategyStatusDiv.textContent = '';
                strategyStatusDiv.className = 'status-message';
            }
        }, 5000);
    }
}

// Event listener for Save Strategy button
if (saveStrategyButton) {
    saveStrategyButton.addEventListener('click', async () => {
        const name = strategyNameInput?.value.trim(); // Use optional chaining
        const code = strategyEditor?.value;

        if (!name || !code) {
            displayStrategyStatus("Please enter both a strategy name and code.", "error");
            return;
        }

        displayStrategyStatus("Saving strategy...", "info");
        console.log(`Saving strategy: Name='${name}', Code='${code.substring(0, 50)}...'`); // DEBUG
        try {
            const response = await fetch('/strategies/save', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name: name, code: code }) // Assuming backend determines type or defaults to Pine
            });
            const result = await response.json();
            console.log("Save response status:", response.status); // DEBUG
            console.log("Save response body:", result); // DEBUG
            if (response.ok) {
                displayStrategyStatus(result.message, "success");
                // Refresh strategy lists after saving
                fetchAndPopulateStrategies();
                if (strategyNameInput) strategyNameInput.value = ''; // Clear name input after successful save
            } else {
                displayStrategyStatus(`Error: ${result.message}`, "error");
            }
        } catch (error) {
            console.error("Error saving strategy:", error);
            displayStrategyStatus("Failed to save strategy. Check console.", "error");
        }
    });
}

// Event listener for Delete Strategy button
const deleteStrategyButton = document.getElementById('delete-strategy-button');
if (deleteStrategyButton) {
    deleteStrategyButton.addEventListener('click', async () => {
        const strategyId = strategySelect?.value; // Use optional chaining
        if (!strategyId) {
            displayStrategyStatus("Please select a strategy to delete.", "error");
            return;
        }

        // Prevent deleting built-in strategies (adjust IDs if necessary)
        if (strategyId === 'simple_sma_cross' || strategyId === 'rsi_oversold') {
            displayStrategyStatus("Cannot delete built-in strategies.", "error");
            return;
        }

        // Confirmation dialog
        const selectedOptionText = strategySelect.options[strategySelect.selectedIndex]?.text || strategyId;
        if (!confirm(`Are you sure you want to delete the strategy "${selectedOptionText}"? This action cannot be undone.`)) {
            return; // User cancelled
        }

        displayStrategyStatus("Deleting strategy...", "info");
        try {
            // Use DELETE method and include strategy ID in the URL
            const response = await fetch(`/strategies/delete/${strategyId}`, {
                method: 'DELETE'
            });
            const result = await response.json();
            if (response.ok) {
                displayStrategyStatus(result.message, "success");
                // Refresh strategy lists after deleting
                fetchAndPopulateStrategies();
                // Optionally clear editor/name if the deleted strategy was loaded
                if (strategyNameInput?.value === strategyId) {
                    if (strategyEditor) strategyEditor.value = '';
                    strategyNameInput.value = '';
                }
            } else {
                displayStrategyStatus(`Error: ${result.message}`, "error");
            }
        } catch (error) {
            console.error("Error deleting strategy:", error);
            displayStrategyStatus("Failed to delete strategy. Check console.", "error");
        }
    });
}
// Event listener for Test Pine Script button
if (testPineScriptButton) {
    testPineScriptButton.addEventListener('click', async () => {
        const code = strategyEditor?.value;
        if (!code || !code.trim()) {
            if (pineTestResultsDiv) {
                pineTestResultsDiv.textContent = 'Error: Editor is empty.';
                pineTestResultsDiv.style.color = 'red';
            }
            return;
        }

        if (pineTestResultsDiv) {
            pineTestResultsDiv.textContent = 'Testing Pine Script...';
            pineTestResultsDiv.style.color = 'black';
        }

        try {
            // TODO: Create this endpoint in app.py if it doesn't exist
            const response = await fetch('/strategies/test_pine', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ code: code })
            });
            const result = await response.json();

            if (pineTestResultsDiv) {
                if (response.ok && result.status === 'success') {
                    pineTestResultsDiv.textContent = `Success: ${result.message}`;
                    pineTestResultsDiv.style.color = 'green';
                } else {
                    pineTestResultsDiv.textContent = `Error: ${result.message || 'Unknown error'}`;
                    pineTestResultsDiv.style.color = 'red';
                }
            }
        } catch (error) {
            console.error("Error testing Pine Script:", error);
            if (pineTestResultsDiv) {
                pineTestResultsDiv.textContent = 'Failed to test Pine Script. Check console.';
                pineTestResultsDiv.style.color = 'red';
            }
        }
    });
}


// Event listener for Load Strategy button
if (loadStrategyButton) {
    loadStrategyButton.addEventListener('click', async () => {
        const strategyId = strategySelect?.value;
        if (!strategyId) {
            displayStrategyStatus("Please select a strategy to load.", "error");
            return;
        }

        // Handle built-in strategies (no code to load) - Adjust IDs if needed
        if (strategyId === 'simple_sma_cross' || strategyId === 'rsi_oversold') {
             displayStrategyStatus("Built-in strategies don't have editable code.", "info");
             if (strategyEditor) strategyEditor.value = `# Code for built-in strategy '${strategyId}' is handled server-side.`;
             if (strategyNameInput) strategyNameInput.value = strategyId; // Show the ID in the name field
             return;
        }


        displayStrategyStatus("Loading strategy...", "info");
        console.log(`Loading strategy ID: ${strategyId}`); // DEBUG
        try {
            const response = await fetch(`/strategies/load/${strategyId}`);
            const result = await response.json();
            console.log("Load response status:", response.status); // DEBUG
            console.log("Load response body:", result); // DEBUG
            if (response.ok) {
                if (strategyEditor) strategyEditor.value = result.code || '';
                if (strategyNameInput) strategyNameInput.value = result.id || ''; // Populate name field with loaded strategy's ID/filename
                displayStrategyStatus(`Strategy "${result.id || strategyId}" loaded successfully.`, "success");
            } else {
                displayStrategyStatus(`Error: ${result.message}`, "error");
                if (strategyEditor) strategyEditor.value = ''; // Clear editor on error
                if (strategyNameInput) strategyNameInput.value = '';
            }
        } catch (error) {
            console.error("Error loading strategy:", error);
            displayStrategyStatus("Failed to load strategy. Check console.", "error");
            if (strategyEditor) strategyEditor.value = '';
            if (strategyNameInput) strategyNameInput.value = '';
        }
    });
}


// Function to set default dates (last 7 days)
function setDefaultDates() {
    // Ensure inputs exist before setting values
    if (!startDateInput || !endDateInput) return;

    const today = new Date();
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);

    // Format dates as YYYY-MM-DD
    const formatDate = (date) => date.toISOString().split('T')[0];

    // Only set if empty
    if (!endDateInput.value) endDateInput.value = formatDate(today);
    if (!startDateInput.value) startDateInput.value = formatDate(sevenDaysAgo);
}

// Function to display status messages (already defined above)

// Chart functions are now expected to be in charts.js (e.g., updateChartsWithBacktestResults)
// Chart initialization is now expected to be in charts.js (via DOMContentLoaded)


// Event listener for fetching historical data (potentially for initial chart display)
if (fetchDataButton) {
    fetchDataButton.addEventListener('click', async () => {
        const pair = backtestPairSelect?.value; // Use optional chaining
        const startDate = startDateInput?.value;
        const endDate = endDateInput?.value;

        if (!pair || !startDate || !endDate) {
            displayStatusMessage("Please select a pair and date range.", "error");
            return;
        }

        displayStatusMessage("Fetching data...", "info");
        try {
            // Assuming this endpoint returns data suitable for chart initialization
            const response = await fetch(`/hyperliquid/data?pair=${pair}&start_date=${startDate}&end_date=${endDate}`);
            const result = await response.json(); // Try to parse JSON even if response is not ok

            if (!response.ok) {
                const errorMsg = result.error || `HTTP error! status: ${response.status}`;
                throw new Error(errorMsg);
            }

            if (result.data && Array.isArray(result.data)) {
                if (result.data.length > 0) {
                    displayStatusMessage("Data fetched successfully.", "success");
                    // Call chart initialization function from charts.js if it exists
                    if (typeof initializeChart === 'function') {
                        initializeChart(result.data);
                    } else if (typeof createPriceChart === 'function') { // Fallback
                        console.warn("initializeChart not found, using createPriceChart");
                        createPriceChart(result.data);
                    } else {
                        console.error("Chart initialization function (initializeChart or createPriceChart) not found.");
                    }
                } else {
                    displayStatusMessage("No data returned for the selected range.", "warning");
                    if (typeof initializeChart === 'function') initializeChart([]);
                    else if (typeof createPriceChart === 'function') createPriceChart([]);
                }
            } else {
                 console.warn("Unexpected data format received:", result);
                 throw new Error("Received unexpected data format from server.");
            }
        } catch (error) {
            console.error("Failed to fetch historical data:", error);
            displayStatusMessage(`Failed to fetch historical data: ${error.message}`, "error");
            if (typeof initializeChart === 'function') initializeChart([]);
            else if (typeof createPriceChart === 'function') createPriceChart([]);
        }
    });
}

// Removed local updateChartWithSignals function from script.js
// Chart updates are now handled by charts.js

// Event listener for fetching data (This might be redundant if backtest fetches its own data)
// This DOMContentLoaded listener seems redundant as there's another one below.
// Combining them into one is better practice.
/*
document.addEventListener('DOMContentLoaded', () => {
    const fetchDataButton = document.getElementById('fetch-data-button');
    if (fetchDataButton) {
        // ... (fetch logic - already present above and below)
    }
});
*/

// Event listener for running backtest (This should be inside the main DOMContentLoaded)
// Combining this into the main listener below.
/*
document.addEventListener('DOMContentLoaded', () => {
    const runBacktestButton = document.getElementById('run-backtest-button');
    if (runBacktestButton) {
        runBacktestButton.addEventListener('click', runBacktest);
    }
    // ... (date setting logic - moved to main listener)
});
*/

/**
 * Run a backtest with the current settings
 */
async function runBacktest() {
    // Get DOM elements
    const backtestPairSelect = document.getElementById('backtest-pair');
    const backtestStrategySelect = document.getElementById('backtest-strategy');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const backtestResultsDiv = document.getElementById('backtest-results')?.querySelector('pre');
    const tradeSummaryDiv = document.getElementById('trade-summary'); // Added for summary display
    const detailedTradeResultsDiv = document.getElementById('detailed-trade-results'); // Added for table display


    // Validate DOM elements
    if (!backtestResultsDiv || !tradeSummaryDiv || !detailedTradeResultsDiv) { // Check new divs too
        console.error('Backtest results/summary/details div not found');
        return;
    }

    // Get values from inputs
    const pair = backtestPairSelect?.value; // Use optional chaining
    const strategy = backtestStrategySelect?.value;
    let startDate = startDateInput?.value;
    let endDate = endDateInput?.value;

    // Validate inputs
    if (!pair || !strategy) {
        displayStatusMessage("Please select pair and strategy.", "error");
        return;
    }

    // Set default dates if they're empty (redundant if set in DOMContentLoaded, but safe)
    if (!startDate) {
        const defaultStartDate = new Date();
        defaultStartDate.setDate(defaultStartDate.getDate() - 30);
        startDate = defaultStartDate.toISOString().split('T')[0];
        if (startDateInput) startDateInput.value = startDate;
    }
    if (!endDate) {
        const defaultEndDate = new Date();
        endDate = defaultEndDate.toISOString().split('T')[0];
        if (endDateInput) endDateInput.value = endDate;
    }

    // Show loading message
    backtestResultsDiv.textContent = 'Running backtest...';
    tradeSummaryDiv.innerHTML = ''; // Clear previous summary
    detailedTradeResultsDiv.innerHTML = ''; // Clear previous table
    displayStatusMessage("Running backtest...", "info");

    try {
        // Log request data
        console.log('Sending backtest request with data:', {
            pair, strategy, start_date: startDate, end_date: endDate
        });

        // Send request to server
        const response = await fetch('/backtest', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                pair: pair,
                strategy: strategy,
                start_date: startDate,
                end_date: endDate
            })
        });

        // Parse response
        const result = await response.json();
        console.log('Backtest response received in script.js:', result); // Log the whole object

        // --- START NEW DETAILED LOGGING ---
        if (result && result.trades) {
            console.log('Type of result.trades in script.js:', typeof result.trades);
            console.log('Is result.trades an array in script.js?', Array.isArray(result.trades));
            console.log('Content of result.trades in script.js:', JSON.stringify(result.trades, null, 2)); // Log the content as string
        } else {
            console.log('result or result.trades is missing/falsy in script.js');
        }
        // --- END NEW DETAILED LOGGING ---

        // Handle response
        if (response.ok) {
            // Format results for display (Summary)
            let summaryText = "Backtest Summary:\n"; // Changed title
            summaryText += `  Total Trades: ${result.number_of_trades || 0}\n`;
            summaryText += `  Winning Trades: ${result.winning_trades || 0}\n`;
            summaryText += `  Losing Trades: ${result.losing_trades || 0}\n`;
            summaryText += `  Win Ratio: ${result.win_ratio_pct?.toFixed(2) || 0}%\n`; // Use optional chaining and formatting
            summaryText += `  Total P&L: ${result.total_pnl_pct?.toFixed(2) || 0}%\n`; // Use optional chaining and formatting

            // Add capital information if available
            if (result.initial_capital !== undefined && result.final_capital !== undefined) { // Check for undefined
                summaryText += `\nCapital Performance:\n`; // Changed title
                summaryText += `  Initial Capital: $${result.initial_capital.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`; // Format currency
                summaryText += `  Final Capital: $${result.final_capital.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`; // Format currency
                summaryText += `  Growth: ${result.capital_growth_pct?.toFixed(2) || 0}%\n`; // Use optional chaining and formatting
            }

            // Create HTML content for the summary
            let summaryHtml = `<div class="backtest-summary">${summaryText.replace(/\n/g, '<br>')}</div>`;
            tradeSummaryDiv.innerHTML = summaryHtml; // Display summary

            // Create HTML content for the trade table
            let tableHtml = '';
            if (result.trades && result.trades.length > 0) {
                console.log(`Found ${result.trades.length} trades in result`);
                // Use createTradeTable function (assuming it's globally available or imported from createTradeTable.js)
                if (typeof createTradeTable === 'function') {
                     tableHtml = createTradeTable(result.trades);
                } else if (typeof createSimpleTradeTable === 'function') { // Fallback
                     console.warn("createTradeTable not found, using createSimpleTradeTable.");
                     tableHtml = createSimpleTradeTable(result.trades);
                } else {
                     console.error('Trade table creation function not found.');
                     tableHtml = '<p class="error">Error: Trade table display function is missing.</p>';
                }
            } else {
                console.log('No trades found in result');
                tableHtml = '<div class="no-trades">No trades were executed during this backtest period.</div>';
            }
            detailedTradeResultsDiv.innerHTML = tableHtml; // Display trade table
            backtestResultsDiv.textContent = JSON.stringify(result, null, 2); // Keep raw JSON display

            displayStatusMessage("Backtest completed successfully.", "success");

            // --- Chart Update ---
            // Call the function in charts.js to update charts with historical data and markers
            if (typeof updateChartsWithBacktestResults === 'function') {
                console.log('Calling updateChartsWithBacktestResults from script.js'); // Corrected log origin
                // Pass historical data and the results object containing trades
                // The charts.js function expects (historicalData, backtestResults)
                // where backtestResults.result contains the trades.
                updateChartsWithBacktestResults(result.historical_data, { result: result }); // Pass the potentially non-array trades
            } else {
                console.error('updateChartsWithBacktestResults function not found in charts.js!');
                displayStatusMessage("Error: Chart update function missing.", "error");
            }
            // Removed the logic that called local updateChartWithSignals and createPriceChart

        } else {
            // Handle backtest error
            const errorMsg = result.error || 'Unknown error';
            backtestResultsDiv.textContent = `Error: ${errorMsg}`;
            tradeSummaryDiv.innerHTML = ''; // Clear summary on error
            detailedTradeResultsDiv.innerHTML = `<p class="error">Backtest failed: ${errorMsg}</p>`; // Show error in table area
            displayStatusMessage(`Backtest failed: ${errorMsg}`, "error");
            // Optionally clear chart on error by calling update function with null/empty data
            // Ensure the function exists before calling
            if (typeof updateChartsWithBacktestResults === 'function') {
                 updateChartsWithBacktestResults(null, null); // Or pass empty arrays to clear
            } else if (typeof createPriceChart === 'function') {
                 // Fallback to clearing using createPriceChart if the main function isn't found
                 console.warn("updateChartsWithBacktestResults not found, attempting fallback clear with createPriceChart");
                 createPriceChart([]);
            } else if (typeof initializeChart === 'function') {
                 // Further fallback
                 console.warn("createPriceChart not found, attempting fallback clear with initializeChart");
                 initializeChart([]);
            }
        }
    } catch (error) {
        // Handle fetch/processing exception
        console.error('Error during backtest fetch/processing:', error);
        const errorMsg = error.message || 'Client-side error during backtest';
        backtestResultsDiv.textContent = `Error: ${errorMsg}`;
        tradeSummaryDiv.innerHTML = ''; // Clear summary on error
        detailedTradeResultsDiv.innerHTML = `<p class="error">Backtest failed: ${errorMsg}</p>`; // Show error in table area
        displayStatusMessage("Backtest failed with an error.", "error");
        // Optionally clear chart on error
        // Ensure the function exists before calling
        if (typeof updateChartsWithBacktestResults === 'function') {
             updateChartsWithBacktestResults(null, null); // Or pass empty arrays to clear
        } else if (typeof createPriceChart === 'function') {
            console.warn("updateChartsWithBacktestResults not found, attempting fallback clear with createPriceChart");
            createPriceChart([]);
        } else if (typeof initializeChart === 'function') {
             console.warn("createPriceChart not found, attempting fallback clear with initializeChart");
             initializeChart([]);
        }
    }
}


// --- Live Trading Tab ---
const startLiveButton = document.getElementById('start-live-button');
const stopLiveButton = document.getElementById('stop-live-button');
const liveStatusDiv = document.getElementById('live-status')?.querySelector('p'); // Use optional chaining

if (startLiveButton) {
    startLiveButton.addEventListener('click', () => sendLiveTradeCommand('start'));
}
if (stopLiveButton) {
    stopLiveButton.addEventListener('click', () => sendLiveTradeCommand('stop'));
}

async function sendLiveTradeCommand(action) {
    const pair = livePairSelect?.value; // Use optional chaining
    const strategy = document.getElementById('live-strategy')?.value;
    const capital = document.getElementById('capital')?.value;
    const takeProfit = document.getElementById('take-profit')?.value;
    const stopLoss = document.getElementById('stop-loss')?.value;

    if (!pair || !strategy || !capital || !takeProfit || !stopLoss) {
        displayStatusMessage("Please configure all live trading parameters.", "error");
        return;
    }

    if (liveStatusDiv) liveStatusDiv.textContent = `Sending ${action} command...`;
    try {
        const response = await fetch('/live_trade', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: action,
                pair: pair,
                strategy: strategy,
                capital: parseFloat(capital),
                take_profit: parseFloat(takeProfit),
                stop_loss: parseFloat(stopLoss)
            })
        });
        const result = await response.json();
         if (response.ok) {
            if (liveStatusDiv) liveStatusDiv.textContent = result.message; // Update status based on backend response
            displayStatusMessage(`Live trading ${action} requested.`, "success");
            // Update button states
            if (startLiveButton) startLiveButton.disabled = (action === 'start');
            if (stopLiveButton) stopLiveButton.disabled = (action === 'stop');
        } else {
             if (liveStatusDiv) liveStatusDiv.textContent = `Error: ${result.message}`;
             displayStatusMessage(`Failed to ${action} live trading.`, "error");
        }
    } catch (error) {
        console.error(`Error sending ${action} command:`, error);
        if (liveStatusDiv) liveStatusDiv.textContent = `Error sending ${action} command. Check console.`;
        displayStatusMessage(`Error sending ${action} command.`, "error");
    }
}


// --- Initial Setup on Page Load ---
// Consolidate all DOMContentLoaded logic here
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM fully loaded - Main Listener');

    // --- Get Element References ---
    // (Assuming elements are correctly referenced where needed, e.g., inside functions or globally like configForm)

    // --- Initial Data Fetching & Setup ---
    setDefaultDates(); // Set default backtesting dates first
    fetchAndPopulatePairs(); // Fetch pairs for dropdowns
    fetchAndPopulateStrategies().then(() => { // Fetch strategies, then populate config
        if (window.APP_CONFIG) {
            populateConfigForm(window.APP_CONFIG);
        } else {
            console.warn("Global APP_CONFIG not found. Form fields not pre-populated.");
            // Optionally fetch config if not embedded: fetch('/config').then(res => res.json()).then(populateConfigForm);
        }
    });
    loadAvailablePythonStrategies(); // Load Python strategies for editor

    // Check initial auto-optimization status
    updateOptimizationStatus();
    fetch('/auto_optimize/status').then(res => res.ok ? res.json() : Promise.reject('Failed status check'))
      .then(status => {
        if (status.is_running && !autoOptimizationInterval) {
             console.log('Auto-optimization is running on load, starting status polling.');
             autoOptimizationInterval = setInterval(updateOptimizationStatus, 10000); // Poll every 10 seconds
        }
      }).catch(err => console.warn("Initial auto-opt status check failed:", err));


    // --- Event Listeners ---

    // Backtesting Tab - Run Backtest Button
    const runBtn = document.getElementById('run-backtest-button');
    if (runBtn) {
        runBtn.addEventListener('click', runBacktest);
    } else {
        console.error("Run Backtest button not found!");
    }

    // Backtesting Tab - Fetch Data Button (if still needed)
    const fetchBtn = document.getElementById('fetch-data-button');
    if (fetchBtn) {
        fetchBtn.addEventListener('click', async () => {
            // This logic is duplicated in the global scope, consider removing one
            console.warn('Fetch Data button clicked - ensure this is necessary.');
            const pair = backtestPairSelect?.value;
            const startDate = startDateInput?.value;
            const endDate = endDateInput?.value;
            if (!pair || !startDate || !endDate) {
                displayStatusMessage("Please select a pair and date range.", "error"); return;
            }
            displayStatusMessage("Fetching data...", "info");
            // ... (rest of fetch logic as defined globally) ...
             try {
                const response = await fetch(`/hyperliquid/data?pair=${pair}&start_date=${startDate}&end_date=${endDate}`);
                const result = await response.json();
                if (!response.ok) throw new Error(result.error || `HTTP error! status: ${response.status}`);
                if (result.data && Array.isArray(result.data)) {
                    if (result.data.length > 0) {
                        displayStatusMessage("Data fetched successfully.", "success");
                        if (typeof initializeChart === 'function') initializeChart(result.data);
                        else if (typeof createPriceChart === 'function') createPriceChart(result.data);
                    } else {
                        displayStatusMessage("No data returned.", "warning");
                         if (typeof initializeChart === 'function') initializeChart([]);
                         else if (typeof createPriceChart === 'function') createPriceChart([]);
                    }
                } else { throw new Error("Unexpected data format."); }
            } catch (error) {
                console.error("Failed to fetch historical data:", error);
                displayStatusMessage(`Failed to fetch data: ${error.message}`, "error");
                 if (typeof initializeChart === 'function') initializeChart([]);
                 else if (typeof createPriceChart === 'function') createPriceChart([]);
            }
        });
    }

    // Theme Toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        const currentTheme = localStorage.getItem('theme') || 'light'; // Default to light
        const applyTheme = (theme) => {
            document.body.classList.toggle('dark-mode', theme === 'dark');
            themeToggle.textContent = theme === 'dark' ? 'Light Mode' : 'Dark Mode';
            // Ensure chart theme update function exists before calling
            if (typeof updateChartTheme === 'function') {
                updateChartTheme(theme);
            } else {
                console.warn("updateChartTheme function not found, cannot update chart theme.");
            }
        };
        applyTheme(currentTheme); // Apply initial theme

        themeToggle.addEventListener('click', () => {
            const newTheme = document.body.classList.contains('dark-mode') ? 'light' : 'dark';
            localStorage.setItem('theme', newTheme);
            applyTheme(newTheme);
        });
    } else {
        console.error('Theme toggle button not found');
    }

    // Configuration Tab Listeners (already defined globally with checks)

    // Strategy Tab Listeners (already defined globally with checks)

    // Python Strategy Tab Listeners
    const pythonStrategySelect = document.getElementById('python-strategy-select');
    const newStrategyButton = document.getElementById('new-strategy-button');
    const deletePythonStrategyButton = document.getElementById('delete-python-strategy-button');
    const savePythonStrategyButton = document.getElementById('save-python-strategy-button');
    const testPythonStrategyButton = document.getElementById('test-python-strategy-button');
    const newStrategyForm = document.getElementById('new-strategy-form');
    const createStrategyButton = document.getElementById('create-strategy-button');
    const cancelStrategyButton = document.getElementById('cancel-strategy-button');
    const newStrategyIdInput = document.getElementById('new-strategy-id');
    const newStrategyNameInput = document.getElementById('new-strategy-name');
    const newStrategyDescriptionInput = document.getElementById('new-strategy-description');
    const newStrategyTemplateSelect = document.getElementById('new-strategy-template');

    if (pythonStrategySelect) {
        pythonStrategySelect.addEventListener('change', function() { loadPythonStrategyDetails(this.value); });
    }
    if (newStrategyButton && newStrategyForm) {
        newStrategyButton.addEventListener('click', () => { newStrategyForm.style.display = 'block'; });
    }
    if (cancelStrategyButton && newStrategyForm) {
        cancelStrategyButton.addEventListener('click', () => {
            newStrategyForm.style.display = 'none';
            if (newStrategyIdInput) newStrategyIdInput.value = '';
            if (newStrategyNameInput) newStrategyNameInput.value = '';
            if (newStrategyDescriptionInput) newStrategyDescriptionInput.value = '';
            if (newStrategyTemplateSelect) newStrategyTemplateSelect.selectedIndex = 0;
        });
    }
    if (createStrategyButton && newStrategyForm) {
        createStrategyButton.addEventListener('click', async () => {
            const strategyId = newStrategyIdInput?.value.trim();
            const strategyName = newStrategyNameInput?.value.trim();
            const strategyDescription = newStrategyDescriptionInput?.value.trim();
            const strategyTemplate = newStrategyTemplateSelect?.value;

            if (!strategyId || !strategyName || !strategyDescription || !strategyTemplate) {
                displayStatusMessage('Please fill in all fields for the new Python strategy.', 'error'); return;
            }
            displayStatusMessage(`Creating Python strategy ${strategyName}...`, 'info');
            try {
                const response = await fetch('/create_python_strategy', {
                    method: 'POST', headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: strategyId, name: strategyName, description: strategyDescription, template: strategyTemplate })
                });
                const result = await response.json();
                if (response.ok) {
                    displayStatusMessage(result.message || 'Strategy created successfully', 'success');
                    newStrategyForm.style.display = 'none';
                    if (newStrategyIdInput) newStrategyIdInput.value = '';
                    if (newStrategyNameInput) newStrategyNameInput.value = '';
                    if (newStrategyDescriptionInput) newStrategyDescriptionInput.value = '';
                    if (newStrategyTemplateSelect) newStrategyTemplateSelect.selectedIndex = 0;
                    await loadAvailablePythonStrategies(); // Reload dropdown
                    if (pythonStrategySelect) { // Select and load the new strategy
                        pythonStrategySelect.value = strategyId;
                        loadPythonStrategyDetails(strategyId);
                    }
                } else { displayStatusMessage(result.error || 'Failed to create strategy', 'error'); }
            } catch (error) {
                console.error('Error creating Python strategy:', error);
                displayStatusMessage(`Error creating strategy: ${error.message}`, 'error');
            }
        });
    }
     if (deletePythonStrategyButton) { // Changed from deleteStrategyButton
        deletePythonStrategyButton.addEventListener('click', async () => {
            const strategyId = window.currentStrategyId; // Use the globally stored ID
            if (!strategyId) {
                displayStatusMessage('Please select a Python strategy to delete.', 'error'); return;
            }
            const strategyName = pythonStrategySelect.options[pythonStrategySelect.selectedIndex]?.text || strategyId;
            if (!confirm(`Are you sure you want to delete the Python strategy "${strategyName}"? This cannot be undone.`)) {
                return;
            }
            displayStatusMessage(`Deleting Python strategy ${strategyName}...`, 'info');
            try {
                const response = await fetch(`/delete_python_strategy/${strategyId}`, { method: 'DELETE' });
                const result = await response.json();
                if (response.ok) {
                    displayStatusMessage(result.message || `Strategy '${strategyName}' deleted`, 'success');
                    loadPythonStrategyDetails(null); // Clear editor fields
                    await loadAvailablePythonStrategies(); // Reload dropdown
                    await fetchAndPopulateStrategies(); // Refresh backtest/live dropdowns
                } else { displayStatusMessage(result.error || 'Failed to delete strategy', 'error'); }
            } catch (error) {
                console.error('Error deleting Python strategy:', error);
                displayStatusMessage(`Error deleting strategy: ${error.message}`, 'error');
            }
        });
    }
    if (savePythonStrategyButton) {
        savePythonStrategyButton.addEventListener('click', savePythonStrategy);
    }
    if (testPythonStrategyButton) {
        testPythonStrategyButton.addEventListener('click', testPythonStrategy);
    }

    // Live Trading Listeners (already defined globally with checks)

    // Optimization Listeners (already defined globally with checks)
    const optBtn = document.getElementById('optimize-strategy-button');
    if (optBtn) {
         optBtn.addEventListener('click', async () => {
            const pair = backtestPairSelect?.value;
            const startDate = startDateInput?.value;
            const endDate = endDateInput?.value;
            const strategyId = backtestStrategySelect?.value; // Assuming optimization uses backtest strategy

            if (!pair || !startDate || !endDate || !strategyId) {
                displayStatusMessage("Please select pair, date range, and strategy for optimization.", "error"); return;
            }
            if (optimizationResultsDiv) optimizationResultsDiv.innerHTML = '<p>Optimizing strategy parameters...</p>';
            displayStatusMessage("Starting strategy optimization...", "info");
            try {
                const response = await fetch('/strategies/optimize', {
                    method: 'POST', headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ pair: pair, strategy_id: strategyId, start_date: startDate, end_date: endDate })
                });
                const result = await response.json();
                if (response.ok) {
                    let html = '<h3>Optimization Results</h3>';
                    if (result.best_fitness !== undefined) html += `<p>Best Fitness Score: ${result.best_fitness.toFixed(4)}</p>`;
                    if (result.best_parameters && Object.keys(result.best_parameters).length > 0) {
                        html += '<h4>Best Parameters:</h4><ul>';
                        for (const [param, value] of Object.entries(result.best_parameters)) {
                            html += `<li>${param}: ${value}</li>`;
                            const inputElement = document.getElementById(param); // Try to update inputs
                            if (inputElement) inputElement.value = value;
                        }
                        html += '</ul>';
                    } else { html += '<p>No optimal parameters found.</p>'; }
                    // ... (rest of history display logic) ...
                    if (optimizationResultsDiv) optimizationResultsDiv.innerHTML = html;
                    displayStatusMessage("Strategy optimization completed.", "success");
                } else {
                    const errorMsg = result.error || "Optimization failed.";
                    if (optimizationResultsDiv) optimizationResultsDiv.innerHTML = `<p class="error">Error: ${errorMsg}</p>`;
                    displayStatusMessage(`Strategy optimization failed: ${errorMsg}`, "error");
                }
            } catch (error) {
                console.error("Error optimizing strategy:", error);
                if (optimizationResultsDiv) optimizationResultsDiv.innerHTML = `<p class="error">Error: ${error.message}. Check console.</p>`;
                displayStatusMessage(`Error optimizing strategy: ${error.message}`, "error");
            }
        });
    }

    // Auto Optimization Listeners (already defined globally with checks)
    const startAutoBtn = document.getElementById('start-auto-opt-button');
    const stopAutoBtn = document.getElementById('stop-auto-opt-button');
    if (startAutoBtn) startAutoBtn.addEventListener('click', startAutoOptimization);
    if (stopAutoBtn) stopAutoBtn.addEventListener('click', stopAutoOptimization);


    // --- Final Setup ---
    // Set default active tab (e.g., Configuration) by simulating a click
    const defaultTabButton = document.querySelector('.tab-button'); // Get the first tab button
    if (defaultTabButton) {
        const onclickAttr = defaultTabButton.getAttribute('onclick');
        const tabNameMatch = onclickAttr?.match(/openTab\(event,\s*'([^']+)'\)/);
        if (tabNameMatch && tabNameMatch[1]) {
            openTab({ currentTarget: defaultTabButton }, tabNameMatch[1]); // Pass a mock event object
        } else {
             console.warn("Could not determine default tab name from first button. Manually setting to 'Configuration'.");
             // Fallback: explicitly open a known default tab if possible
             const configButton = Array.from(document.querySelectorAll('.tab-button')).find(btn => btn.textContent.includes('Configuration'));
             if (configButton) {
                 openTab({ currentTarget: configButton }, 'Configuration');
             } else if (defaultTabButton) {
                 // If we can't find 'Configuration', just activate the first button found
                 defaultTabButton.classList.add('active');
                 // Try to guess the tab ID from the button's potential 'data-tab' attribute or similar
                 const potentialTabId = defaultTabButton.dataset.tab || 'Configuration'; // Default guess
                 const firstTabContent = document.getElementById(potentialTabId);
                 if (firstTabContent) firstTabContent.style.display = 'block';
             }
        }
    }

    console.log('Initial setup and event listeners attached.');
}); // End Main DOMContentLoaded
</final_file_content>

IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.<environment_details>
# VSCode Visible Files
tradingbot/static/js/script.js

# VSCode Open Tabs
tradingbot/templates/index.html
tradingbot/app.py
tradingbot/static/js/charts.js
tradingbot/static/js/script.js
../../Library/Application Support/Code/User/workspaceStorage/bcf2b3604877967d1ea7db6265fe329f/Augment.vscode-augment/Augment-Memories

# Current Time
18-4-2025, 2:50:24 p.m. (Europe/Amsterdam, UTC+2:00)

# Context Window Usage
126.414 / 1.048,576K tokens used (12%)

# Current Mode
ACT MODE
</environment_details>
