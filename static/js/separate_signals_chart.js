// Separate signals chart script
console.log('Separate signals chart script loaded');

// Function to create a separate chart for signals
function createSeparateSignalsChart(signals, historicalData) {
    console.log('Creating separate signals chart with', signals.length, 'signals');

    if (!signals || signals.length === 0 || !historicalData || historicalData.length === 0) {
        console.log('No signals or historical data to display');
        return;
    }

    // Get the existing container and title
    const signalsChartContainer = document.getElementById('signals-chart-container');
    const signalsTitle = document.getElementById('signals-title');

    if (!signalsChartContainer) {
        console.error('Signals chart container not found in the DOM');
        return;
    }

    // Make the container and title visible
    signalsChartContainer.style.display = 'block';
    if (signalsTitle) {
        signalsTitle.style.display = 'block';
    }

    // Clear existing canvas if needed
    signalsChartContainer.innerHTML = '';

    // Create canvas for the chart
    const canvas = document.createElement('canvas');
    canvas.id = 'signals-chart';
    signalsChartContainer.appendChild(canvas);

    console.log('Prepared signals chart container and canvas');

    // Prepare data for the chart
    const priceData = [];
    const buySignals = [];
    const sellSignals = [];

    // Process historical data for price line
    historicalData.forEach(dataPoint => {
        priceData.push({
            x: new Date(dataPoint.timestamp),
            y: dataPoint.close
        });
    });

    // Process signals
    signals.forEach(signal => {
        const point = {
            x: new Date(parseInt(signal.timestamp)),
            y: signal.price
        };

        if (signal.type === 'buy') {
            buySignals.push(point);
        } else if (signal.type === 'sell') {
            sellSignals.push(point);
        }
    });

    console.log('Processed price data points:', priceData.length);
    console.log('Processed buy signals:', buySignals.length);
    console.log('Processed sell signals:', sellSignals.length);

    // Get min and max values for the chart
    const allPrices = priceData.map(point => point.y);
    const minPrice = Math.min(...allPrices) * 0.99; // Add some padding
    const maxPrice = Math.max(...allPrices) * 1.01; // Add some padding

    const allDates = priceData.map(point => point.x);
    const minDate = new Date(Math.min(...allDates.map(d => d.getTime())));
    const maxDate = new Date(Math.max(...allDates.map(d => d.getTime())));

    // Create the chart
    const ctx = document.getElementById('signals-chart').getContext('2d');

    // Create the chart
    const signalsChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [
                {
                    label: 'Price',
                    data: priceData,
                    borderColor: 'rgba(150, 150, 150, 0.8)',
                    backgroundColor: 'rgba(150, 150, 150, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    fill: false,
                    tension: 0.1,
                    yAxisID: 'y'
                },
                {
                    label: 'Buy Signals',
                    data: buySignals,
                    backgroundColor: '#00FF00',  // Bright green
                    borderColor: '#FFFFFF',      // White border
                    pointBackgroundColor: '#00FF00',  // Ensure points are green
                    pointRadius: 10,             // Large points
                    pointStyle: 'circle',
                    borderWidth: 2,
                    hoverRadius: 15,
                    hoverBorderWidth: 3,
                    fill: false,
                    showLine: false,
                    yAxisID: 'y'
                },
                {
                    label: 'Sell Signals',
                    data: sellSignals,
                    backgroundColor: '#FF0000',  // Bright red
                    borderColor: '#FFFFFF',      // White border
                    pointBackgroundColor: '#FF0000',  // Ensure points are red
                    pointRadius: 10,             // Large points
                    pointStyle: 'circle',
                    borderWidth: 2,
                    hoverRadius: 15,
                    hoverBorderWidth: 3,
                    fill: false,
                    showLine: false,
                    yAxisID: 'y'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const point = context.raw;
                            if (context.dataset.label === 'Buy Signals') {
                                return `Buy at $${point.y.toLocaleString()}`;
                            } else if (context.dataset.label === 'Sell Signals') {
                                return `Sell at $${point.y.toLocaleString()}`;
                            }
                            return `Price: $${point.y.toLocaleString()}`;
                        }
                    }
                },
                legend: {
                    labels: {
                        color: window.isDarkMode ? '#ffffff' : '#333333'
                    }
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day',
                        displayFormats: {
                            day: 'MMM dd'
                        }
                    },
                    min: minDate,
                    max: maxDate,
                    ticks: {
                        color: window.isDarkMode ? '#ffffff' : '#333333'
                    },
                    grid: {
                        color: window.isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    min: minPrice,
                    max: maxPrice,
                    ticks: {
                        color: window.isDarkMode ? '#ffffff' : '#333333',
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    },
                    grid: {
                        color: window.isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });

    console.log('Signals chart created successfully');

    // Store the chart in the window object for later reference
    window.signalsChart = signalsChart;
}

// Export function
window.createSeparateSignalsChart = createSeparateSignalsChart;
