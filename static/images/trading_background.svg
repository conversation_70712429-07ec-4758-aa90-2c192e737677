<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0f172a" />
      <stop offset="100%" stop-color="#1e293b" />
    </linearGradient>
    <filter id="noise" x="0%" y="0%" width="100%" height="100%">
      <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
      <feBlend mode="overlay" in="SourceGraphic" />
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect width="100%" height="100%" fill="url(#bgGradient)" />
  
  <!-- Noise overlay -->
  <rect width="100%" height="100%" filter="url(#noise)" opacity="0.4" />
  
  <!-- Grid lines -->
  <g stroke="#334155" stroke-width="0.5" opacity="0.2">
    <!-- Horizontal grid lines -->
    <line x1="0" y1="100" x2="1920" y2="100" />
    <line x1="0" y1="200" x2="1920" y2="200" />
    <line x1="0" y1="300" x2="1920" y2="300" />
    <line x1="0" y1="400" x2="1920" y2="400" />
    <line x1="0" y1="500" x2="1920" y2="500" />
    <line x1="0" y1="600" x2="1920" y2="600" />
    <line x1="0" y1="700" x2="1920" y2="700" />
    <line x1="0" y1="800" x2="1920" y2="800" />
    <line x1="0" y1="900" x2="1920" y2="900" />
    <line x1="0" y1="1000" x2="1920" y2="1000" />
    
    <!-- Vertical grid lines -->
    <line x1="100" y1="0" x2="100" y2="1080" />
    <line x1="200" y1="0" x2="200" y2="1080" />
    <line x1="300" y1="0" x2="300" y2="1080" />
    <line x1="400" y1="0" x2="400" y2="1080" />
    <line x1="500" y1="0" x2="500" y2="1080" />
    <line x1="600" y1="0" x2="600" y2="1080" />
    <line x1="700" y1="0" x2="700" y2="1080" />
    <line x1="800" y1="0" x2="800" y2="1080" />
    <line x1="900" y1="0" x2="900" y2="1080" />
    <line x1="1000" y1="0" x2="1000" y2="1080" />
    <line x1="1100" y1="0" x2="1100" y2="1080" />
    <line x1="1200" y1="0" x2="1200" y2="1080" />
    <line x1="1300" y1="0" x2="1300" y2="1080" />
    <line x1="1400" y1="0" x2="1400" y2="1080" />
    <line x1="1500" y1="0" x2="1500" y2="1080" />
    <line x1="1600" y1="0" x2="1600" y2="1080" />
    <line x1="1700" y1="0" x2="1700" y2="1080" />
    <line x1="1800" y1="0" x2="1800" y2="1080" />
  </g>
  
  <!-- Candlestick chart pattern -->
  <g transform="translate(200, 300)" stroke-linecap="round">
    <!-- Stylized candlestick chart -->
    <g opacity="0.15">
      <!-- Green candles -->
      <rect x="50" y="100" width="20" height="60" fill="#10b981" />
      <line x1="60" y1="80" x2="60" y2="100" stroke="#10b981" stroke-width="2" />
      <line x1="60" y1="160" x2="60" y2="180" stroke="#10b981" stroke-width="2" />
      
      <rect x="130" y="120" width="20" height="80" fill="#10b981" />
      <line x1="140" y1="100" x2="140" y2="120" stroke="#10b981" stroke-width="2" />
      <line x1="140" y1="200" x2="140" y2="220" stroke="#10b981" stroke-width="2" />
      
      <rect x="290" y="90" width="20" height="70" fill="#10b981" />
      <line x1="300" y1="70" x2="300" y2="90" stroke="#10b981" stroke-width="2" />
      <line x1="300" y1="160" x2="300" y2="180" stroke="#10b981" stroke-width="2" />
      
      <rect x="370" y="60" width="20" height="90" fill="#10b981" />
      <line x1="380" y1="40" x2="380" y2="60" stroke="#10b981" stroke-width="2" />
      <line x1="380" y1="150" x2="380" y2="170" stroke="#10b981" stroke-width="2" />
      
      <rect x="450" y="40" width="20" height="60" fill="#10b981" />
      <line x1="460" y1="20" x2="460" y2="40" stroke="#10b981" stroke-width="2" />
      <line x1="460" y1="100" x2="460" y2="120" stroke="#10b981" stroke-width="2" />
      
      <rect x="610" y="80" width="20" height="70" fill="#10b981" />
      <line x1="620" y1="60" x2="620" y2="80" stroke="#10b981" stroke-width="2" />
      <line x1="620" y1="150" x2="620" y2="170" stroke="#10b981" stroke-width="2" />
      
      <!-- Red candles -->
      <rect x="90" y="140" width="20" height="50" fill="#ef4444" />
      <line x1="100" y1="120" x2="100" y2="140" stroke="#ef4444" stroke-width="2" />
      <line x1="100" y1="190" x2="100" y2="210" stroke="#ef4444" stroke-width="2" />
      
      <rect x="170" y="160" width="20" height="40" fill="#ef4444" />
      <line x1="180" y1="140" x2="180" y2="160" stroke="#ef4444" stroke-width="2" />
      <line x1="180" y1="200" x2="180" y2="220" stroke="#ef4444" stroke-width="2" />
      
      <rect x="210" y="180" width="20" height="60" fill="#ef4444" />
      <line x1="220" y1="160" x2="220" y2="180" stroke="#ef4444" stroke-width="2" />
      <line x1="220" y1="240" x2="220" y2="260" stroke="#ef4444" stroke-width="2" />
      
      <rect x="250" y="150" width="20" height="40" fill="#ef4444" />
      <line x1="260" y1="130" x2="260" y2="150" stroke="#ef4444" stroke-width="2" />
      <line x1="260" y1="190" x2="260" y2="210" stroke="#ef4444" stroke-width="2" />
      
      <rect x="330" y="120" width="20" height="50" fill="#ef4444" />
      <line x1="340" y1="100" x2="340" y2="120" stroke="#ef4444" stroke-width="2" />
      <line x1="340" y1="170" x2="340" y2="190" stroke="#ef4444" stroke-width="2" />
      
      <rect x="410" y="100" width="20" height="60" fill="#ef4444" />
      <line x1="420" y1="80" x2="420" y2="100" stroke="#ef4444" stroke-width="2" />
      <line x1="420" y1="160" x2="420" y2="180" stroke="#ef4444" stroke-width="2" />
      
      <rect x="490" y="80" width="20" height="70" fill="#ef4444" />
      <line x1="500" y1="60" x2="500" y2="80" stroke="#ef4444" stroke-width="2" />
      <line x1="500" y1="150" x2="500" y2="170" stroke="#ef4444" stroke-width="2" />
      
      <rect x="530" y="110" width="20" height="50" fill="#ef4444" />
      <line x1="540" y1="90" x2="540" y2="110" stroke="#ef4444" stroke-width="2" />
      <line x1="540" y1="160" x2="540" y2="180" stroke="#ef4444" stroke-width="2" />
      
      <rect x="570" y="130" width="20" height="40" fill="#ef4444" />
      <line x1="580" y1="110" x2="580" y2="130" stroke="#ef4444" stroke-width="2" />
      <line x1="580" y1="170" x2="580" y2="190" stroke="#ef4444" stroke-width="2" />
    </g>
  </g>
  
  <!-- Duplicate the candlestick pattern in other areas with different opacities -->
  <g transform="translate(800, 500)" opacity="0.1">
    <use href="#candlestick-pattern" />
  </g>
  
  <g transform="translate(1200, 200)" opacity="0.08">
    <use href="#candlestick-pattern" />
  </g>
  
  <!-- Line chart pattern -->
  <path d="M0,800 Q100,750 200,780 T400,720 T600,750 T800,700 T1000,730 T1200,680 T1400,710 T1600,670 T1800,690 T2000,650" 
        fill="none" stroke="#3b82f6" stroke-width="2" opacity="0.1" />
  
  <!-- Dots for data points -->
  <g fill="#60a5fa" opacity="0.2">
    <circle cx="200" cy="780" r="3" />
    <circle cx="400" cy="720" r="3" />
    <circle cx="600" cy="750" r="3" />
    <circle cx="800" cy="700" r="3" />
    <circle cx="1000" cy="730" r="3" />
    <circle cx="1200" cy="680" r="3" />
    <circle cx="1400" cy="710" r="3" />
    <circle cx="1600" cy="670" r="3" />
    <circle cx="1800" cy="690" r="3" />
  </g>
</svg>
