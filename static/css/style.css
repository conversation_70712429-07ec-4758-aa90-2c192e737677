/* --- Color Variables --- */
:root {
    /* Light mode (default) */
    --bg-color: rgba(244, 244, 244, 0.7);
    --text-color: #333;
    --header-color: #333;
    --border-color: #ccc;
    --tab-bg: rgba(255, 255, 255, 0.7);
    --tab-text: #333;
    --tab-hover-bg: rgba(221, 221, 221, 0.9);
    --tab-active-border: #007bff;
    --tab-active-text: #007bff;
    --content-bg: rgba(255, 255, 255, 0.8);
    --input-bg: rgba(255, 255, 255, 0.9);
    --input-border: #ccc;
    --input-text: #000;
    --button-bg: #007bff;
    --button-text: white;
    --button-hover-bg: #0056b3;
    --status-success-bg: rgba(212, 237, 218, 0.9);
    --status-success-text: #155724;
    --status-success-border: #c3e6cb;
    --status-error-bg: rgba(248, 215, 218, 0.9);
    --status-error-text: #721c24;
    --status-error-border: #f5c6cb;
    --pre-bg: rgba(233, 233, 233, 0.9);
    --pre-border: #dcdcdc;
    --chart-bg: rgba(255, 255, 255, 0.8);
    --chart-border: #ddd;
    --chart-line: rgb(75, 192, 192);
    --chart-text: #666; /* Default axis/label color */
    --card-bg: rgba(255, 255, 255, 0.8);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --profit-color: #28a745;
    --loss-color: #dc3545;
}

/* Dark mode */
[data-theme="dark"] {
    --bg-color: rgba(18, 18, 18, 0.7);
    --text-color: #e0e0e0;
    --header-color: #f0f0f0;
    --border-color: #444;
    --tab-bg: rgba(30, 30, 30, 0.8);
    --tab-text: #e0e0e0;
    --tab-hover-bg: rgba(51, 51, 51, 0.9);
    --tab-active-border: #0d6efd;
    --tab-active-text: #0d6efd;
    --content-bg: rgba(30, 30, 30, 0.8);
    --input-bg: rgba(45, 45, 45, 0.9);
    --input-border: #444;
    --input-text: #e0e0e0;
    --profit-color: #4caf50;
    --loss-color: #f44336;
    --button-bg: #0d6efd;
    --button-text: white;
    --button-hover-bg: #0b5ed7;
    --status-success-bg: rgba(13, 54, 34, 0.9);
    --status-success-text: #75b798;
    --status-success-border: #0f5132;
    --status-error-bg: rgba(44, 11, 14, 0.9);
    --status-error-text: #ea868f;
    --status-error-border: #842029;
    --pre-bg: rgba(45, 45, 45, 0.9);
    --pre-border: #444;
    --chart-bg: rgba(30, 30, 30, 0.8);
    --chart-border: #444;
    --chart-line: rgb(75, 192, 192);
    --chart-text: #aaa;
    --card-bg: rgba(45, 45, 45, 0.8);
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Theme toggle switch styling */
#theme-toggle {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 8px 12px;
    background-color: var(--button-bg);
    color: var(--button-text);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

#theme-toggle:hover {
    background-color: var(--button-hover-bg);
}

/* Base styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--bg-color);
    background-image: url('../images/trading_background.svg');
    background-size: cover;
    background-attachment: fixed;
    background-position: center;
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--header-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Tab Navigation */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.tab-button {
    background-color: var(--tab-bg);
    color: var(--tab-text);
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
    text-decoration: none;
    margin-right: 5px;
    border-radius: 5px 5px 0 0;
}

.tab-button:hover {
    background-color: var(--tab-hover-bg);
}

.tab-button.active {
    border-bottom: 3px solid var(--tab-active-border);
    color: var(--tab-active-text);
    font-weight: bold;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 20px;
    background-color: var(--content-bg);
    border-radius: 0 0 5px 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
}

/* Form styling */
input, select, textarea {
    width: 100%;
    padding: 8px;
    margin: 5px 0 15px 0;
    display: inline-block;
    border: 1px solid var(--input-border);
    border-radius: 4px;
    box-sizing: border-box;
    background-color: var(--input-bg);
    color: var(--input-text);
}

label {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
}

.form-group {
    margin-bottom: 15px;
}

/* Button styling */
.button, button {
    background-color: var(--button-bg);
    color: var(--button-text);
    padding: 10px 15px;
    margin: 5px 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
    text-decoration: none;
    display: inline-block;
}

.button:hover, button:hover {
    background-color: var(--button-hover-bg);
}

.button-group {
    margin: 10px 0;
}

/* Strategy editor styling */
#strategy-editor {
    font-family: monospace;
    width: 100%;
    height: 300px;
    background-color: var(--input-bg);
    color: var(--input-text);
    border: 1px solid var(--input-border);
}

/* Status message styling */
#config-status,
#backtest-results pre,
#live-status p,
.status-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    color: var(--text-color); /* Default status text color */
}

/* Status message specific styling */
.status-message {
    font-weight: bold;
    transition: all 0.3s ease;
    display: none; /* Hidden by default until populated */
}

#backtest-results pre {
    background-color: var(--pre-bg);
    border: 1px solid var(--pre-border);
    white-space: pre-wrap; /* Wrap long lines */
    word-wrap: break-word;
    color: var(--text-color); /* Ensure pre text color adapts */
}

.status-message.success, .alert-success {
    background-color: var(--status-success-bg);
    color: var(--status-success-text);
    border: 1px solid var(--status-success-border);
}

.status-message.error, .alert-error {
    background-color: var(--status-error-bg);
    color: var(--status-error-text);
    border: 1px solid var(--status-error-border);
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.alert {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    display: block;
}

/* Results section styling */
.results-section {
    margin-top: 20px;
    padding: 15px;
    background-color: var(--content-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.results-section h3 {
    margin-top: 0;
    color: var(--header-color);
}

/* Auto-optimization section styling */
.auto-optimization-section {
    margin-top: 20px;
    padding: 15px;
    background-color: var(--content-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.status-section {
    margin-top: 10px;
    padding: 10px;
    background-color: var(--content-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

/* Button styling for specific actions */
#start-auto-opt-button {
    background-color: #28a745;
    color: white;
}

#start-auto-opt-button:hover {
    background-color: #218838;
}

#stop-auto-opt-button {
    background-color: #f44336;
    color: white;
}

/* Progress bar styling */
.progress-bar-container {
    width: 100%;
    height: 20px;
    background-color: #333;
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #4CAF50;
    width: 0%;
    transition: width 0.3s ease;
}

#download-progress {
    margin: 15px 0;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

/* Backtesting layout */
.backtesting-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin-top: 20px;
}

.backtesting-controls {
    flex: 0 0 300px;
    padding: 15px;
    background-color: var(--content-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.backtesting-chart-panel {
    flex: 1;
    min-height: 800px; /* Increased height to accommodate trade table */
    background-color: var(--content-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px var(--shadow-color);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow-y: auto; /* Allow scrolling for large trade tables */
    backdrop-filter: blur(10px);
}



.chart-container {
    flex: 1;
    min-height: 500px;
    margin-bottom: 20px;
}

#signals-chart {
    width: 100%;
    height: 150px;
    margin-top: 10px;
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--content-bg);
}

.trade-results-container {
    margin-top: 10px;
    max-height: calc(100vh - 250px);
    overflow-y: auto;
    border-top: 1px solid var(--border-color);
    padding-top: 10px;
    width: 100%;
}

/* Trade Table Styles */
.trade-table-container {
    margin-top: 20px;
    overflow-x: auto;
    width: 100%;
}

.trade-summary {
    margin-top: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: var(--content-bg);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9em;
}

.trade-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 5px;
    font-size: 0.85em;
    table-layout: fixed;
}

.trade-table th, .trade-table td {
    padding: 6px 8px;
    text-align: right;
    border: 1px solid var(--border-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.trade-table th {
    background-color: var(--header-bg);
    color: var(--header-text);
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}



.trade-table tr:nth-child(even) {
    background-color: var(--tab-bg);
}

.trade-table tr:hover {
    background-color: var(--tab-hover-bg);
}

.trade-table .profit {
    color: #28a745;
}

.trade-table .loss {
    color: #dc3545;
}

.backtest-summary {
    white-space: pre-line;
    margin-bottom: 20px;
    line-height: 1.5;
}

.control-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.control-section h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: var(--header-color);
}

.form-row {
    display: flex;
    gap: 10px;
}

.form-group.compact {
    margin-bottom: 10px;
}

.compact-input {
    padding: 6px;
    font-size: 14px;
}

.results-section.compact {
    max-height: 200px;
    overflow-y: auto;
    font-size: 12px;
}

.results-section.compact pre {
    margin: 0;
    white-space: pre-wrap;
    font-size: 12px;
}

/* Chart container styling */
.chart-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

#price-chart {
    width: 100%;
    height: 400px !important;
    margin-bottom: 10px;
    background-color: var(--chart-bg);
    border: 1px solid var(--chart-border);
    border-radius: 4px;
}

#volume-chart {
    width: 100%;
    height: 150px !important;
    background-color: var(--chart-bg);
    border: 1px solid var(--chart-border);
    border-radius: 4px;
}

/* Strategy editor styling */
.strategy-editor-panel {
    margin-top: 20px;
}

.strategy-selection {
    margin-bottom: 20px;
}

.strategy-content {
    background-color: var(--chart-bg);
    border: 1px solid var(--chart-border);
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

.strategy-section {
    margin-bottom: 20px;
}

.code-display {
    background-color: var(--chart-bg);
    border: 1px solid var(--chart-border);
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    white-space: pre-wrap;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
    display: block;
    width: 100%;
    box-sizing: border-box;
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.5;
}

.code-editor {
    background-color: var(--chart-bg);
    border: 1px solid var(--chart-border);
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    white-space: pre-wrap;
    width: 100%;
    box-sizing: border-box;
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
}

.editor-buttons {
    margin-top: 10px;
    margin-bottom: 10px;
}

.test-results {
    margin-top: 10px;
    border: 1px solid var(--chart-border);
    border-radius: 4px;
    padding: 10px;
    min-height: 50px;
    background-color: var(--chart-bg);
    white-space: pre-wrap;
}

/* Add styles for the trade table */
.trade-table-container {
    margin-top: 20px;
    overflow-x: auto;
    background-color: var(--content-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color);
    padding: 15px;
}

.trade-table-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--header-color);
    font-size: 1.2rem;
}

.trade-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.trade-table th, .trade-table td {
    padding: 10px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.trade-table th {
    background-color: var(--tab-hover-bg);
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.trade-table tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.trade-table .profit {
    color: var(--profit-color);
    font-weight: bold;
}

.trade-table .loss {
    color: var(--loss-color);
    font-weight: bold;
}

/* Trade summary styles */
.trade-summary {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--tab-bg);
    border-radius: 5px;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.trade-summary h3 {
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
    color: var(--header-color);
}

.summary-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.summary-table td {
    padding: 5px 10px;
    border-bottom: 1px solid var(--border-color);
}

.summary-table td:first-child {
    font-weight: bold;
    width: 40%;
}

/* Trade table styles */
.trade-table-container {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--tab-bg);
    border-radius: 5px;
    box-shadow: 0 2px 5px var(--shadow-color);
    overflow-x: auto;
}

.trade-table-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
    color: var(--header-color);
}

.trade-table {
    width: 100%;
    border-collapse: collapse;
}

.trade-table th {
    background-color: var(--tab-hover-bg);
    color: var(--header-color);
    font-weight: bold;
    padding: 10px;
    text-align: left;
    position: sticky;
    top: 0;
    z-index: 10;
}

.trade-table td {
    padding: 8px 10px;
    border-bottom: 1px solid var(--border-color);
}

.trade-table .profit {
    color: var(--profit-color);
}

.trade-table .loss {
    color: var(--loss-color);
}

.trade-table tr.profit-row {
    background-color: rgba(76, 175, 80, 0.1);
}

.trade-table tr.loss-row {
    background-color: rgba(244, 67, 54, 0.1);
}
