from flask import Flask, render_template, request, jsonify, session, Response, flash, redirect, url_for, send_from_directory # Import Response
import os
import json # json is already imported, ensure it stays
import subprocess
import signal
from datetime import datetime, timezone, timedelta
import sys # To get python executable path
import re # For sanitizing filenames
import atexit
import time
import traceback
import threading
import queue
import glob # For finding pine script files

# Import functions from our API module
from hyperliquid_api import fetch_trading_pairs, test_api_credentials, fetch_historical_data
# Import the backtester functions
from backtester import run_backtest, simulate_strategy
from strategy_developer import optimize_strategy
# Import the improved Strategy-bot
from improved_strategy_bot import start_strategy_bot, stop_strategy_bot, get_strategy_bot_status, get_strategy_bot_results, reset_strategy_bot_results
from strategies import STRATEGY_PARAMS, STRATEGY_DESCRIPTIONS

app = Flask(__name__)
app.secret_key = os.urandom(24) # For session management, if needed later

# --- Configuration ---
# WARNING: Storing private keys in configuration files is highly insecure!
# Use environment variables or a secure secrets management solution in production.
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
STRATEGIES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategies')
PINE_STRATEGIES_DIR = os.path.join(STRATEGIES_DIR, 'pine_examples')

# Create the pine_examples directory if it doesn't exist
os.makedirs(PINE_STRATEGIES_DIR, exist_ok=True)

# Function to get all available Pine Script strategies
def get_pine_strategies():
    pine_files = glob.glob(os.path.join(PINE_STRATEGIES_DIR, '*.pine'))
    strategies = []

    for file_path in pine_files:
        strategy_name = os.path.basename(file_path).replace('.pine', '')
        strategies.append({
            'id': f'pine_{strategy_name}',
            'name': strategy_name.replace('_', ' ').title(),
            'file_path': file_path
        })

    return strategies
# Default configuration structure
APP_CONFIG = {
    'credentials': {'wallet_address': '', 'private_key': ''},
    'trading': {
        'pair': '',
        'strategy': '',
        'capital': 100.0, # Default value
        'take_profit': 5.0, # Default value (%)
        'stop_loss': 2.0 # Default value (%)
    }
}

def load_config():
    """Loads configuration from config.json, merging with defaults."""
    global APP_CONFIG
    print(f"Loading configuration from {CONFIG_FILE}")
    default_config = { # Define defaults clearly
        'credentials': {'wallet_address': '', 'private_key': ''},
        'trading': {
            'pair': '',
            'strategy': '',
            'capital': 100.0,
            'take_profit': 5.0,
            'stop_loss': 2.0
        }
    }
    if os.path.exists(CONFIG_FILE):
        try:
            print(f"Found existing config file at {CONFIG_FILE}")
            with open(CONFIG_FILE, 'r') as f:
                loaded_config = json.load(f)
                print(f"Loaded config: {json.dumps(loaded_config, indent=2)}")
                # Merge loaded config with defaults to ensure all keys exist
                APP_CONFIG['credentials'] = loaded_config.get('credentials', default_config['credentials'])
                APP_CONFIG['trading'] = loaded_config.get('trading', default_config['trading'])

                # Handle potential old format (only credentials or old api_keys)
                if 'api_keys' in loaded_config: # Check old top-level key
                     print("Found old 'api_keys' format, migrating to 'credentials'.")
                     old_keys = loaded_config.get('api_keys', {})
                     APP_CONFIG['credentials']['wallet_address'] = old_keys.get('public', '')
                     APP_CONFIG['credentials']['private_key'] = old_keys.get('secret', '')
                     # Keep existing trading config if present, otherwise use defaults
                     APP_CONFIG['trading'] = loaded_config.get('trading', default_config['trading'])
                     save_config() # Save in new format immediately
                elif 'credentials' in loaded_config and 'trading' not in loaded_config:
                     # Handle case where only credentials exist (new format but no trading section)
                     print("Found credentials but no trading config. Adding default trading config.")
                     APP_CONFIG['trading'] = default_config['trading']
                     save_config() # Save updated structure

        except json.JSONDecodeError as e:
            print(f"Error reading {CONFIG_FILE}: {e}")
            print("Using default configuration.")
            APP_CONFIG = default_config # Reset to defaults on error
    else:
        print(f"{CONFIG_FILE} not found. Using default configuration.")
        APP_CONFIG = default_config # Use defaults if file doesn't exist
    print(f"Final loaded config: {json.dumps(APP_CONFIG, indent=2)}")
    return APP_CONFIG['credentials']

def save_config():
    """Saves the current APP_CONFIG to config.json."""
    # WARNING: Saving private key to a file is insecure.
    print(f"Saving configuration to {CONFIG_FILE}")
    print(f"Config to save: {json.dumps(APP_CONFIG, indent=2)}")
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(APP_CONFIG, f, indent=4)
        print("Configuration saved successfully")
    except Exception as e:
        print(f"Error saving configuration: {e}")
        raise

# Ensure strategies directory exists
os.makedirs(STRATEGIES_DIR, exist_ok=True)

# Load configuration at startup
load_config()

# Add these global variables at the top with other globals
optimization_thread = None
optimization_queue = queue.Queue()
optimization_running = False
optimization_results = []

# Strategy-bot routes
@app.route('/strategy_bot')
def strategy_bot_page():
    """Renders the Strategy-bot page."""
    return render_template('strategy_bot.html')

@app.route('/strategy_bot/start', methods=['POST'])
def strategy_bot_start():
    """Start the Strategy-bot."""
    data = request.json
    if not data:
        return jsonify({'status': 'error', 'message': 'No data provided'}), 400

    pair = data.get('pair', 'BTC-USD')
    strategy = data.get('strategy', 'master_strategy')  # Default to master_strategy
    iterations = int(data.get('iterations', 20))

    # Get date range for backtesting
    start_date = data.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
    end_date = data.get('end_date', datetime.now().strftime('%Y-%m-%d'))

    # Update master strategy parameters
    from strategies import STRATEGY_PARAMS
    if 'master_strategy' in STRATEGY_PARAMS:
        # Strategy selection
        STRATEGY_PARAMS['master_strategy']['use_sma'] = data.get('use_sma', True)
        STRATEGY_PARAMS['master_strategy']['use_rsi'] = data.get('use_rsi', True)
        STRATEGY_PARAMS['master_strategy']['use_macd'] = data.get('use_macd', True)
        STRATEGY_PARAMS['master_strategy']['use_bollinger'] = data.get('use_bollinger', True)
        STRATEGY_PARAMS['master_strategy']['use_stochastic'] = data.get('use_stochastic', False)
        STRATEGY_PARAMS['master_strategy']['use_volume'] = data.get('use_volume', False)

        # Strategy dependencies
        STRATEGY_PARAMS['master_strategy']['sma_depends_on_volume'] = data.get('sma_depends_on_volume', 0.0)
        STRATEGY_PARAMS['master_strategy']['rsi_depends_on_sma'] = data.get('rsi_depends_on_sma', 0.0)
        STRATEGY_PARAMS['master_strategy']['macd_depends_on_rsi'] = data.get('macd_depends_on_rsi', 0.0)
        STRATEGY_PARAMS['master_strategy']['bollinger_depends_on_volume'] = data.get('bollinger_depends_on_volume', 0.0)

        # Signal thresholds
        STRATEGY_PARAMS['master_strategy']['threshold_buy'] = data.get('threshold_buy', 0.3)
        STRATEGY_PARAMS['master_strategy']['threshold_sell'] = data.get('threshold_sell', 0.3)

        # Risk management
        STRATEGY_PARAMS['master_strategy']['take_profit'] = data.get('take_profit', 5.0)
        STRATEGY_PARAMS['master_strategy']['stop_loss'] = data.get('stop_loss', 2.0)

        print(f"Updated master strategy parameters: {STRATEGY_PARAMS['master_strategy']}")

    # Start the Strategy-bot with the improved implementation
    result = start_strategy_bot(pair, start_date, end_date, iterations, strategy)
    return jsonify(result)

@app.route('/strategy_bot/stop', methods=['GET', 'POST'])
def strategy_bot_stop():
    """Stop the Strategy-bot."""
    result = stop_strategy_bot()
    return jsonify(result)

@app.route('/strategy_bot/status')
def strategy_bot_status():
    """Get the current status of the Strategy-bot."""
    result = get_strategy_bot_status()

    # Save the status to a file so it can be retrieved even after a page refresh
    try:
        with open('strategy_bot_status.json', 'w') as f:
            json.dump(result, f)
    except Exception as e:
        print(f"Error saving strategy bot status: {e}")

    return jsonify(result)

@app.route('/strategy_bot/results')
def strategy_bot_results():
    """Get all Strategy-bot results."""
    result = get_strategy_bot_results()
    return jsonify(result)

@app.route('/strategy_bot/reset', methods=['GET', 'POST'])
def strategy_bot_reset():
    """Reset all Strategy-bot results."""
    result = reset_strategy_bot_results()
    return jsonify(result)

@app.route('/strategy_bot/reset_all_versions', methods=['POST'])
def reset_all_strategy_versions():
    """Reset all Strategy-bot results and delete all optimized strategy versions."""
    result = reset_strategy_bot_results()
    return jsonify(result)

@app.route('/strategy_bot/save_strategy', methods=['POST'])
def save_strategy_bot_strategy():
    """Save a strategy from the Strategy-bot as a new strategy."""
    data = request.json
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    strategy_id = data.get('strategy_id')
    new_name = data.get('new_name')
    parameters = data.get('parameters')

    if not strategy_id:
        return jsonify({'error': 'No strategy ID provided'}), 400

    if not new_name:
        return jsonify({'error': 'No new name provided'}), 400

    if not parameters:
        return jsonify({'error': 'No parameters provided'}), 400

    # Create a valid Python identifier from the new name
    import re
    new_strategy_id = re.sub(r'\W+', '_', new_name.lower())

    # Check if strategy already exists
    from strategies import STRATEGY_PARAMS
    if new_strategy_id in STRATEGY_PARAMS:
        return jsonify({'error': f'Strategy {new_strategy_id} already exists'}), 400

    try:
        # Get the original strategy code
        import inspect
        import strategies
        import importlib

        # Try to get the original strategy function
        original_strategy_func = None
        base_strategy_id = strategy_id

        # Extract the base strategy name from optimized_X_Y format
        if strategy_id.startswith('optimized_'):
            parts = strategy_id.split('_')
            if len(parts) > 2:
                base_strategy_id = '_'.join(parts[1:-1])  # Remove 'optimized_' prefix and timestamp

        if hasattr(strategies, base_strategy_id):
            original_strategy_func = getattr(strategies, base_strategy_id)
        else:
            return jsonify({'error': f'Base strategy {base_strategy_id} not found'}), 404

        # Get the original strategy code
        original_code = inspect.getsource(original_strategy_func)

        # Create the new strategy code by replacing the function name and parameters
        new_code = original_code.replace(f'def {base_strategy_id}', f'def {new_strategy_id}')

        # Add the strategy to the strategies.py file
        strategies_file = inspect.getfile(strategies)

        # Add the new strategy to the end of the file
        with open(strategies_file, 'a') as f:
            f.write('\n\n' + new_code)

        # Add the strategy to STRATEGY_PARAMS and STRATEGY_DESCRIPTIONS
        params_code = f"\nSTRATEGY_PARAMS['{new_strategy_id}'] = {parameters}\n"
        description_code = f"STRATEGY_DESCRIPTIONS['{new_strategy_id}'] = 'Optimized strategy saved from Strategy-bot: {new_name}'\n"

        with open(strategies_file, 'a') as f:
            f.write(params_code)
            f.write(description_code)

        # Reload the strategies module to make the new strategy available immediately
        importlib.reload(strategies)

        return jsonify({
            'success': True,
            'message': f'Strategy saved as {new_name}',
            'strategy_id': new_strategy_id
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Error saving strategy: {str(e)}'}), 500

# --- Routes ---
@app.route('/')
def index():
    """Renders the main application page."""
    config = load_config()

    # Get the active tab from the query parameter
    active_tab = request.args.get('tab', 'Configuration')

    # Validate the tab name to prevent security issues
    valid_tabs = ['Configuration', 'Strategies', 'Backtesting', 'StrategyBot', 'LiveTrading']
    if active_tab not in valid_tabs:
        active_tab = 'Configuration'

    return render_template('index.html',
                         config=config,
                         strategies=list(STRATEGY_PARAMS.keys()),
                         strategy_descriptions=STRATEGY_DESCRIPTIONS,
                         STRATEGY_PARAMS=STRATEGY_PARAMS,
                         active_tab=active_tab) # Pass the active tab

@app.route('/debug')
def debug():
    """Renders the debug page for Chart.js testing."""
    return render_template('debug.html')

@app.route('/strategies')
def strategy_management():
    """Renders the strategy management page."""
    return render_template('strategy_management.html')

@app.route('/strategies/pine_examples/<filename>')
def serve_pine_strategy(filename):
    """Serves a Pine Script strategy file."""
    pine_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategies', 'pine_examples')
    return send_from_directory(pine_dir, filename)

@app.route('/available_python_strategies')
def get_available_python_strategies():
    """Returns a list of available Python strategies."""
    from strategies import STRATEGY_PARAMS, STRATEGY_DESCRIPTIONS

    # Define the core strategies we want to show first in the UI
    core_strategies = [
        'master_strategy'  # Only show the master combined strategy
    ]

    # Core strategies that should not be deletable
    core_non_deletable = ["master_strategy"]

    strategies = []

    # First add core strategies
    for strategy_id in core_strategies:
        if strategy_id in STRATEGY_PARAMS:
            # For the test strategy, rename it to bollinger_bands for display
            display_id = 'bollinger_bands' if strategy_id == 'test' else strategy_id
            display_name = display_id.replace('_', ' ').title()

            strategies.append({
                'id': strategy_id,
                'name': display_name,
                'description': STRATEGY_DESCRIPTIONS.get(strategy_id, 'No description available'),
                'is_core': True
            })

    # Then add all other user-created strategies
    for strategy_id in STRATEGY_PARAMS:
        if strategy_id not in core_strategies:
            # Skip strategies that are temporary or internal
            if strategy_id.startswith('temp_') or strategy_id.startswith('_'):
                continue

            display_name = strategy_id.replace('_', ' ').title()

            strategies.append({
                'id': strategy_id,
                'name': display_name,
                'description': STRATEGY_DESCRIPTIONS.get(strategy_id, 'No description available'),
                'is_core': strategy_id in core_non_deletable
            })

    print(f"Returning {len(strategies)} available Python strategies")
    return jsonify(strategies)

@app.route('/strategies/python/<strategy_id>')
def get_python_strategy(strategy_id):
    """Returns information about a Python strategy."""
    from strategies import STRATEGY_PARAMS, STRATEGY_DESCRIPTIONS
    import inspect
    import strategies

    print(f"Requested Python strategy: {strategy_id}")
    print(f"Available strategies: {list(STRATEGY_PARAMS.keys())}")

    # Check if strategy exists
    if strategy_id not in STRATEGY_PARAMS:
        print(f"Strategy {strategy_id} not found in STRATEGY_PARAMS")
        return jsonify({'error': f'Strategy {strategy_id} not found'}), 404

    # Get strategy parameters
    params = STRATEGY_PARAMS.get(strategy_id, {})
    print(f"Strategy parameters: {params}")

    # Get strategy description
    description = STRATEGY_DESCRIPTIONS.get(strategy_id, 'No description available')
    print(f"Strategy description: {description}")

    # For all strategies, use the master_strategy function
    strategy_func = None
    try:
        if hasattr(strategies, 'master_strategy'):
            strategy_func = getattr(strategies, 'master_strategy')
            print(f"Using master_strategy function for {strategy_id}")
        else:
            print(f"master_strategy function not found")
    except Exception as e:
        print(f"Error getting master_strategy function: {e}")

    # Get strategy code (use master_strategy code for all strategies)
    strategy_code = ""
    if strategy_func:
        try:
            strategy_code = inspect.getsource(strategy_func)
            print(f"Got strategy code of length: {len(strategy_code)}")
        except Exception as e:
            print(f"Error getting source code: {e}")

    # Prepare response
    response_data = {
        'id': strategy_id,
        'name': strategy_id.replace('_', ' ').title(),
        'description': description,
        'params': params,
        'code': strategy_code
    }
    print(f"Returning response with keys: {list(response_data.keys())}")

    # Return strategy information
    return jsonify(response_data)

@app.route('/create_python_strategy', methods=['POST'])
def create_python_strategy():
    """Creates a new Python strategy."""
    data = request.json
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    print(f"Received data: {data}")

    strategy_id = data.get('id')
    strategy_name = data.get('name')
    strategy_description = data.get('description')
    strategy_template = data.get('template')

    print(f"Template: {strategy_template}")

    if not strategy_id:
        return jsonify({'error': 'No strategy ID provided'}), 400

    if not strategy_name:
        return jsonify({'error': 'No strategy name provided'}), 400

    if not strategy_description:
        return jsonify({'error': 'No strategy description provided'}), 400

    # Always use 'simple' as the default template
    if not strategy_template:
        strategy_template = 'simple'

    # Check if strategy already exists
    from strategies import STRATEGY_PARAMS
    if strategy_id in STRATEGY_PARAMS:
        return jsonify({'error': f'Strategy {strategy_id} already exists'}), 400

    # Create a new strategy with parameters for the master strategy
    strategy_code = ""
    # We're ignoring the template type and always using the same template
    if strategy_template == 'rsi':
        strategy_code = f"""def {strategy_id}(data, rsi_period=14, oversold=30, overbought=70):
    \"\"\"{strategy_description}\"\"\"
    import pandas as pd
    import numpy as np

    # Calculate RSI
    delta = data['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    avg_gain = gain.rolling(window=rsi_period).mean()
    avg_loss = loss.rolling(window=rsi_period).mean()

    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    data['rsi'] = rsi

    # Initialize signals
    buy_signals = pd.Series(False, index=data.index)
    sell_signals = pd.Series(False, index=data.index)

    # Generate signals
    for i in range(1, len(data)):
        # Buy when RSI crosses below oversold level
        if data['rsi'].iloc[i-1] < oversold and data['rsi'].iloc[i] > oversold:
            buy_signals.iloc[i] = True

        # Sell when RSI crosses above overbought level
        elif data['rsi'].iloc[i-1] < overbought and data['rsi'].iloc[i] > overbought:
            sell_signals.iloc[i] = True

    return buy_signals, sell_signals
"""
    elif strategy_template == 'macd':
        strategy_code = f"""def {strategy_id}(data, fast_period=12, slow_period=26, signal_period=9):
    \"\"\"{strategy_description}\"\"\"
    import pandas as pd
    import numpy as np

    # Calculate MACD
    data['ema_fast'] = data['close'].ewm(span=fast_period, adjust=False).mean()
    data['ema_slow'] = data['close'].ewm(span=slow_period, adjust=False).mean()
    data['macd'] = data['ema_fast'] - data['ema_slow']
    data['signal'] = data['macd'].ewm(span=signal_period, adjust=False).mean()
    data['histogram'] = data['macd'] - data['signal']

    # Initialize signals
    buy_signals = pd.Series(False, index=data.index)
    sell_signals = pd.Series(False, index=data.index)

    # Generate signals
    for i in range(1, len(data)):
        # Buy when MACD crosses above signal line
        if data['macd'].iloc[i-1] < data['signal'].iloc[i-1] and data['macd'].iloc[i] > data['signal'].iloc[i]:
            buy_signals.iloc[i] = True

        # Sell when MACD crosses below signal line
        elif data['macd'].iloc[i-1] > data['signal'].iloc[i-1] and data['macd'].iloc[i] < data['signal'].iloc[i]:
            sell_signals.iloc[i] = True

    return buy_signals, sell_signals
"""
    elif strategy_template == 'bollinger':
        strategy_code = f"""def {strategy_id}(data, window=20, num_std=2):
    \"\"\"{strategy_description}\"\"\"
    import pandas as pd
    import numpy as np

    # Calculate Bollinger Bands
    data['sma'] = data['close'].rolling(window=window).mean()
    data['std'] = data['close'].rolling(window=window).std()
    data['upper_band'] = data['sma'] + (data['std'] * num_std)
    data['lower_band'] = data['sma'] - (data['std'] * num_std)

    # Initialize signals
    buy_signals = pd.Series(False, index=data.index)
    sell_signals = pd.Series(False, index=data.index)

    # Generate signals
    for i in range(1, len(data)):
        # Buy when price crosses below lower band
        if data['close'].iloc[i-1] < data['lower_band'].iloc[i-1] and data['close'].iloc[i] > data['lower_band'].iloc[i]:
            buy_signals.iloc[i] = True

        # Sell when price crosses above upper band
        elif data['close'].iloc[i-1] > data['upper_band'].iloc[i-1] and data['close'].iloc[i] < data['upper_band'].iloc[i]:
            sell_signals.iloc[i] = True

    return buy_signals, sell_signals
"""
    elif strategy_template == 'ichimoku':
        strategy_code = f"""def {strategy_id}(data, tenkan_period=9, kijun_period=26, senkou_span_b_period=52):
    \"\"\"{strategy_description}\"\"\"
    import pandas as pd
    import numpy as np

    # Calculate Ichimoku Cloud components
    # Tenkan-sen (Conversion Line): (highest high + lowest low) / 2 for the past 9 periods
    high_9 = data['high'].rolling(window=tenkan_period).max()
    low_9 = data['low'].rolling(window=tenkan_period).min()
    data['tenkan_sen'] = (high_9 + low_9) / 2

    # Kijun-sen (Base Line): (highest high + lowest low) / 2 for the past 26 periods
    high_26 = data['high'].rolling(window=kijun_period).max()
    low_26 = data['low'].rolling(window=kijun_period).min()
    data['kijun_sen'] = (high_26 + low_26) / 2

    # Senkou Span A (Leading Span A): (Conversion Line + Base Line) / 2, plotted 26 periods ahead
    data['senkou_span_a'] = ((data['tenkan_sen'] + data['kijun_sen']) / 2).shift(kijun_period)

    # Senkou Span B (Leading Span B): (highest high + lowest low) / 2 for the past 52 periods, plotted 26 periods ahead
    high_52 = data['high'].rolling(window=senkou_span_b_period).max()
    low_52 = data['low'].rolling(window=senkou_span_b_period).min()
    data['senkou_span_b'] = ((high_52 + low_52) / 2).shift(kijun_period)

    # Initialize signals
    buy_signals = pd.Series(False, index=data.index)
    sell_signals = pd.Series(False, index=data.index)

    # Generate signals
    for i in range(1, len(data)):
        # Buy when Tenkan-sen crosses above Kijun-sen (TK Cross)
        if data['tenkan_sen'].iloc[i-1] < data['kijun_sen'].iloc[i-1] and data['tenkan_sen'].iloc[i] > data['kijun_sen'].iloc[i]:
            buy_signals.iloc[i] = True

        # Sell when Tenkan-sen crosses below Kijun-sen
        elif data['tenkan_sen'].iloc[i-1] > data['kijun_sen'].iloc[i-1] and data['tenkan_sen'].iloc[i] < data['kijun_sen'].iloc[i]:
            sell_signals.iloc[i] = True

    return buy_signals, sell_signals
"""
    elif strategy_template == 'vwap':
        strategy_code = f"""def {strategy_id}(data, vwap_period=14, threshold=0.01):
    \"\"\"{strategy_description}\"\"\"
    import pandas as pd
    import numpy as np

    # Calculate VWAP (Volume Weighted Average Price)
    data['vwap'] = (data['close'] * data['volume']).rolling(window=vwap_period).sum() / data['volume'].rolling(window=vwap_period).sum()

    # Calculate percentage difference between close and VWAP
    data['close_vwap_pct'] = (data['close'] - data['vwap']) / data['vwap']

    # Initialize signals
    buy_signals = pd.Series(False, index=data.index)
    sell_signals = pd.Series(False, index=data.index)

    # Generate signals
    for i in range(1, len(data)):
        # Buy when price crosses above VWAP by threshold percentage
        if data['close_vwap_pct'].iloc[i-1] < threshold and data['close_vwap_pct'].iloc[i] > threshold:
            buy_signals.iloc[i] = True

        # Sell when price crosses below VWAP by threshold percentage
        elif data['close_vwap_pct'].iloc[i-1] > -threshold and data['close_vwap_pct'].iloc[i] < -threshold:
            sell_signals.iloc[i] = True

    return buy_signals, sell_signals
"""
    elif strategy_template == 'simple':
        # Use the basic strategy template
        strategy_code = f"""def {strategy_id}(data, param1=10, param2=20):
    \"\"\"{strategy_description}\"\"\"
    import pandas as pd
    import numpy as np

    # Initialize signals
    buy_signals = pd.Series(False, index=data.index)
    sell_signals = pd.Series(False, index=data.index)

    # Generate signals
    for i in range(1, len(data)):
        # Example buy condition
        if data['close'].iloc[i] > data['close'].iloc[i-1] * (1 + param1/1000):
            buy_signals.iloc[i] = True

        # Example sell condition
        elif data['close'].iloc[i] < data['close'].iloc[i-1] * (1 - param2/1000):
            sell_signals.iloc[i] = True

    return buy_signals, sell_signals
"""
    else:
        return jsonify({'error': f'Unknown template: {strategy_template}'}), 400

    try:
        # Add the strategy to the strategies.py file
        import inspect
        import strategies

        # Get the path to the strategies.py file
        strategies_file = inspect.getfile(strategies)

        # Add the strategy to STRATEGY_PARAMS and STRATEGY_DESCRIPTIONS
        # Create a copy of the master strategy parameters
        from strategies import STRATEGY_PARAMS, STRATEGY_DESCRIPTIONS

        # Create a new strategy with the master strategy parameters
        params_code = f"\nSTRATEGY_PARAMS['{strategy_id}'] = {{\n"
        params_code += "    'use_sma': True,\n"
        params_code += "    'use_rsi': True,\n"
        params_code += "    'use_macd': True,\n"
        params_code += "    'use_bollinger': True,\n"
        params_code += "    'use_stochastic': False,\n"
        params_code += "    'use_volume': False,\n"
        params_code += "    'weight_sma_buy': 1.0,\n"
        params_code += "    'weight_sma_sell': 1.0,\n"
        params_code += "    'weight_rsi_buy': 1.0,\n"
        params_code += "    'weight_rsi_sell': 1.0,\n"
        params_code += "    'weight_macd_buy': 1.0,\n"
        params_code += "    'weight_macd_sell': 1.0,\n"
        params_code += "    'weight_bollinger_buy': 1.0,\n"
        params_code += "    'weight_bollinger_sell': 1.0,\n"
        params_code += "    'sma_depends_on_volume': 0.0,\n"
        params_code += "    'rsi_depends_on_sma': 0.0,\n"
        params_code += "    'macd_depends_on_rsi': 0.0,\n"
        params_code += "    'bollinger_depends_on_volume': 0.0,\n"
        params_code += "    'threshold_buy': 0.3,\n"
        params_code += "    'threshold_sell': 0.3,\n"
        params_code += "    'take_profit': 5.0,\n"
        params_code += "    'stop_loss': 2.0\n"
        params_code += "}\n"

        description_code = f"STRATEGY_DESCRIPTIONS['{strategy_id}'] = '{strategy_description}'\n"

        with open(strategies_file, 'a') as f:
            f.write(params_code)
            f.write(description_code)

        # Reload the strategies module to make the new strategy available immediately
        import importlib
        importlib.reload(strategies)

        return jsonify({'message': f'Strategy {strategy_name} created successfully'})

    except Exception as e:
        return jsonify({'error': f'Error creating strategy: {str(e)}'}), 500



@app.route('/save_python_strategy', methods=['POST'])
def save_python_strategy():
    """Saves a Python strategy."""
    data = request.json
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    strategy_id = data.get('strategy_id')
    code = data.get('code')

    if not strategy_id:
        return jsonify({'error': 'No strategy ID provided'}), 400

    if not code:
        return jsonify({'error': 'No code provided'}), 400

    # Check if strategy exists
    from strategies import STRATEGY_PARAMS
    if strategy_id not in STRATEGY_PARAMS:
        return jsonify({'error': f'Strategy {strategy_id} not found'}), 404

    # Save the strategy code to a temporary file
    import tempfile
    import os

    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix='.py', delete=False) as temp_file:
            temp_file.write(code.encode('utf-8'))
            temp_path = temp_file.name

        # Validate the code by trying to import it
        import importlib.util
        import sys

        spec = importlib.util.spec_from_file_location(f"temp_{strategy_id}", temp_path)
        module = importlib.util.module_from_spec(spec)
        sys.modules[f"temp_{strategy_id}"] = module
        spec.loader.exec_module(module)

        # Check if the strategy function exists in the module
        if not hasattr(module, strategy_id):
            os.unlink(temp_path)  # Clean up
            return jsonify({'error': f'Strategy function {strategy_id} not found in the code'}), 400

        # If we get here, the code is valid
        # Now we can save it to the actual strategy file
        import inspect
        import strategies

        # Get the path to the strategies.py file
        strategies_file = inspect.getfile(strategies)

        # Read the current content of the file
        with open(strategies_file, 'r') as f:
            content = f.read()

        # Find the strategy function in the content
        import re
        pattern = f"def {strategy_id}\([^)]*\):.*?(?=\n\ndef|\n\nSTRATEGY_PARAMS|$)"
        match = re.search(pattern, content, re.DOTALL)

        if not match:
            os.unlink(temp_path)  # Clean up
            return jsonify({'error': f'Could not find strategy function {strategy_id} in the strategies.py file'}), 500

        # Replace the strategy function with the new code
        new_content = content.replace(match.group(0), code.strip())

        # Write the new content back to the file
        with open(strategies_file, 'w') as f:
            f.write(new_content)

        # Clean up
        os.unlink(temp_path)

        return jsonify({'message': f'Strategy {strategy_id} saved successfully'})

    except Exception as e:
        # Clean up if the temp file exists
        if 'temp_path' in locals():
            try:
                os.unlink(temp_path)
            except:
                pass

        return jsonify({'error': f'Error saving strategy: {str(e)}'}), 500

@app.route('/save_strategy_parameters', methods=['POST'])
def save_strategy_parameters():
    """Saves parameters for a strategy."""
    data = request.json
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    print(f"Received data: {data}")

    strategy_id = data.get('strategy_id')
    parameters = data.get('parameters')
    strategy_name = data.get('strategy_name')
    strategy_description = data.get('strategy_description')

    if not strategy_id:
        return jsonify({'error': 'No strategy ID provided'}), 400

    if not parameters:
        return jsonify({'error': 'No parameters provided'}), 400

    try:
        # Update the strategy parameters in STRATEGY_PARAMS
        from strategies import STRATEGY_PARAMS, STRATEGY_DESCRIPTIONS

        if strategy_id not in STRATEGY_PARAMS:
            return jsonify({'error': f'Strategy {strategy_id} not found'}), 404

        # Update parameters
        STRATEGY_PARAMS[strategy_id].update(parameters)

        # Update strategy name in memory if provided
        if strategy_name:
            STRATEGY_DESCRIPTIONS[strategy_id] = strategy_name
            print(f"Updated strategy name in memory: {strategy_id} -> {strategy_name}")

        # Save the updated parameters to the strategies file
        import inspect
        import strategies
        import re
        import importlib

        strategies_file = inspect.getfile(strategies)

        # Read the current content of the file
        with open(strategies_file, 'r') as f:
            content = f.read()

        # Find the STRATEGY_PARAMS entry for this strategy
        pattern = r"STRATEGY_PARAMS\['" + re.escape(strategy_id) + r"'\]\s*=\s*{[^}]*}"
        match = re.search(pattern, content)

        if not match:
            return jsonify({'error': f'Strategy parameters for {strategy_id} not found in file'}), 404

        # Create the new parameters string
        params_str = f"STRATEGY_PARAMS['{strategy_id}'] = {{\n"
        for key, value in parameters.items():
            if isinstance(value, bool):
                # Use Python's True/False instead of JavaScript's true/false
                params_str += f"    '{key}': {str(value)},\n"
            elif isinstance(value, (int, float)):
                # Make sure floats have .0 suffix
                if isinstance(value, int):
                    params_str += f"    '{key}': {value}.0,\n"
                else:
                    params_str += f"    '{key}': {value},\n"
            else:
                params_str += f"    '{key}': '{value}',\n"
        params_str += "}"

        # Replace the old parameters with the new ones
        new_content = content.replace(match.group(0), params_str)

        # Update the strategy name and description if provided
        if strategy_name or strategy_description:
            # Find the STRATEGY_DESCRIPTIONS entry for this strategy
            desc_pattern = r"STRATEGY_DESCRIPTIONS\['" + re.escape(strategy_id) + r"'\]\s*=\s*'[^']*'"
            desc_match = re.search(desc_pattern, new_content)

            # Use the provided description or keep the existing one
            description_to_use = strategy_description if strategy_description else STRATEGY_DESCRIPTIONS.get(strategy_id, "")

            # If strategy name is provided, use it for the display name
            if strategy_name:
                print(f"Using strategy name for display: {strategy_name}")
                # Update the in-memory version with the strategy name
                STRATEGY_DESCRIPTIONS[strategy_id] = strategy_name
                print(f"Updated strategy name in memory: {strategy_id} -> {strategy_name}")

            if desc_match:
                # Create the new description string using the appropriate value
                if strategy_name:
                    desc_str = f"STRATEGY_DESCRIPTIONS['{strategy_id}'] = '{strategy_name}'"
                else:
                    desc_str = f"STRATEGY_DESCRIPTIONS['{strategy_id}'] = '{description_to_use}'"

                # Replace the old description with the new one
                new_content = new_content.replace(desc_match.group(0), desc_str)
                print(f"Updated strategy description in file: {strategy_id} -> {strategy_name if strategy_name else description_to_use}")
            else:
                # If no description entry exists, add it after the parameters
                params_pattern = r"STRATEGY_PARAMS\['" + re.escape(strategy_id) + r"'\]\s*=\s*{[^}]*}"
                params_match = re.search(params_pattern, new_content)
                if params_match:
                    # Insert the description after the parameters
                    insert_pos = params_match.end()
                    if strategy_name:
                        new_content = new_content[:insert_pos] + f"\nSTRATEGY_DESCRIPTIONS['{strategy_id}'] = '{strategy_name}'" + new_content[insert_pos:]
                        print(f"Added new strategy name to file: {strategy_id} -> {strategy_name}")
                    else:
                        new_content = new_content[:insert_pos] + f"\nSTRATEGY_DESCRIPTIONS['{strategy_id}'] = '{description_to_use}'" + new_content[insert_pos:]
                        print(f"Added new strategy description to file: {strategy_id} -> {description_to_use}")

            # Also update the in-memory version with the description if provided
            if strategy_description:
                # We keep the name as the display name, but store the description separately if needed
                # For now, we're just using the name for display
                print(f"Strategy description provided: {strategy_description}")

        # Write the updated content back to the file
        with open(strategies_file, 'w') as f:
            f.write(new_content)

        # Reload the strategies module
        importlib.reload(strategies)

        # Check if we need to create a new version or just update the existing strategy
        create_new_version = request.json.get('create_new_version', True)

        if create_new_version:
            # Create a new version of the strategy with these parameters
            timestamp = int(time.time())
            new_strategy_id = f"{strategy_id}_v{timestamp}"

            # Use the provided name for the new version if available
            new_strategy_desc = strategy_description if strategy_description else f"Custom version of {strategy_id} with optimized parameters"

            # Save as a new strategy version
            STRATEGY_PARAMS[new_strategy_id] = parameters.copy()
            STRATEGY_DESCRIPTIONS[new_strategy_id] = new_strategy_desc

            # Add the new strategy to the file
            with open(strategies_file, 'a') as f:
                f.write(f"\n\nSTRATEGY_PARAMS['{new_strategy_id}'] = {{\n")
                for key, value in parameters.items():
                    if isinstance(value, bool):
                        # Use Python's True/False instead of JavaScript's true/false
                        f.write(f"    '{key}': {str(value)},\n")
                    elif isinstance(value, (int, float)):
                        # Make sure floats have .0 suffix
                        if isinstance(value, int):
                            f.write(f"    '{key}': {value}.0,\n")
                        else:
                            f.write(f"    '{key}': {value},\n")
                    else:
                        f.write(f"    '{key}': '{value}',\n")
                f.write("}\n")
                f.write(f"STRATEGY_DESCRIPTIONS['{new_strategy_id}'] = '{new_strategy_desc}'\n")
        else:
            # Just update the existing strategy in memory
            new_strategy_id = strategy_id

        # Reload the strategies module again
        importlib.reload(strategies)

        return jsonify({
            'message': f'Strategy parameters saved successfully. New version created: {new_strategy_id}',
            'new_strategy_id': new_strategy_id
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Error saving strategy parameters: {str(e)}'}), 500


@app.route('/test_python_strategy', methods=['POST'])
def test_python_strategy():
    """Tests a Python strategy."""
    data = request.json
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    strategy_id = data.get('strategy_id')
    code = data.get('code')

    if not strategy_id:
        return jsonify({'error': 'No strategy ID provided'}), 400

    if not code:
        return jsonify({'error': 'No code provided'}), 400

    # Save the strategy code to a temporary file
    import tempfile
    import os

    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix='.py', delete=False) as temp_file:
            temp_file.write(code.encode('utf-8'))
            temp_path = temp_file.name

        # Validate the code by trying to import it
        import importlib.util
        import sys

        spec = importlib.util.spec_from_file_location(f"temp_{strategy_id}", temp_path)
        module = importlib.util.module_from_spec(spec)
        sys.modules[f"temp_{strategy_id}"] = module
        spec.loader.exec_module(module)

        # Check if the strategy function exists in the module
        if not hasattr(module, strategy_id):
            os.unlink(temp_path)  # Clean up
            return jsonify({'error': f'Strategy function {strategy_id} not found in the code'}), 400

        # Get the strategy function
        strategy_func = getattr(module, strategy_id)

        # Test the strategy with some sample data
        import pandas as pd
        import numpy as np

        # Create some sample data
        dates = pd.date_range('2020-01-01', periods=100)
        data = pd.DataFrame({
            'open': np.random.randn(100).cumsum() + 100,
            'high': np.random.randn(100).cumsum() + 102,
            'low': np.random.randn(100).cumsum() + 98,
            'close': np.random.randn(100).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)

        # Call the strategy function with the sample data
        from strategies import STRATEGY_PARAMS
        params = STRATEGY_PARAMS.get(strategy_id, {})

        # Get the parameter names from the function signature
        import inspect
        sig = inspect.signature(strategy_func)
        param_names = list(sig.parameters.keys())[1:]  # Skip the first parameter (data)

        # Create a dictionary of parameters to pass to the function
        func_params = {}
        for name in param_names:
            if name in params:
                func_params[name] = params[name]

        # Call the function
        result = strategy_func(data, **func_params)

        # Check if the result is valid
        if not isinstance(result, tuple) or len(result) != 2:
            return jsonify({'error': 'Strategy function must return a tuple of (buy_signals, sell_signals)'}), 400

        buy_signals, sell_signals = result

        if not isinstance(buy_signals, pd.Series) or not isinstance(sell_signals, pd.Series):
            return jsonify({'error': 'Strategy function must return a tuple of (buy_signals, sell_signals) as pandas Series'}), 400

        # Count the number of buy and sell signals
        num_buy = buy_signals.sum()
        num_sell = sell_signals.sum()

        # Clean up
        os.unlink(temp_path)

        return jsonify({
            'message': f'Strategy {strategy_id} tested successfully',
            'output': f'Test completed successfully.\nBuy signals: {num_buy}\nSell signals: {num_sell}'
        })

    except Exception as e:
        # Clean up if the temp file exists
        if 'temp_path' in locals():
            try:
                os.unlink(temp_path)
            except:
                pass

        return jsonify({'error': f'Error testing strategy: {str(e)}'}), 500

@app.route('/config', methods=['POST'])
def update_config():
    """Updates and saves the full configuration."""
    global APP_CONFIG

    # --- Update Credentials ---
    wallet_address = request.form.get('wallet_address', '').strip()
    private_key = request.form.get('private_key', '').strip()

    # Basic validation (presence check)
    if not wallet_address or not private_key:
         return jsonify({'status': 'error', 'message': 'Both Wallet Address and Private Key are required.'}), 400

    # Validate address format
    if not wallet_address.startswith('0x') or len(wallet_address) != 42:
         return jsonify({'status': 'error', 'message': 'Invalid Wallet Address format.'}), 400

    # Validate private key format
    if not private_key.startswith('0x') or len(private_key) != 66:
         print("Warning: Private key format seems unusual.")
         # Allow saving, test will likely fail.

    APP_CONFIG['credentials']['wallet_address'] = wallet_address
    APP_CONFIG['credentials']['private_key'] = private_key

    # --- Update Trading Parameters ---
    APP_CONFIG['trading']['pair'] = request.form.get('selected_pair', APP_CONFIG['trading']['pair']) # Keep old if not provided
    APP_CONFIG['trading']['strategy'] = request.form.get('selected_strategy', APP_CONFIG['trading']['strategy']) # Keep old if not provided

    try:
        capital_str = request.form.get('capital')
        tp_str = request.form.get('take_profit')
        sl_str = request.form.get('stop_loss')

        # Update only if values are provided in the form
        if capital_str is not None: APP_CONFIG['trading']['capital'] = float(capital_str)
        if tp_str is not None: APP_CONFIG['trading']['take_profit'] = float(tp_str)
        if sl_str is not None: APP_CONFIG['trading']['stop_loss'] = float(sl_str)

        # Add validation for numeric values (ensure they are positive)
        if APP_CONFIG['trading']['capital'] <= 0:
             return jsonify({'status': 'error', 'message': 'Capital must be a positive number.'}), 400
        if APP_CONFIG['trading']['take_profit'] <= 0:
             return jsonify({'status': 'error', 'message': 'Take Profit must be a positive number.'}), 400
        if APP_CONFIG['trading']['stop_loss'] <= 0:
             return jsonify({'status': 'error', 'message': 'Stop Loss must be a positive number.'}), 400

    except (ValueError, TypeError) as e:
         # Handle cases where conversion to float fails or input is not suitable
         return jsonify({'status': 'error', 'message': f'Invalid numeric input for Capital, Take Profit, or Stop Loss: {e}'}), 400
    except Exception as e: # Catch other potential errors during update
         print(f"Error updating trading parameters: {e}")
         traceback.print_exc()
         return jsonify({'status': 'error', 'message': 'An unexpected error occurred while updating trading parameters.'}), 500


    # --- Save and Test ---
    save_config()
    # Test the credentials after saving
    is_valid, message = test_api_credentials(APP_CONFIG['credentials']['wallet_address'], APP_CONFIG['credentials']['private_key'])

    if is_valid:
        return jsonify({'status': 'success', 'message': f'Configuration saved. Credentials test: {message}'})
    else:
        # Still save, but report the test failure
        return jsonify({'status': 'warning', 'message': f'Configuration saved, but credentials test failed: {message}'})

@app.route('/test_api', methods=['POST'])
def test_api_keys_route():
    """Tests the configured credentials via the hyperliquid_api module."""
    # Use credentials directly from the global APP_CONFIG
    wallet = APP_CONFIG['credentials'].get('wallet_address')
    key = APP_CONFIG['credentials'].get('private_key')
    is_valid, message = test_api_credentials(wallet, key)
    if is_valid:
        return jsonify({'status': 'success', 'message': message})
    else:
        # Return 400 for client-side error (bad credentials) or 500 for server-side issues
        status_code = 400 if "required" in message.lower() or "invalid" in message.lower() else 500
        return jsonify({'status': 'error', 'message': message}), status_code

# Function to sanitize filename
def sanitize_filename(name):
    # Remove invalid characters and replace spaces
    name = re.sub(r'[^\w\-_\. ]', '', name)
    name = name.strip().replace(' ', '_')
    return name

@app.route('/strategies')
def get_strategies():
    """Returns the list of available strategies from Python and Pine Script files."""
    strategies = []

    # Add Python strategies from STRATEGY_PARAMS
    from strategies import STRATEGY_PARAMS
    for strategy_id, params in STRATEGY_PARAMS.items():
        # Skip strategies that are temporary or internal
        if strategy_id.startswith('temp_') or strategy_id.startswith('_'):
            continue

        # Get the display name from STRATEGY_DESCRIPTIONS if available
        from strategies import STRATEGY_DESCRIPTIONS
        if strategy_id in STRATEGY_DESCRIPTIONS:
            display_name = STRATEGY_DESCRIPTIONS[strategy_id]
        else:
            # Format the display name from the strategy_id
            display_name = strategy_id.replace('_', ' ').title()

        # Special case for test strategy (which is actually bollinger_bands)
        if strategy_id == 'test':
            display_name = 'Bollinger Bands'

        strategies.append({"id": strategy_id, "name": display_name, "type": "python"})

    # Add strategies from .pine files in the strategies directory
    print(f"Looking for .pine strategies in: {STRATEGIES_DIR}") # Debug log
    try:
        # Ensure the strategies directory exists
        os.makedirs(STRATEGIES_DIR, exist_ok=True)

        for filename in os.listdir(STRATEGIES_DIR):
            if filename.endswith('.pine'):
                strategy_id = filename[:-5]  # Remove .pine extension
                # Use the filename (without extension) as the name, maybe sanitize later if needed
                strategy_name = strategy_id.replace('_', ' ').title()

                # Try to extract strategy name from the file content
                try:
                    with open(os.path.join(STRATEGIES_DIR, filename), 'r') as f:
                        content = f.read()
                        # Look for strategy name in the strategy declaration
                        name_match = re.search(r'strategy\(\s*"([^"]+)"', content)
                        if name_match:
                            strategy_name = name_match.group(1)
                except Exception as e:
                    print(f"Error reading strategy file {filename}: {e}")

                print(f"Found .pine strategy: id={strategy_id}, name={strategy_name}") # Debug log
                strategies.append({"id": strategy_id, "name": f"{strategy_name} (Pine Script)", "type": "pine"})
    except FileNotFoundError:
        print(f"Warning: Strategies directory not found at {STRATEGIES_DIR}")
    except Exception as e:
        print(f"Error listing strategies in {STRATEGIES_DIR}: {e}")

    print(f"Returning strategies: {strategies}") # Debug log
    return jsonify({"strategies": strategies})

@app.route('/pairs')
def get_trading_pairs():
    """Returns the list of available trading pairs."""
    try:
        # --- Restore original logic ---
        pairs = fetch_trading_pairs()
        if pairs:
            formatted_pairs = [{"id": pair, "name": f"{pair}-USD"} for pair in pairs]
            json_output = json.dumps(formatted_pairs, indent=2) # Use indent for readability
            return Response(json_output, mimetype='application/json')
        else:
            return Response("[]", mimetype='application/json') # Return empty JSON list
    except Exception as e:
        print(f"Error fetching trading pairs: {e}") # Keep original log message
        traceback.print_exc()
        # Return an error JSON response
        error_response = json.dumps({'error': 'Failed to fetch trading pairs'})
        return Response(error_response, mimetype='application/json', status=500)

@app.route('/strategies/save', methods=['POST'])
def save_strategy():
    """Saves strategy code to a file."""
    data = request.json
    name = data.get('name')
    code = data.get('code')

    if not name or not code:
        return jsonify({'status': 'error', 'message': 'Strategy name and code are required.'}), 400

    filename = sanitize_filename(name)
    if not filename:
         return jsonify({'status': 'error', 'message': 'Invalid strategy name.'}), 400

    filepath = os.path.join(STRATEGIES_DIR, f"{filename}.pine") # Save as .pine

    try:
        with open(filepath, 'w') as f:
            f.write(code)
        return jsonify({'status': 'success', 'message': f'Strategy "{name}" saved as {filename}.pine'}) # Update message
    except Exception as e:
        print(f"Error saving strategy file {filepath}: {e}")
        return jsonify({'status': 'error', 'message': 'Failed to save strategy.'}), 500

@app.route('/strategies/load/<strategy_id>')
def load_strategy(strategy_id):
    """Loads strategy code from a file."""
    # Basic check to prevent loading built-in placeholders this way
    if strategy_id in ["simple_sma_cross", "rsi_oversold"]:
         return jsonify({'status': 'error', 'message': 'Cannot load built-in strategy code.'}), 400

    filename = sanitize_filename(strategy_id) # Sanitize just in case
    filepath = os.path.join(STRATEGIES_DIR, f"{filename}.pine") # Load from .pine

    if not os.path.exists(filepath):
        return jsonify({'status': 'error', 'message': f'Strategy file {filename}.pine not found.'}), 404 # Update message

    try:
        with open(filepath, 'r') as f:
            code = f.read()
        # Return filename (as ID) and code
        return jsonify({'status': 'success', 'id': strategy_id, 'code': code})
    except Exception as e:
        print(f"Error loading strategy file {filepath}: {e}")
        return jsonify({'status': 'error', 'message': 'Failed to load strategy.'}), 500

@app.route('/strategies/test_pine', methods=['POST'])
def test_pine_script():
    """
    Performs a basic syntax check on the provided Pine Script code.
    Note: This is a very rudimentary check. Full validation requires TradingView.
    """
    data = request.json
    code = data.get('code')

    if not code:
        return jsonify({'status': 'error', 'message': 'No Pine Script code provided.'}), 400

    # Basic checks (Example: presence of version and strategy/indicator declaration)
    has_version = re.search(r'//\s*@version\s*=\s*\d+', code)
    has_declaration = re.search(r'^\s*(strategy|indicator)\s*\(', code, re.MULTILINE)

    errors = []
    if not has_version:
        errors.append("Missing or invalid '//@version=' directive.")
    if not has_declaration:
        errors.append("Missing 'strategy()' or 'indicator()' declaration.")

    # Add more basic checks here if needed (e.g., balanced parentheses, common keywords)

    if not errors:
        return jsonify({'status': 'success', 'message': 'Basic Pine Script checks passed. Test thoroughly on TradingView for full validation.'})
    else:
        error_message = "Basic Pine Script checks failed: " + " ".join(errors)
        return jsonify({'status': 'error', 'message': error_message}), 400



@app.route('/optimize', methods=['GET', 'POST'])
def optimize_strategy_route():
    """Optimizes strategy parameters using genetic algorithm."""
    try:
        # Handle both GET and POST requests
        if request.method == 'POST':
            if request.is_json:
                data = request.json
            else:
                data = request.form
        else:  # GET request
            data = request.args

        # Get parameters from request
        pair = data.get('pair')
        strategy = data.get('strategy')
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if not all([pair, strategy, start_date, end_date]):
            return jsonify({
                'status': 'error',
                'message': 'Missing required parameters: pair, strategy, start_date, end_date'
            }), 400

        # Run optimization
        result = optimize_strategy(pair, start_date, end_date)

        if result['best_parameters'] is None:
            return jsonify({
                'status': 'error',
                'message': 'Optimization failed to find good parameters'
            }), 400

        return jsonify({
            'status': 'success',
            'best_parameters': result['best_parameters'],
            'best_fitness': result['best_fitness'],
            'history': result['history']
        })

    except Exception as e:
        print(f"Error in optimize_strategy_route: {e}")
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# --- Data Fetching Route ---

@app.route('/fetch_historical_data', methods=['GET', 'POST'])
def fetch_historical_data_endpoint():
    """Fetch historical data for a given pair and time range."""
    try:
        # Handle both GET and POST requests
        if request.method == 'POST':
            if request.is_json:
                data = request.json
            else:
                data = request.form
        else:  # GET request
            data = request.args

        print(f"Received fetch_historical_data request: {data}")

        # Extract parameters
        pair = data.get('pair')
        start_date_str = data.get('start_date')
        end_date_str = data.get('end_date')

        # Validate required parameters
        if not all([pair, start_date_str, end_date_str]):
            print("Error: Missing required parameters for data fetch.")
            return jsonify({'error': 'Missing required parameters'}), 400

        # Convert dates to timestamps
        try:
            start_dt = datetime.strptime(start_date_str, '%Y-%m-%d')
            # For end_date, include the whole day by setting time to 23:59:59
            end_dt = datetime.strptime(end_date_str, '%Y-%m-%d').replace(hour=23, minute=59, second=59)

            # Convert to UTC timestamp in milliseconds
            start_time_ms = int(start_dt.timestamp() * 1000)
            end_time_ms = int(end_dt.timestamp() * 1000)

        except ValueError:
            return jsonify({'error': "Invalid date format. Please use 'YYYY-MM-DD'."}), 400

        print(f"Fetching data for {pair} from {start_time_ms} to {end_time_ms}")

        # Fetch the data
        df = fetch_historical_data(pair, start_time_ms, end_time_ms)

        if df is None:
            return jsonify({'error': f'Failed to fetch data for {pair}'}), 500

        if df.empty:
            return jsonify({'error': f'No data found for {pair} in the specified date range'}), 404

        # Optimize the data for transmission
        # Only include necessary columns to reduce payload size
        essential_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

        # If the dataframe has more than 1000 rows, downsample it to reduce size
        if len(df) > 1000:
            # Downsample by taking every nth row to get about 1000 points
            n = len(df) // 1000 + 1
            df = df.iloc[::n].copy()
            print(f"Downsampled data from {len(df) * n} to {len(df)} points")

        # Select only essential columns if they exist
        columns_to_use = [col for col in essential_columns if col in df.columns]
        df_slim = df[columns_to_use].copy()

        # Convert DataFrame to list of dictionaries for JSON serialization
        data_list = df_slim.reset_index().to_dict('records')

        # Format timestamps for JavaScript
        for item in data_list:
            if 'timestamp' in item:
                # Convert pandas Timestamp to milliseconds for JavaScript
                item['timestamp'] = int(item['timestamp'].timestamp() * 1000)

        print(f"Successfully fetched {len(data_list)} data points")

        # Return the data as JSON
        return jsonify({
            'data': data_list,
            'pair': pair,
            'start_date': start_date_str,
            'end_date': end_date_str,
            'message': f'Successfully downloaded {len(data_list)} data points for {pair}'
        })

    except Exception as e:
        print(f"Error in fetch_historical_data_endpoint: {e}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

# --- Backtesting Route ---

@app.route('/backtest', methods=['GET', 'POST'])
def run_backtest_route():
    """Handles the backtest request."""
    print("\n--- Entering /backtest route ---")

    # Handle both GET and POST requests
    if request.method == 'POST':
        if request.is_json:
            data = request.json
        else:
            data = request.form
    else:  # GET request
        data = request.args

    print(f"Received backtest request data: {data}")
    pair = data.get('pair')
    strategy_id = data.get('strategy')
    start_date_str = data.get('start_date')
    end_date_str = data.get('end_date')
    format_type = data.get('format', 'json')  # Default to JSON if not specified

    # Validate parameters
    if not all([pair, strategy_id, start_date_str, end_date_str]):
        print("Error: Missing required parameters.")
        return jsonify({'error': 'Missing required parameters for backtest.'}), 400

    print(f"Parameters: pair={pair}, strategy={strategy_id}, start={start_date_str}, end={end_date_str}, format={format_type}")

    # Run backtest
    try:
        # Use the new_minimal_backtester module to run the backtest
        try:
            from new_minimal_backtester import run_backtest
            print(f"Starting backtest for {strategy_id} on {pair} from {start_date_str} to {end_date_str}")
            backtest_results = run_backtest(pair, strategy_id, start_date_str, end_date_str)
            print(f"Backtest completed with result keys: {list(backtest_results.keys()) if isinstance(backtest_results, dict) else 'not a dict'}")
        except ImportError as e:
            print(f"Error importing minimal_backtester: {e}")
            return jsonify({'error': f"Failed to import minimal_backtester: {str(e)}"}), 500

        # Check for errors
        if 'error' in backtest_results:
            error_message = backtest_results['error']
            print(f"Backtest failed: {error_message}")
            return jsonify({'error': error_message}), 400

        # If successful, prepare the response
        print("Backtest successful. Preparing response...")
        try:
            # Log the keys in the results for debugging
            print(f"Backtest result keys: {list(backtest_results.keys())}")
            if 'signals' in backtest_results:
                print(f"Number of signals: {len(backtest_results['signals'])}")

            # Check if HTML format is requested
            if format_type.lower() == 'html':
                # Create a custom filter for timestamp conversion
                def timestamp_to_datetime(timestamp):
                    return datetime.fromtimestamp(timestamp / 1000)

                # Add the filter to the Jinja environment
                app.jinja_env.filters['timestamp_to_datetime'] = timestamp_to_datetime

                # Render the HTML template with the backtest results
                strategy_name = strategy_id
                metrics = backtest_results.get('metrics', {})
                trades = backtest_results.get('trades', [])

                # Check if compact format is requested
                if format_type.lower() == 'compact':
                    html_response = render_template('trade_table_compact.html',
                                                  strategy_name=strategy_name,
                                                  metrics=metrics,
                                                  trades=trades)
                else:
                    html_response = render_template('trade_table.html',
                                                  strategy_name=strategy_name,
                                                  metrics=metrics,
                                                  trades=trades)

                print("--- Exiting /backtest route successfully with HTML response ---")
                return Response(html_response, mimetype='text/html')
            else:
                # Return the results as JSON (default)
                # Debug the trades data
                if 'trades' in backtest_results:
                    print(f"Number of trades: {len(backtest_results['trades'])}")
                    for i, trade in enumerate(backtest_results['trades'][:3]):
                        print(f"Trade {i+1} data: {trade}")
                        if 'pnl_percent' in trade:
                            print(f"PnL percent: {trade['pnl_percent']}")

                json_response = jsonify(backtest_results)
                print("--- Exiting /backtest route successfully with JSON response ---")
                return json_response
        except Exception as json_err:
            print(f"!!! Error during jsonify: {json_err}")
            traceback.print_exc()
            return jsonify({'error': f'Failed to serialize backtest results: {str(json_err)}'}), 500
    except Exception as e:
        print(f"!!! Unexpected error in backtest route: {e}")
        traceback.print_exc()
        return jsonify({'error': f'Server error: {str(e)}'}), 500



@app.route('/hyperliquid/pairs')
def get_hyperliquid_pairs_route():
    """Fetches trading pairs from the Hyperliquid API."""
    pairs = fetch_trading_pairs()
    if pairs is not None:
        return jsonify({'pairs': pairs})
    else:
        return jsonify({'error': 'Failed to fetch pairs from Hyperliquid API.'}), 500

@app.route('/hyperliquid/data', methods=['GET'])
def get_hyperliquid_data_route():
    """Fetches historical data from the Hyperliquid API."""
    pair = request.args.get('pair')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    if not all([pair, start_date_str, end_date_str]):
        return jsonify({'error': 'Missing required parameters: pair, start_date, end_date'}), 400

    try:
        # Convert date strings (YYYY-MM-DD) to milliseconds timestamps
        # Assume dates are start/end of the day in UTC
        start_dt = datetime.strptime(start_date_str, '%Y-%m-%d').replace(tzinfo=timezone.utc)
        # Add one day to end date to include the whole day, then get timestamp
        end_dt = datetime.strptime(end_date_str, '%Y-%m-%d').replace(tzinfo=timezone.utc)
        # Hyperliquid might expect end time exclusive or inclusive, adjust if needed
        # For candles, end time is often exclusive of the last candle's period start
        start_time_ms = int(start_dt.timestamp() * 1000)
        end_time_ms = int(end_dt.timestamp() * 1000) + (24 * 60 * 60 * 1000) # Add ms for the full end day

        print(f"Fetching data for {pair} from {start_date_str} ({start_time_ms}) to {end_date_str} ({end_time_ms})")

        # Fetch data using the API module function
        # Note: Hyperliquid API might use asset names like 'BTC' instead of 'BTC-USD'. Adjust 'pair' if needed.
        # Assuming the API function expects the asset name (e.g., 'BTC', 'ETH')
        asset_name = pair.split('-')[0] # Basic split, might need refinement
        data = fetch_historical_data(asset_name, start_time_ms, end_time_ms)

        if data is not None:
             # Convert timestamps back to ISO format strings for JSON compatibility if needed
             # Or ensure the API module returns JSON-serializable data (like ms timestamps)
             # The current API function returns ms timestamps which are fine for JSON
            return jsonify({'data': data})
        else:
            return jsonify({'error': f'Failed to fetch historical data for {pair}.'}), 500

    except ValueError:
        return jsonify({'error': 'Invalid date format. Please use YYYY-MM-DD.'}), 400
    except Exception as e:
        print(f"Error in /hyperliquid/data route: {e}")
        return jsonify({'error': 'An internal error occurred.'}), 500


# --- Live Trading Process Management ---
live_trader_process = None # Store the subprocess object

def start_live_trader(pair, strategy_id, capital, tp, sl):
    """Starts the live_trader.py script as a subprocess."""
    global live_trader_process
    if live_trader_process is not None and live_trader_process.poll() is None:
        print("Live trader process is already running.")
        return False, "Live trader is already running."

    # Use credentials from APP_CONFIG
    wallet = APP_CONFIG['credentials'].get('wallet_address')
    key = APP_CONFIG['credentials'].get('private_key')
    if not wallet or not key:
        return False, "Cannot start live trader: Credentials missing in config."

    # Construct the command to run the script
    python_executable = sys.executable # Use the same python that runs Flask
    script_path = os.path.join(os.path.dirname(__file__), 'live_trader.py')
    command = [
        python_executable,
        script_path,
        pair,
        strategy_id,
        str(capital),
        str(tp),
        str(sl)
        # Credentials are now expected to be read from config.json by live_trader.py
        # Or passed via environment variables if live_trader.py is modified to accept them
    ]

    print(f"Starting live trader with command: {' '.join(command)}")
    try:
        # Pass credentials via environment variables for better security than command line args
        # Assumes live_trader.py is modified to read HL_WALLET_ADDRESS and HL_PRIVATE_KEY
        env = os.environ.copy()
        env['HL_WALLET_ADDRESS'] = wallet
        env['HL_PRIVATE_KEY'] = key

        live_trader_process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, env=env)
        print(f"Live trader process started with PID: {live_trader_process.pid}")
        time.sleep(1) # Give it a second to start
        if live_trader_process.poll() is not None: # Check if it terminated immediately
             stdout, stderr = live_trader_process.communicate()
             print(f"Live trader failed to start. Error:\n{stderr}")
             live_trader_process = None
             return False, f"Live trader failed to start. Check logs. Error: {stderr[:100]}..."
        return True, f"Live trader started successfully (PID: {live_trader_process.pid})."
    except Exception as e:
        print(f"Failed to start live trader process: {e}")
        traceback.print_exc()
        live_trader_process = None
        return False, f"Failed to start live trader process: {e}"

def stop_live_trader():
    """Stops the running live trader process."""
    global live_trader_process
    if live_trader_process is None or live_trader_process.poll() is not None:
        print("Live trader process is not running.")
        return False, "Live trader is not running or already stopped."

    print(f"Stopping live trader process (PID: {live_trader_process.pid})...")
    try:
        # Send SIGTERM for graceful shutdown
        live_trader_process.terminate() # Sends SIGTERM
        # Wait a bit for graceful shutdown
        try:
             stdout, stderr = live_trader_process.communicate(timeout=10) # Wait up to 10s
             print("Live trader process terminated gracefully.")
             print(f"Trader stdout:\n{stdout}")
             print(f"Trader stderr:\n{stderr}")
        except subprocess.TimeoutExpired:
             print("Live trader process did not terminate gracefully after 10s. Forcing kill...")
             live_trader_process.kill() # Sends SIGKILL if terminate didn't work
             stdout, stderr = live_trader_process.communicate()
             print("Live trader process killed.")
             print(f"Trader stdout:\n{stdout}")
             print(f"Trader stderr:\n{stderr}")

        live_trader_process = None
        return True, "Live trader stopped successfully."
    except Exception as e:
        print(f"Error stopping live trader process: {e}")
        traceback.print_exc()
        # Might need to manually kill if terminate fails and communicate errors
        return False, f"Error stopping live trader: {e}"


@app.route('/live_trade', methods=['POST'])
def toggle_live_trade():
    data = request.json
    action = data.get('action') # 'start' or 'stop'

    if action == 'start':
        pair = data.get('pair')
        strategy_id = data.get('strategy')
        capital = data.get('capital')
        tp = data.get('take_profit')
        sl = data.get('stop_loss')

        # Validate received parameters
        if not all([pair, strategy_id]):
             return jsonify({'error': 'Missing pair or strategy for starting live trade.'}), 400
        try:
            # Use defaults from config if not provided, but frontend should send them
            # Ensure conversion to float and check positivity
            capital = float(capital if capital is not None else APP_CONFIG['trading']['capital'])
            tp = float(tp if tp is not None else APP_CONFIG['trading']['take_profit'])
            sl = float(sl if sl is not None else APP_CONFIG['trading']['stop_loss'])
            if capital <= 0 or tp <= 0 or sl <= 0:
                 raise ValueError("Capital, Take Profit, and Stop Loss must be positive numbers.")
        except (ValueError, TypeError):
             return jsonify({'error': 'Invalid numeric input for Capital, Take Profit, or Stop Loss.'}), 400

        success, message = start_live_trader(pair, strategy_id, capital, tp, sl)
        if success:
            return jsonify({'message': message})
        else:
            return jsonify({'error': message}), 500 # Internal server error if start fails

    elif action == 'stop':
        success, message = stop_live_trader()
        if success:
            return jsonify({'message': message})
        else:
            # If already stopped, it's not really an error for the user
            status_code = 400 if "not running" in message else 500
            return jsonify({'error': message}), status_code
    else:
        return jsonify({'error': 'Invalid action specified.'}), 400


# --- Main Execution ---
# Add cleanup for the trader process on Flask exit
import atexit
atexit.register(stop_live_trader)

def background_optimization_worker():
    """Worker function that runs in a separate thread to handle optimization tasks."""
    global optimization_running, optimization_results

    while True:
        try:
            # Get task from queue with timeout
            task = optimization_queue.get(timeout=1)
            optimization_running = True

            # Extract task parameters
            pair = task['pair']
            lookback_days = task['lookback_days']

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=lookback_days)

            # Run optimization
            try:
                result = optimize_strategy(
                    pair,
                    start_date.strftime('%Y-%m-%d'),
                    end_date.strftime('%Y-%m-%d')
                )

                # Store result with timestamp
                optimization_results.append({
                    'timestamp': datetime.now().isoformat(),
                    'pair': pair,
                    'result': result
                })

                # Keep only last 100 results
                if len(optimization_results) > 100:
                    optimization_results = optimization_results[-100:]

            except Exception as e:
                print(f"Error in optimization task: {e}")
                traceback.print_exc()

            optimization_queue.task_done()

        except queue.Empty:
            # No tasks available
            optimization_running = False
            continue
        except Exception as e:
            print(f"Error in optimization worker: {e}")
            traceback.print_exc()
            optimization_running = False
            continue

@app.route('/auto_optimize/start', methods=['POST'])
def start_auto_optimization():
    """Start automatic background optimization."""
    global optimization_thread

    data = request.json
    pair = data.get('pair', 'BTC-USD')
    lookback_days = int(data.get('lookback_days', 30))

    if optimization_thread is None or not optimization_thread.is_alive():
        # Start new optimization thread
        optimization_thread = threading.Thread(
            target=background_optimization_worker,
            daemon=True
        )
        optimization_thread.start()

    # Add task to queue
    optimization_queue.put({
        'pair': pair,
        'lookback_days': lookback_days
    })

    return jsonify({
        'status': 'success',
        'message': f'Started automatic optimization for {pair} with {lookback_days} days lookback'
    })

@app.route('/auto_optimize/status')
def get_optimization_status():
    """Get the current status of the optimization process."""
    return jsonify({
        'is_running': optimization_running,
        'queue_size': optimization_queue.qsize(),
        'latest_results': optimization_results[-10:] if optimization_results else []
    })

@app.route('/auto_optimize/stop', methods=['POST'])
def stop_auto_optimization():
    """Stop the automatic optimization process."""
    global optimization_thread, optimization_running

    # Clear the queue
    while not optimization_queue.empty():
        try:
            optimization_queue.get_nowait()
        except queue.Empty:
            break

    optimization_running = False

    return jsonify({
        'status': 'success',
        'message': 'Stopped automatic optimization'
    })



@app.route('/strategies/delete/<strategy_id>', methods=['DELETE'])
@app.route('/delete_python_strategy/<strategy_id>', methods=['DELETE'])  # Add this route to support the JavaScript code
def delete_strategy_route(strategy_id):
    """Deletes a strategy file (works for both Pine Script and Python strategies)."""
    print(f"Deleting strategy: {strategy_id}")
    # Check if it's a Python strategy
    from strategies import STRATEGY_PARAMS

    # Define the core strategies that cannot be deleted
    core_strategies = ["simple_sma_cross", "rsi_strategy", "macd_cross", "bollinger_bands", "test"]

    if strategy_id in core_strategies:
        # It's a core Python strategy that cannot be deleted
        print(f"Cannot delete core Python strategy: {strategy_id}")
        return jsonify({'status': 'error', 'message': 'Cannot delete core Python strategy.'}), 400

    # Check if it's a Python strategy that can be deleted
    if strategy_id in STRATEGY_PARAMS and strategy_id not in core_strategies:
        # It's a Python strategy that can be deleted
        # We need to modify the strategies.py file to remove the strategy
        import inspect
        import strategies
        import re
        import importlib

        # Get the path to the strategies.py file
        strategies_file = inspect.getfile(strategies)
        print(f"Strategies file path: {strategies_file}")

        try:
            # Read the current content of the file
            with open(strategies_file, 'r') as f:
                content = f.read()

            # Find and remove the strategy function - use a more flexible pattern
            # This pattern matches any function definition with the given name
            # and captures everything until the next function definition or the end of the file
            pattern = f"def {re.escape(strategy_id)}\\s*\\([^)]*\\):.*?(?=\\n\\s*def\\s+|\\Z)"
            match = re.search(pattern, content, re.DOTALL)

            if match:
                print(f"Found strategy function for {strategy_id}")
                new_content = content.replace(match.group(0), '')

                # Find and remove the STRATEGY_PARAMS and STRATEGY_DESCRIPTIONS entries
                # Use more flexible patterns that can match multiline entries
                params_pattern = f"STRATEGY_PARAMS\\[[\'\"]?{re.escape(strategy_id)}[\'\"]?\\]\\s*=\\s*{{[^}}]*}}\\s*"
                desc_pattern = f"STRATEGY_DESCRIPTIONS\\[[\'\"]?{re.escape(strategy_id)}[\'\"]?\\]\\s*=\\s*[\'\"][^\'\"]*[\'\"]\\s*"

                new_content = re.sub(params_pattern, '', new_content)
                new_content = re.sub(desc_pattern, '', new_content)

                # Write the new content back to the file
                with open(strategies_file, 'w') as f:
                    f.write(new_content)

                # Reload the strategies module to reflect the changes
                importlib.reload(strategies)

                print(f"Python strategy {strategy_id} deleted successfully")
                return jsonify({'status': 'success', 'message': f'Python strategy {strategy_id} deleted successfully.'})
            else:
                print(f"Strategy function for {strategy_id} not found, removing parameters only")
                # If we can't find the function, just remove the PARAMS and DESCRIPTIONS
                params_pattern = f"STRATEGY_PARAMS\\[[\'\"]?{re.escape(strategy_id)}[\'\"]?\\]\\s*=\\s*{{[^}}]*}}\\s*"
                desc_pattern = f"STRATEGY_DESCRIPTIONS\\[[\'\"]?{re.escape(strategy_id)}[\'\"]?\\]\\s*=\\s*[\'\"][^\'\"]*[\'\"]\\s*"

                new_content = re.sub(params_pattern, '', content)
                new_content = re.sub(desc_pattern, '', new_content)

                # Write the new content back to the file
                with open(strategies_file, 'w') as f:
                    f.write(new_content)

                # Reload the strategies module to reflect the changes
                importlib.reload(strategies)

                print(f"Python strategy {strategy_id} parameters deleted successfully")
                return jsonify({'status': 'success', 'message': f'Python strategy {strategy_id} parameters deleted successfully.'})
        except Exception as e:
            print(f"Error deleting Python strategy {strategy_id}: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'status': 'error', 'message': f'Failed to delete Python strategy: {str(e)}'}), 500

    # Otherwise, treat it as a Pine Script strategy
    filename = sanitize_filename(strategy_id) # Sanitize just in case
    filepath = os.path.join(STRATEGIES_DIR, f"{filename}.pine") # Delete .pine file
    print(f"Looking for Pine Script strategy at: {filepath}")

    if not os.path.exists(filepath):
        print(f"Strategy file {filename}.pine not found")
        # If it's not a Pine Script strategy and not in STRATEGY_PARAMS, return an error
        if strategy_id not in STRATEGY_PARAMS:
            print(f"Strategy {strategy_id} not found in STRATEGY_PARAMS or as a Pine Script file")
            return jsonify({'status': 'error', 'message': f'Strategy {strategy_id} not found.'}), 404
        return jsonify({'status': 'error', 'message': f'Strategy file {filename}.pine not found.'}), 404

    try:
        os.remove(filepath)
        print(f"Pine Script strategy {strategy_id} deleted successfully")
        return jsonify({'status': 'success', 'message': f'Strategy {strategy_id} deleted successfully.'})
    except Exception as e:
        print(f"Error deleting strategy file {filepath}: {e}")
        return jsonify({'status': 'error', 'message': 'Failed to delete strategy.'}), 500

@app.route('/delete_all_python_strategies', methods=['DELETE'])
def delete_all_python_strategies():
    """Deletes all Python strategies and resets to default."""
    print("Deleting all Python strategies and resetting to default")
    from strategies import STRATEGY_PARAMS
    import inspect
    import strategies
    import re
    import importlib
    import os
    import shutil

    # Get the path to the strategies.py file
    strategies_file = inspect.getfile(strategies)
    print(f"Strategies file path: {strategies_file}")

    try:
        # Check if we have a backup of the original file
        backup_file = strategies_file + ".original"
        if not os.path.exists(backup_file):
            # Create a backup of the original file if it doesn't exist
            shutil.copy2(strategies_file, backup_file)
            print(f"Created backup of original strategies file at {backup_file}")

        # Reset the file to its original state with only core strategies
        original_content = """\"\"\"
Trading strategies module.
\"\"\"
import os
import inspect
import importlib.util
import sys

# Dictionary to store strategy parameters
STRATEGY_PARAMS = {}

# Dictionary to store strategy descriptions
STRATEGY_DESCRIPTIONS = {}

# Simple SMA Cross strategy has been removed

# RSI strategy has been removed

# MACD strategy has been removed

# Bollinger Bands strategy has been removed

# Test strategy has been removed
"""

        # Write the original content back to the file
        with open(strategies_file, 'w') as f:
            f.write(original_content)

        print("Reset strategies file to original state with only core strategies")

        # Reload the strategies module to reflect the changes
        importlib.reload(strategies)

        # Reset the Strategy-bot results
        reset_strategy_bot_results()

        print(f"All Python strategies reset to default successfully")
        return jsonify({'status': 'success', 'message': f'All Python strategies reset to default successfully.'}), 200
    except Exception as e:
        print(f"Error deleting Python strategies: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'status': 'error', 'message': f'Failed to delete Python strategies: {str(e)}'}), 500

@app.route('/test_python_strategy', methods=['POST'])
def test_python_strategy_route():
    """Tests a Python strategy."""
    print("Received test_python_strategy request")
    data = request.json
    if not data:
        print("No data provided")
        return jsonify({'error': 'No data provided'}), 400

    print(f"Request data: {data}")
    strategy_id = data.get('strategy_id')
    code = data.get('code')

    if not strategy_id:
        print("No strategy ID provided")
        return jsonify({'error': 'No strategy ID provided'}), 400

    if not code:
        print("No code provided")
        return jsonify({'error': 'No code provided'}), 400

    # Save the strategy code to a temporary file
    import tempfile
    import os

    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(suffix='.py', delete=False) as temp_file:
            temp_file.write(code.encode('utf-8'))
            temp_path = temp_file.name
            print(f"Saved code to temporary file: {temp_path}")

        # Validate the code by trying to import it
        import importlib.util
        import sys

        print(f"Importing module from {temp_path}")
        spec = importlib.util.spec_from_file_location(f"temp_{strategy_id}", temp_path)
        module = importlib.util.module_from_spec(spec)
        sys.modules[f"temp_{strategy_id}"] = module
        spec.loader.exec_module(module)

        # Check if the strategy function exists in the module
        if not hasattr(module, strategy_id):
            print(f"Strategy function {strategy_id} not found in the code")
            os.unlink(temp_path)  # Clean up
            return jsonify({'error': f'Strategy function {strategy_id} not found in the code'}), 400

        # If we get here, the code is valid
        # Clean up
        os.unlink(temp_path)
        print(f"Strategy {strategy_id} test passed successfully")

        return jsonify({'message': f'Strategy {strategy_id} test passed successfully'})

    except Exception as e:
        print(f"Error testing strategy: {str(e)}")
        traceback.print_exc()
        # Clean up if the temp file exists
        if 'temp_path' in locals():
            try:
                os.unlink(temp_path)
            except:
                pass

        return jsonify({'error': f'Error testing strategy: {str(e)}'}), 500

if __name__ == '__main__':
    import time # Add time import if not already present
    import traceback # Add traceback import
    load_config()
    # Note: Setting debug=True is convenient for development but should be False in production.
    # Use host='0.0.0.0' to make the server accessible on your network.
    app.run(debug=True, host='0.0.0.0', port=5005) # Using port 5005 to avoid conflicts
