import random
import time
import json
import os
import importlib
from datetime import datetime
from typing import Dict, <PERSON>, <PERSON>, Tu<PERSON>, Optional
import pandas as pd
import numpy as np
import traceback
import threading
import queue
from hyperliquid_api import fetch_historical_data
from strategy_developer import StrategyGene, StrategyChromosome, StrategyOptimizer
from strategies import STRATEGY_PARAMS, STRATEGY_DESCRIPTIONS
from new_minimal_backtester import run_backtest

# Global variables for tracking the Strategy-bot status
strategy_bot_thread = None
strategy_bot_queue = queue.Queue()
strategy_bot_running = False
strategy_bot_progress = 0
strategy_bot_status = "Niet actief"
strategy_bot_results = []
strategy_bot_current_performance = 0.0
strategy_bot_current_win_ratio = 0.0
strategy_bot_current_trades = 0
strategy_bot_iteration = 0
strategy_bot_total_iterations = 0
strategy_bot_start_time = None
strategy_bot_parameter_history = []

# Global variable declaration is not needed here as it's already at module level

# Path to store Strategy-bot results
RESULTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategy_bot_results')
os.makedirs(RESULTS_DIR, exist_ok=True)

# Path to store generated strategies
STRATEGIES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'strategies')
os.makedirs(STRATEGIES_DIR, exist_ok=True)

class StrategyBotOptimizer:
    """
    Class for optimizing trading strategies.
    """
    def __init__(self,
                 pair: str,
                 start_date: str,
                 end_date: str,
                 iterations: int = 20,
                 generations: int = 10,
                 population_size: int = 20,
                 strategy: str = None):
        self.pair = pair
        self.start_date = start_date
        self.end_date = end_date
        self.iterations = iterations
        self.generations = generations
        self.population_size = population_size
        self.strategy = strategy

        # List of available strategies
        self.available_strategies = list(STRATEGY_PARAMS.keys())

        # Load historical data
        self.data = self._load_historical_data()

    def _load_historical_data(self) -> pd.DataFrame:
        """Load historical data for the specified pair and date range."""
        try:
            start_time_ms = int(datetime.strptime(self.start_date, '%Y-%m-%d').timestamp() * 1000)
            end_time_ms = int(datetime.strptime(self.end_date, '%Y-%m-%d').timestamp() * 1000)
            data = fetch_historical_data(self.pair, start_time_ms, end_time_ms)

            if data is None or data.empty:
                print(f"No historical data found for {self.pair} from {self.start_date} to {self.end_date}")
                return pd.DataFrame()

            return data
        except Exception as e:
            print(f"Error loading historical data: {e}")
            traceback.print_exc()
            return pd.DataFrame()

    def _evaluate_strategy(self, strategy_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Run backtest for a strategy with specific parameters."""
        try:
            # Import the correct run_backtest function that accepts parameters
            from backtester import run_backtest as run_backtest_with_params

            # Run backtest with parameters
            result = run_backtest_with_params(self.pair, strategy_id, self.start_date, self.end_date, params)

            if 'error' in result:
                print(f"Error in backtest: {result['error']}")
                return {'total_pnl_pct': 0.0, 'win_ratio_pct': 0.0, 'number_of_trades': 0}

            return result
        except Exception as e:
            print(f"Error evaluating strategy: {e}")
            traceback.print_exc()
            return {'total_pnl_pct': 0.0, 'win_ratio_pct': 0.0, 'number_of_trades': 0}

    def _combine_strategies(self, strategies: List[str], weights_buy: Dict[str, float] = None, weights_sell: Dict[str, float] = None) -> Tuple[str, Dict[str, Any]]:
        """
        Combine multiple strategies into a new strategy with priority weights.
        Returns the new strategy ID and its parameters.

        Args:
            strategies: List of strategy IDs to combine
            weights_buy: Dictionary mapping strategy IDs to buy signal weights
            weights_sell: Dictionary mapping strategy IDs to sell signal weights
        """
        # Create a unique ID for the combined strategy
        timestamp = int(time.time())
        strategy_names = "_".join(strategies[:3])  # Use first 3 strategy names in ID
        new_strategy_id = f"multi_{strategy_names}_{timestamp}"

        # Set default weights if not provided
        if weights_buy is None:
            weights_buy = {strategy: 1.0 for strategy in strategies}
        if weights_sell is None:
            weights_sell = {strategy: 1.0 for strategy in strategies}

        # Create strategy parameters dictionary
        strategy_params = {}
        for strategy in strategies:
            strategy_params[strategy] = STRATEGY_PARAMS.get(strategy, {}).copy()

        # Create combined parameters
        combined_params = {
            'strategies': strategies,
            'weights_buy': weights_buy,
            'weights_sell': weights_sell,
            'threshold_buy': 0.5,  # Default threshold
            'threshold_sell': 0.5,  # Default threshold
            'strategy_params': strategy_params
        }

        # Create a description for the combined strategy
        strategy_names_str = ", ".join(strategies)
        description = f"Multi-strategy combining {strategy_names_str} with priority weights"

        # Update STRATEGY_PARAMS and STRATEGY_DESCRIPTIONS
        STRATEGY_PARAMS[new_strategy_id] = combined_params
        STRATEGY_DESCRIPTIONS[new_strategy_id] = description

        print(f"Created new multi-strategy: {new_strategy_id}")
        print(f"Strategies: {strategies}")
        print(f"Buy weights: {weights_buy}")
        print(f"Sell weights: {weights_sell}")

        return new_strategy_id, combined_params

    def _optimize_multi_strategy(self, strategies: List[str]) -> Dict[str, Any]:
        """
        Optimize a multi-strategy by finding the best weights and thresholds.

        Args:
            strategies: List of strategy IDs to combine

        Returns:
            Dictionary with optimization results
        """
        print(f"Optimizing multi-strategy with strategies: {strategies}")

        # Define parameter ranges for optimization
        weights_range = [0.1, 0.5, 1.0, 1.5, 2.0]  # Possible weight values
        threshold_range = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7]  # Possible threshold values

        best_performance = -float('inf')
        best_weights_buy = {}
        best_weights_sell = {}
        best_threshold_buy = 0.5
        best_threshold_sell = 0.5

        # Start with equal weights
        initial_weights = {strategy: 1.0 for strategy in strategies}

        # Try different weight combinations (simplified approach)
        for strategy in strategies:
            for weight_buy in weights_range:
                for weight_sell in weights_range:
                    for threshold_buy in threshold_range:
                        for threshold_sell in threshold_range:
                            # Create weight dictionaries
                            weights_buy = initial_weights.copy()
                            weights_sell = initial_weights.copy()

                            # Modify weight for current strategy
                            weights_buy[strategy] = weight_buy
                            weights_sell[strategy] = weight_sell

                            # Create multi-strategy
                            multi_strategy_id, multi_params = self._combine_strategies(
                                strategies, weights_buy, weights_sell
                            )

                            # Update thresholds
                            multi_params['threshold_buy'] = threshold_buy
                            multi_params['threshold_sell'] = threshold_sell

                            # Evaluate the strategy
                            result = self._evaluate_strategy('multi_strategy', multi_params)
                            performance = result.get('total_pnl_pct', 0.0)

                            print(f"Testing weights: Buy={weights_buy}, Sell={weights_sell}, Thresholds: Buy={threshold_buy}, Sell={threshold_sell}, Performance: {performance:.2f}%")

                            # Update best if improved
                            if performance > best_performance:
                                best_performance = performance
                                best_weights_buy = weights_buy.copy()
                                best_weights_sell = weights_sell.copy()
                                best_threshold_buy = threshold_buy
                                best_threshold_sell = threshold_sell

                                print(f"New best performance: {best_performance:.2f}%")
                                print(f"Best weights buy: {best_weights_buy}")
                                print(f"Best weights sell: {best_weights_sell}")
                                print(f"Best thresholds: Buy={best_threshold_buy}, Sell={best_threshold_sell}")

        # Create final multi-strategy with best parameters
        multi_strategy_id, multi_params = self._combine_strategies(
            strategies, best_weights_buy, best_weights_sell
        )

        # Update thresholds
        multi_params['threshold_buy'] = best_threshold_buy
        multi_params['threshold_sell'] = best_threshold_sell

        # Final evaluation
        final_result = self._evaluate_strategy('multi_strategy', multi_params)

        return {
            'strategy_id': multi_strategy_id,
            'parameters': multi_params,
            'performance': best_performance,
            'win_ratio': final_result.get('win_ratio_pct', 0.0),
            'number_of_trades': final_result.get('number_of_trades', 0)
        }

    def _save_strategy(self, strategy_id: str, code: str) -> bool:
        """Save a strategy to a file."""
        try:
            # Save to strategies directory
            strategy_file = os.path.join(STRATEGIES_DIR, f"{strategy_id}.py")
            with open(strategy_file, 'w') as f:
                f.write(code)

            return True
        except Exception as e:
            print(f"Error saving strategy: {e}")
            traceback.print_exc()
            return False

    def _save_result(self, strategy_id: str, parameters: Dict[str, Any], performance: float, win_ratio: float = None, number_of_trades: int = None) -> None:
        """Save a Strategy-bot result."""
        try:
            # Create a unique version number
            version = len(strategy_bot_results) + 1

            # Ensure performance is a float
            performance = float(performance)

            # Debug log
            print(f"Saving result for {strategy_id} with performance: {performance} (type: {type(performance)})")

            # Create result object
            result = {
                'version': version,
                'timestamp': datetime.now().isoformat(),
                'strategy_id': strategy_id,
                'parameters': parameters,
                'performance': performance,
                'pair': self.pair,
                'start_date': self.start_date,
                'end_date': self.end_date
            }

            # Add win ratio and number of trades if provided
            if win_ratio is not None:
                result['win_ratio'] = float(win_ratio)
            if number_of_trades is not None:
                result['number_of_trades'] = int(number_of_trades)
                # Also add as num_trades for compatibility with the UI
                result['num_trades'] = int(number_of_trades)

            # Print the result for debugging
            print(f"Saving result: performance={performance}, win_ratio={win_ratio}, trades={number_of_trades}")

            # Add to global results list
            strategy_bot_results.append(result)

            # Save to file
            result_file = os.path.join(RESULTS_DIR, f"result_{version}.json")
            with open(result_file, 'w') as f:
                json.dump(result, f, indent=4)

        except Exception as e:
            print(f"Error saving result: {e}")
            traceback.print_exc()

    def _save_permanent_strategy(self, strategy_result: Dict[str, Any]) -> None:
        """Save the best strategy as a permanent strategy in the system.

        This function creates a new strategy in the system based on the optimized parameters
        from the Strategy-bot. The strategy will be available for future use in backtesting
        and live trading.
        """
        try:
            # Generate a unique name for the strategy
            timestamp = int(time.time())
            strategy_name = f"Geoptimaliseerde Strategie {timestamp}"
            strategy_id = f"optimized_strategy_{timestamp}"

            # Get the parameters from the result
            params = strategy_result['parameters']

            # Add to global results list
            global strategy_bot_results

            # Create a permanent result entry
            permanent_result = {
                'version': len(strategy_bot_results) + 1,
                'timestamp': datetime.now().isoformat(),
                'strategy_id': strategy_id,
                'parameters': params,
                'performance': strategy_result.get('performance', 0.0),
                'pair': self.pair,
                'start_date': self.start_date,
                'end_date': self.end_date,
                'win_ratio': strategy_result.get('win_ratio', 0.0),
                'number_of_trades': strategy_result.get('number_of_trades', 0),
                'num_trades': strategy_result.get('number_of_trades', 0),
                'is_permanent': True,
                'name': strategy_name
            }

            strategy_bot_results.append(permanent_result)

            # Save to file
            result_file = os.path.join(RESULTS_DIR, f"result_permanent_{timestamp}.json")
            with open(result_file, 'w') as f:
                json.dump(permanent_result, f, indent=4)

            print(f"Successfully saved permanent strategy: {strategy_id}")

        except Exception as e:
            print(f"Error saving permanent strategy: {e}")
            traceback.print_exc()

    def _generate_multi_strategy_code(self, strategy_id, strategies_list, weights_buy, weights_sell,
                                     threshold_buy, threshold_sell, strategy_params):
        """Generate Python code for a multi-strategy based on the optimized parameters."""

        # Start with the function definition
        code = f"def {strategy_id}(data):\n"
        code += f'    """Automatisch geoptimaliseerde multi-strategie.\n'
        code += f'    \n'
        code += f'    Deze strategie combineert de volgende strategieën met geoptimaliseerde gewichten:\n'

        # Add each strategy to the docstring
        for strategy in strategies_list:
            code += f'    - {strategy}\n'

        code += f'    """\n'
        code += f'    import pandas as pd\n'
        code += f'    import numpy as np\n'
        code += f'    from strategies import {", ".join(strategies_list)}\n\n'

        # Initialize signals
        code += f'    # Initialize weighted signals\n'
        code += f'    weighted_buy_signals = pd.Series(0.0, index=data.index)\n'
        code += f'    weighted_sell_signals = pd.Series(0.0, index=data.index)\n\n'

        # Add code to call each strategy with its parameters
        for strategy in strategies_list:
            code += f'    # Get signals from {strategy}\n'

            # Get parameters for this strategy
            params = strategy_params.get(strategy, {})
            param_str = ", ".join([f"{k}={v}" for k, v in params.items()])

            code += f'    {strategy}_buy, {strategy}_sell = {strategy}(data, {param_str})\n'

            # Add weighted signals
            weight_buy = weights_buy.get(strategy, 1.0)
            weight_sell = weights_sell.get(strategy, 1.0)

            code += f'    weighted_buy_signals += {strategy}_buy.astype(float) * {weight_buy}\n'
            code += f'    weighted_sell_signals += {strategy}_sell.astype(float) * {weight_sell}\n\n'

        # Normalize and apply thresholds
        code += f'    # Apply thresholds to generate final signals\n'
        code += f'    final_buy_signals = weighted_buy_signals > {threshold_buy}\n'
        code += f'    final_sell_signals = weighted_sell_signals > {threshold_sell}\n\n'

        # Return the signals
        code += f'    return final_buy_signals, final_sell_signals\n'

        return code

    def run(self) -> List[Dict[str, Any]]:
        """
        Run the Strategy-bot to optimize the parameters of the master strategy.
        Returns a list of results.
        """
        global strategy_bot_progress, strategy_bot_status
        global strategy_bot_current_performance, strategy_bot_current_win_ratio, strategy_bot_current_trades
        global strategy_bot_iteration, strategy_bot_total_iterations, strategy_bot_start_time, strategy_bot_parameter_history

        results = []

        try:
            # Initialize status and progress
            strategy_bot_status = "Bezig met initialisatie"
            strategy_bot_progress = 5
            print(f"Initial progress: {strategy_bot_progress}%")

            # Set total iterations and reset current iteration
            strategy_bot_total_iterations = self.iterations
            strategy_bot_iteration = 0

            # Record start time
            strategy_bot_start_time = time.time()

            # Clear parameter history
            strategy_bot_parameter_history = []

            # Get the master strategy parameters
            master_params = STRATEGY_PARAMS.get('master_strategy', {}).copy()
            print(f"Starting with master strategy parameters: {master_params}")

            # Define parameter ranges for optimization
            param_ranges = {
                'threshold_buy': [0.1, 0.2, 0.3, 0.4, 0.5],
                'threshold_sell': [0.1, 0.2, 0.3, 0.4, 0.5],
                'weight_sma_buy': [0.5, 1.0, 1.5, 2.0],
                'weight_sma_sell': [0.5, 1.0, 1.5, 2.0],
                'weight_rsi_buy': [0.5, 1.0, 1.5, 2.0],
                'weight_rsi_sell': [0.5, 1.0, 1.5, 2.0],
                'weight_macd_buy': [0.5, 1.0, 1.5, 2.0],
                'weight_macd_sell': [0.5, 1.0, 1.5, 2.0],
                'weight_bollinger_buy': [0.5, 1.0, 1.5, 2.0],
                'weight_bollinger_sell': [0.5, 1.0, 1.5, 2.0],
                'sma_short_window': [5, 10, 15, 20],
                'sma_long_window': [20, 30, 40, 50],
                'rsi_period': [7, 14, 21],
                'rsi_oversold': [20, 30, 40],
                'rsi_overbought': [60, 70, 80],
                'macd_fast_period': [8, 12, 16],
                'macd_slow_period': [21, 26, 30],
                'macd_signal_period': [7, 9, 11],
                'bollinger_window': [15, 20, 25],
                'bollinger_num_std': [1.5, 2.0, 2.5],
                'take_profit': [3.0, 5.0, 7.0],
                'stop_loss': [1.0, 2.0, 3.0]
            }

            # Initialize best parameters and performance
            best_params = master_params.copy()
            best_performance = -float('inf')
            best_win_ratio = 0.0
            best_trades = 0

            # Optimize parameters
            for iteration in range(self.iterations):
                if not strategy_bot_running:
                    break

                # Update iteration counter
                strategy_bot_iteration = iteration + 1

                strategy_bot_status = f"Iteratie {strategy_bot_iteration}/{self.iterations}: Optimaliseren van parameters"
                print(f"Iteration {strategy_bot_iteration}/{self.iterations}")

                # Update progress
                strategy_bot_progress = 5 + int(strategy_bot_iteration * 90 / self.iterations)
                print(f"Progress updated to: {strategy_bot_progress}%")

                # Create a new set of parameters by randomly selecting from the ranges
                # In the first iteration, use the default parameters
                if iteration == 0:
                    current_params = master_params.copy()
                else:
                    current_params = best_params.copy()

                    # Randomly modify some parameters
                    for param, values in param_ranges.items():
                        if param in current_params and random.random() < 0.3:  # 30% chance to change each parameter
                            current_params[param] = random.choice(values)

                # Ensure use_* parameters are preserved
                for param in ['use_sma', 'use_rsi', 'use_macd', 'use_bollinger']:
                    if param in master_params:
                        current_params[param] = master_params[param]

                print(f"Testing parameters: {current_params}")

                # Run backtest with current parameters
                try:
                    # Import the correct run_backtest function
                    from new_minimal_backtester import run_backtest

                    # Run backtest with parameters
                    result = run_backtest(self.pair, 'master_strategy', self.start_date, self.end_date, current_params)

                    if 'error' in result:
                        print(f"Error in backtest: {result['error']}")
                        performance = 0.0
                        win_ratio = 0.0
                        num_trades = 0
                    else:
                        performance = result.get('total_pnl_pct', 0.0)
                        win_ratio = result.get('win_ratio_pct', 0.0)
                        num_trades = result.get('number_of_trades', 0)

                    print(f"Performance: {performance:.2f}%, Win Ratio: {win_ratio:.2f}%, Trades: {num_trades}")

                    # Update current performance metrics for real-time display
                    strategy_bot_current_performance = performance
                    strategy_bot_current_win_ratio = win_ratio
                    strategy_bot_current_trades = num_trades

                    # Add to parameter history
                    strategy_bot_parameter_history.append({
                        'iteration': strategy_bot_iteration,
                        'parameters': current_params.copy(),
                        'performance': performance,
                        'win_ratio': win_ratio,
                        'trades': num_trades,
                        'timestamp': time.time()
                    })

                    # Update best if improved
                    if performance > best_performance:
                        best_performance = performance
                        best_params = current_params.copy()
                        best_win_ratio = win_ratio
                        best_trades = num_trades

                        print(f"New best performance: {best_performance:.2f}%")
                        print(f"Best parameters: {best_params}")

                        # Create a unique ID for this version
                        timestamp = int(time.time())
                        version_id = f"master_strategy_v{iteration+1}_{timestamp}"

                        # Save the result
                        self._save_result(
                            version_id,
                            best_params,
                            best_performance,
                            best_win_ratio,
                            best_trades
                        )

                        # Add to results
                        results.append({
                            'strategy_id': version_id,
                            'parameters': best_params.copy(),
                            'performance': best_performance,
                            'win_ratio': best_win_ratio,
                            'number_of_trades': best_trades,
                            'timestamp': datetime.now().isoformat()
                        })

                except Exception as e:
                    print(f"Error evaluating parameters: {e}")
                    traceback.print_exc()

            # Sort results by performance
            results.sort(key=lambda x: x.get('performance', 0.0), reverse=True)

            # Final optimization phase
            if results and strategy_bot_running:
                strategy_bot_status = "Fase 2: Finetuning van de beste parameters"
                strategy_bot_progress = 95
                print("Phase 2: Fine-tuning the best parameters")

                # Get the best result
                best_result = results[0]
                print(f"Best result: {best_result['strategy_id']} with performance: {best_result['performance']:.2f}%")

                # Create a final optimized strategy
                timestamp = int(time.time())
                final_strategy_id = f"master_strategy_optimized_{timestamp}"

                # Create the final strategy result
                final_strategy_result = {
                    'strategy_id': final_strategy_id,
                    'parameters': best_result['parameters'].copy(),
                    'performance': best_result['performance'],
                    'win_ratio': best_result['win_ratio'],
                    'number_of_trades': best_result['number_of_trades'],
                    'timestamp': datetime.now().isoformat(),
                    'is_master': True
                }

                # Update current performance metrics for real-time display
                strategy_bot_current_performance = best_result['performance']
                strategy_bot_current_win_ratio = best_result['win_ratio']
                strategy_bot_current_trades = best_result['number_of_trades']

                # Save the result
                self._save_result(
                    final_strategy_result['strategy_id'],
                    final_strategy_result['parameters'],
                    final_strategy_result['performance'],
                    final_strategy_result['win_ratio'],
                    final_strategy_result['number_of_trades']
                )

                # Add to results
                results.append(final_strategy_result)

                print(f"Final optimized strategy: {final_strategy_id} performance: {final_strategy_result['performance']:.2f}%")

                # Save the best strategy as a permanent strategy in the system
                self._save_permanent_strategy(final_strategy_result)

                # Update the master strategy parameters with the best parameters
                STRATEGY_PARAMS['master_strategy'] = best_result['parameters'].copy()
                print(f"Updated master strategy parameters: {STRATEGY_PARAMS['master_strategy']}")

            strategy_bot_status = "Voltooid"
            strategy_bot_progress = 100

            return results

        except Exception as e:
            print(f"Error running Strategy-bot: {e}")
            traceback.print_exc()
            strategy_bot_status = "Fout"
            return results

def strategy_bot_worker():
    """Background worker for the Strategy-bot."""
    global strategy_bot_running, strategy_bot_progress, strategy_bot_status

    while strategy_bot_running:
        try:
            # Get task from queue with timeout
            task = strategy_bot_queue.get(timeout=1)

            # Extract task parameters
            pair = task.get('pair', 'BTC-USD')
            strategy = task.get('strategy')
            iterations = task.get('iterations', 20)
            generations = task.get('generations', 10)
            population_size = task.get('population_size', 20)

            # Calculate date range (use last 30 days by default)
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - pd.Timedelta(days=30)).strftime('%Y-%m-%d')

            # Create and run the optimizer
            optimizer = StrategyBotOptimizer(
                pair=pair,
                start_date=start_date,
                end_date=end_date,
                iterations=iterations,
                generations=generations,
                population_size=population_size,
                strategy=strategy
            )

            # Run the optimizer
            optimizer.run()

        except queue.Empty:
            # No tasks in queue, just continue
            pass
        except Exception as e:
            print(f"Error in Strategy-bot worker: {e}")
            traceback.print_exc()
            strategy_bot_status = "Fout"

    strategy_bot_status = "Gestopt"
    strategy_bot_progress = 0

def start_strategy_bot(pair: str, strategy: str = 'master_strategy', iterations: int = 20, generations: int = 10, population_size: int = 20) -> Dict[str, Any]:
    """Start the Strategy-bot."""
    global strategy_bot_thread, strategy_bot_running, strategy_bot_progress, strategy_bot_status

    # Check if already running
    if strategy_bot_running and strategy_bot_thread and strategy_bot_thread.is_alive():
        return {'status': 'error', 'message': 'Strategy-bot is already running'}

    # Set running flag
    strategy_bot_running = True
    strategy_bot_progress = 10
    strategy_bot_status = "Bezig"

    # Start worker thread
    strategy_bot_thread = threading.Thread(
        target=strategy_bot_worker,
        daemon=True
    )
    strategy_bot_thread.start()

    # Add task to queue
    strategy_bot_queue.put({
        'pair': pair,
        'strategy': strategy,
        'iterations': iterations,
        'generations': generations,
        'population_size': population_size
    })

    return {'status': 'success', 'message': f'Strategy-bot started for {pair} with strategy {strategy}'}

def stop_strategy_bot() -> Dict[str, Any]:
    """Stop the Strategy-bot."""
    global strategy_bot_running, strategy_bot_progress, strategy_bot_status
    global strategy_bot_current_performance, strategy_bot_current_win_ratio, strategy_bot_current_trades
    global strategy_bot_iteration, strategy_bot_total_iterations, strategy_bot_start_time, strategy_bot_parameter_history

    # Set running flag to False
    strategy_bot_running = False
    strategy_bot_status = "Gestopt"

    # Calculate elapsed time
    elapsed_time = 0
    if strategy_bot_start_time:
        elapsed_time = int(time.time() - strategy_bot_start_time)

    # Save the current status to file
    status = {
        'status': strategy_bot_status,
        'progress': strategy_bot_progress,
        'running': strategy_bot_running,
        'current_performance': strategy_bot_current_performance,
        'current_win_ratio': strategy_bot_current_win_ratio,
        'current_trades': strategy_bot_current_trades,
        'iteration': strategy_bot_iteration,
        'total_iterations': strategy_bot_total_iterations,
        'elapsed_time': elapsed_time,
        'parameter_history': strategy_bot_parameter_history[-5:] if strategy_bot_parameter_history else [],
        'results': strategy_bot_results[-10:] if strategy_bot_results else []
    }

    try:
        with open('strategy_bot_status.json', 'w') as f:
            json.dump(status, f)
    except Exception as e:
        print(f"Error saving strategy bot status: {e}")

    # Clear the queue
    while not strategy_bot_queue.empty():
        try:
            strategy_bot_queue.get_nowait()
        except queue.Empty:
            break

    return {'status': 'success', 'message': 'Strategy-bot stopped'}

def get_strategy_bot_status() -> Dict[str, Any]:
    """Get the current status of the Strategy-bot."""
    global strategy_bot_running, strategy_bot_progress, strategy_bot_status, strategy_bot_results
    global strategy_bot_current_performance, strategy_bot_current_win_ratio, strategy_bot_current_trades
    global strategy_bot_iteration, strategy_bot_total_iterations, strategy_bot_start_time, strategy_bot_parameter_history

    # Print debug information
    print(f"Strategy-bot status: {strategy_bot_status}, progress: {strategy_bot_progress}, running: {strategy_bot_running}")

    # Ensure progress is an integer
    try:
        progress = int(strategy_bot_progress)
    except (ValueError, TypeError):
        progress = 0

    # Try to load status from file if not running
    if not strategy_bot_running and progress == 0 and strategy_bot_status == "Niet actief":
        try:
            if os.path.exists('strategy_bot_status.json'):
                with open('strategy_bot_status.json', 'r') as f:
                    saved_status = json.load(f)
                    # Only use saved status if it shows the bot was running or completed
                    if saved_status.get('status') not in ["Niet actief"]:
                        print(f"Loading saved status from file: {saved_status}")

                        # Update global variables from saved status
                        strategy_bot_status = saved_status.get('status', strategy_bot_status)
                        strategy_bot_progress = saved_status.get('progress', strategy_bot_progress)
                        strategy_bot_running = saved_status.get('running', strategy_bot_running)

                        # Update performance metrics if available
                        strategy_bot_current_performance = saved_status.get('current_performance', 0.0)
                        strategy_bot_current_win_ratio = saved_status.get('current_win_ratio', 0.0)
                        strategy_bot_current_trades = saved_status.get('current_trades', 0)

                        # Update iteration information if available
                        strategy_bot_iteration = saved_status.get('iteration', 0)
                        strategy_bot_total_iterations = saved_status.get('total_iterations', 0)

                        # Update parameter history if available
                        if 'parameter_history' in saved_status:
                            strategy_bot_parameter_history = saved_status.get('parameter_history', [])

                        # Update results if available
                        if 'results' in saved_status and saved_status['results']:
                            # Replace the entire results list with the saved results
                            strategy_bot_results = saved_status['results']

                        return saved_status
        except Exception as e:
            print(f"Error loading saved status: {e}")

    # Make sure we return the best results
    if strategy_bot_results:
        # Sort results by performance (highest first)
        strategy_bot_results.sort(key=lambda x: x.get('performance', 0), reverse=True)

    # Print debug information about the current performance metrics
    print(f"Returning status with performance: {strategy_bot_current_performance}, win ratio: {strategy_bot_current_win_ratio}, trades: {strategy_bot_current_trades}")

    # Determine performance values to return
    # Return None if running but progress is very low (likely hasn't calculated first value)
    perf_val = strategy_bot_current_performance if not (strategy_bot_running and progress < 20) else None
    win_ratio_val = strategy_bot_current_win_ratio if not (strategy_bot_running and progress < 20) else None
    trades_val = strategy_bot_current_trades if not (strategy_bot_running and progress < 20) else None

    # Calculate elapsed time and estimated time remaining
    elapsed_time = 0
    estimated_time_remaining = 0

    if strategy_bot_running and strategy_bot_start_time:
        elapsed_time = int(time.time() - strategy_bot_start_time)

        if progress > 0:
            time_per_percent = elapsed_time / progress
            estimated_time_remaining = int(time_per_percent * (100 - progress))

    return {
        'status': strategy_bot_status,
        'progress': progress,
        'running': strategy_bot_running,
        'current_performance': perf_val,
        'current_win_ratio': win_ratio_val,
        'current_trades': trades_val,
        'iteration': strategy_bot_iteration,
        'total_iterations': strategy_bot_total_iterations,
        'elapsed_time': elapsed_time,
        'estimated_time_remaining': estimated_time_remaining,
        'parameter_history': strategy_bot_parameter_history[-5:] if strategy_bot_parameter_history else [],
        'results': strategy_bot_results[-10:] if strategy_bot_results else []
    }

def get_strategy_bot_results() -> Dict[str, Any]:
    """Get all Strategy-bot results."""
    global strategy_bot_results

    # If no results in memory, try to load from files
    if not strategy_bot_results:
        try:
            # Load results from files
            result_files = [f for f in os.listdir(RESULTS_DIR) if f.startswith('result_') and f.endswith('.json')]

            for file in result_files:
                try:
                    with open(os.path.join(RESULTS_DIR, file), 'r') as f:
                        result = json.load(f)
                        strategy_bot_results.append(result)
                except Exception as e:
                    print(f"Error loading result file {file}: {e}")

            # Sort by version
            strategy_bot_results.sort(key=lambda x: x.get('version', 0))

        except Exception as e:
            print(f"Error loading results: {e}")
            traceback.print_exc()

    return {
        'status': 'success',
        'results': strategy_bot_results
    }

def reset_strategy_bot_results() -> Dict[str, Any]:
    """Reset all Strategy-bot results and delete optimized strategies."""
    global strategy_bot_results, strategy_bot_status, strategy_bot_progress, strategy_bot_running
    global strategy_bot_current_performance, strategy_bot_current_win_ratio, strategy_bot_current_trades

    try:
        # Clear the results list
        strategy_bot_results = []

        # Delete all result files
        result_files = [f for f in os.listdir(RESULTS_DIR) if f.startswith('result_') and f.endswith('.json')]
        for file in result_files:
            try:
                os.remove(os.path.join(RESULTS_DIR, file))
                print(f"Deleted result file: {file}")
            except Exception as e:
                print(f"Error deleting result file {file}: {e}")

        # Reset status
        strategy_bot_status = "Niet actief"
        strategy_bot_progress = 0
        strategy_bot_running = False

        # Update status file
        status = {
            'status': strategy_bot_status,
            'progress': strategy_bot_progress,
            'running': strategy_bot_running,
            'current_performance': 0.0,
            'current_win_ratio': 0.0,
            'current_trades': 0,
            'results': []
        }

        with open('strategy_bot_status.json', 'w') as f:
            json.dump(status, f)

        # Remove optimized strategies from STRATEGY_PARAMS and STRATEGY_DESCRIPTIONS
        strategies_to_remove = []
        for strategy_id in list(STRATEGY_PARAMS.keys()):
            if (strategy_id.startswith('optimized_') or
                strategy_id.startswith('multi_') or
                strategy_id.startswith('master_strategy_')):
                strategies_to_remove.append(strategy_id)

        for strategy_id in strategies_to_remove:
            if strategy_id in STRATEGY_PARAMS:
                del STRATEGY_PARAMS[strategy_id]
                print(f"Removed strategy {strategy_id} from STRATEGY_PARAMS")
            if strategy_id in STRATEGY_DESCRIPTIONS:
                del STRATEGY_DESCRIPTIONS[strategy_id]
                print(f"Removed strategy {strategy_id} from STRATEGY_DESCRIPTIONS")

        # Also remove optimized strategies from strategies.py file
        try:
            import inspect
            import strategies
            import re

            # Get the path to the strategies.py file
            strategies_file = inspect.getfile(strategies)

            # Read the current content of the file
            with open(strategies_file, 'r') as f:
                content = f.read()

            # Find and remove STRATEGY_PARAMS and STRATEGY_DESCRIPTIONS entries for optimized strategies
            for strategy_id in strategies_to_remove:
                # Remove STRATEGY_PARAMS entries
                params_pattern = f"STRATEGY_PARAMS\\['{strategy_id}'\\] = .*?\n"
                content = re.sub(params_pattern, '', content)

                # Remove STRATEGY_DESCRIPTIONS entries
                desc_pattern = f"STRATEGY_DESCRIPTIONS\\['{strategy_id}'\\] = .*?\n"
                content = re.sub(desc_pattern, '', content)

            # Write the updated content back to the file
            with open(strategies_file, 'w') as f:
                f.write(content)

            print(f"Removed optimized strategies from {strategies_file}")
        except Exception as e:
            print(f"Error removing optimized strategies from strategies.py: {e}")
            traceback.print_exc()

        return {
            'status': 'success',
            'message': 'All Strategy-bot results and optimized strategies have been reset'
        }
    except Exception as e:
        print(f"Error resetting results: {e}")
        traceback.print_exc()
        return {
            'status': 'error',
            'message': f'Error resetting results: {str(e)}'
        }

# Function to add test strategies with different performance values
def add_test_strategies():
    """Add test strategies with different performance values for testing display"""
    # Add a strategy with negative performance
    negative_result = {
        'version': 100,
        'timestamp': datetime.now().isoformat(),
        'strategy_id': 'test_negative_performance',
        'parameters': {'param1': 10, 'param2': 20},
        'performance': -5.5,
        'pair': 'BTC-USD',
        'start_date': '2025-03-21',
        'end_date': '2025-04-20',
        'win_ratio': 40.0,
        'number_of_trades': 10,
        'num_trades': 10
    }

    # Add a strategy with positive performance
    positive_result = {
        'version': 101,
        'timestamp': datetime.now().isoformat(),
        'strategy_id': 'test_positive_performance',
        'parameters': {'param1': 15, 'param2': 25},
        'performance': 7.8,
        'pair': 'BTC-USD',
        'start_date': '2025-03-21',
        'end_date': '2025-04-20',
        'win_ratio': 65.0,
        'number_of_trades': 15,
        'num_trades': 15
    }

    # Add a strategy with zero performance
    zero_result = {
        'version': 102,
        'timestamp': datetime.now().isoformat(),
        'strategy_id': 'test_zero_performance',
        'parameters': {'param1': 5, 'param2': 15},
        'performance': 0.0,
        'pair': 'BTC-USD',
        'start_date': '2025-03-21',
        'end_date': '2025-04-20',
        'win_ratio': 50.0,
        'number_of_trades': 8,
        'num_trades': 8
    }

    # Add to global results list
    global strategy_bot_results
    strategy_bot_results.append(negative_result)
    strategy_bot_results.append(positive_result)
    strategy_bot_results.append(zero_result)

    # Save to files
    for result, name in [(negative_result, "negative"), (positive_result, "positive"), (zero_result, "zero")]:
        result_file = os.path.join(RESULTS_DIR, f"result_test_{name}.json")
        with open(result_file, 'w') as f:
            json.dump(result, f, indent=4)

    # Update status file
    status = load_status()
    if 'results' not in status:
        status['results'] = []

    # Add the new results
    status['results'].append(negative_result)
    status['results'].append(positive_result)
    status['results'].append(zero_result)

    # Save the updated status
    save_status(status)

    return [negative_result, positive_result, zero_result]

# Helper function to load status
def load_status():
    """Load the current status from file"""
    try:
        if os.path.exists('strategy_bot_status.json'):
            with open('strategy_bot_status.json', 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Error loading status: {e}")

    return {
        'status': "Niet actief",
        'progress': 0,
        'running': False,
        'current_performance': 0.0,
        'current_win_ratio': 0.0,
        'current_trades': 0,
        'results': []
    }

# Helper function to save status
def save_status(status):
    """Save the current status to file"""
    try:
        with open('strategy_bot_status.json', 'w') as f:
            json.dump(status, f)
    except Exception as e:
        print(f"Error saving status: {e}")

# Load results at module initialization
get_strategy_bot_results()

# Add test strategies with different performance values
add_test_strategies()
